import { http } from '@/utils/http/axios';

//获取table
export function getTemplateList(params) {
  return http.request({
    url: '/business/template/list',
    method: 'get',
    params,
  });
}

export function getAuditTemplateList(params) {
  return http.request({
    url: '/business/template/audit/list',
    method: 'get',
    params,
  });
}

export function getOperateTemplateList(params) {
  return http.request({
    url: '/business/template/sync/list',
    method: 'get',
    params,
  });
}

//获取模板类型
export function getTemplateTypeList() {
  return http.request({
    url: '/business/template/type/list',
    method: 'get',
  });
}

// 新增模板
export function addTemplate(data) {
  return http.request({
    url: '/business/template/create',
    method: 'post',
    params: data,
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// 删除模板
export function deleteTemplate(id) {
  return http.request({
    url: `/business/template/${id}/delete`,
    method: 'post',
  });
}

// 批量删除模板
export function batchDeleteTemplate(params) {
  return http.request({
    url: '/business/template/batch/deleteBatch',
    method: 'post',
    params,
  });
}

// 获取模板详情
export function getTemplateDetail(id) {
  return http.request({
    url: `/business/template/${id}`,
    method: 'get',
  });
}

// 审核获取模板详情
export function getAuditTemplateDetail(id) {
  return http.request({
    url: `/business/template/${id}/audit`,
    method: 'get',
  });
}

// 编辑模板
export function editTemplate(id, params) {
  return http.request({
    url: `/business/template/${id}`,
    method: 'POST',
    params,
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// 模板申请发布
export function applyTemplatePublish(id) {
  return http.request({
    url: `/business/template/${id}/publish`,
    method: 'post',
  });
}

// 审核员审核模板
export function auditTemplatePublish(id, params) {
  return http.request({
    url: `/business/template/${id}/audit`,
    method: 'post',
    params,
  });
}

// 撤回发布模板
export function revokeTemplatePublish(id) {
  return http.request({
    url: `/business/template/${id}/revoke`,
    method: 'post',
  });
}

// 审核员下架素材
export function revisingTemplatePublish(id) {
  return http.request({
    url: `/business/template/${id}/revising`,
    method: 'post',
  });
}

// 运营同步模板
export function syncTemplate(data) {
  return http.request({
    url: `/business/template/action/sync`,
    method: 'post',
    params: data
  });
}

// 查询同步结果
export function getSyncResult(id) {
  return http.request({
    url: `/business/template/sync/status?taskid=${id}`,
    method: 'get',
  });
}

/** 上传tts语音 */
export function uploadTts(data) {
  return http.request({
    url: '/business/template/tts/upload',
    method: 'post',
    params: data,
  });
}
