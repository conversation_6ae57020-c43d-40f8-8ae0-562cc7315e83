import { Layout } from '@/router/constant';
import { renderIcon } from '@/utils/index';
import { PERMISSION_KEYS } from './audit';
import SecurityIcon from '@/components/menu-icons/super/security.vue';
import OperateIcon from '@/components/menu-icons/super/operate.vue';

/**
 * @param name 路由名称, 必须设置,且不能重名
 * @param meta 路由元信息（路由附带扩展信息）
 * @param redirect 重定向地址, 访问这个路由时,自定进行重定向
 * @param meta.disabled 禁用整个菜单
 * @param meta.title 菜单名称
 * @param meta.icon 菜单图标
 * @param meta.keepAlive 缓存该路由
 * @param meta.sort 排序越小越排前
 *
 * */
const routes = [
  {
    path: '/log/security',
    component: Layout,
    meta: {
      icon: renderIcon(SecurityIcon),
      sort: 3,
    },
    auth: [PERMISSION_KEYS.super.system_log_read],
    children: [
      {
        path: '',
        name: 'SecurityLog',
        meta: {
          title: '安全日志',
        },
        component: () => import('@/views/log/security/index.vue'),
      },
    ],
  },
  {
    path: '/log/operation',
    component: Layout,
    meta: {
      icon: renderIcon(OperateIcon),
      sort: 3,
    },
    auth: [PERMISSION_KEYS.super.operate_log_read],
    children: [
      {
        path: '',
        name: 'OperationLog',
        meta: {
          title: '操作日志',
        },
        component: () => import('@/views/log/operation/index.vue'),
      },
    ],
  },
];

export default routes;
