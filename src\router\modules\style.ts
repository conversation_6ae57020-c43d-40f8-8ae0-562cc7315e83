import { Layout } from '@/router/constant';
import { renderIcon } from '@/utils/index';
import { PERMISSION_KEYS } from './audit';
import StyleIcon from '@/components/menu-icons/operator/style.vue';

const routes: Array<any> = [
  {
    path: '/style',
    redirect: '/style/list',
    // name: 'style',
    component: Layout,
    meta: {
      title: '风格管理',
      sort: 1,
      isRoot: true,
      activeMenu: 'style',
      icon: renderIcon(StyleIcon),
    },
    auth: [PERMISSION_KEYS.operator.provider_style_read],
    children: [
      {
        path: 'list',
        name: `style`,
        meta: {
          title: '',
          activeMenu: 'style',
        },
        component: () => import('@/views/operator/style.vue'),
      },
      {
        path: 'add',
        name: `style_add`,
        meta: {
          title: '新增风格',
          activeMenu: 'style',
          hidden: true,
        },
        component: () => import('@/views/operator/info.vue'),
      },
      {
        path: 'edit',
        name: `style_edit`,
        meta: {
          title: '编辑风格',
          activeMenu: 'style',
          hidden: true,
        },
        component: () => import('@/views/operator/info.vue'),
      },
    ],
  },
];

export default routes;
