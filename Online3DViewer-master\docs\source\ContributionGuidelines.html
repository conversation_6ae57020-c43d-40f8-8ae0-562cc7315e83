<h1>Contribution Guidelines</h1>

<p>Before implementing a new feature or fixing a bug, please read the following guidelines.</p>

<h2>Basic Guidelines</h2>

<p>Before you start, read the {@link Environment Setup} page. Pull requests must target the <code class="inline">dev</code> branch. Please create a separate pull request for every feature or bug fix.</p>

<h2>Bug Fixing Guidelines</h2>

<p>It's recommended to create an issue before fixing a bug, so it can be discussed what is the best way to fix it.</p>

<h2>Feature Development Guidelines</h2>

<p>It's recommended to create an issue before implementing a new feature. It's always better to discuss the solution before you start to implement it.</p>

<p>There are several aspects that should be taken into consideration when implementing something new.</p>

<ul>
    <li>How will the new feature fit in the existing user interface and user experience?</li>
    <li>Will it work with any kind of model (regardless of color, size, etc.)?</li>
    <li>Does it need to be configurable? Where to store the configuration? How will it appear on the UI?</li>
    <li>How to test the new feature?</li>
</ul>
