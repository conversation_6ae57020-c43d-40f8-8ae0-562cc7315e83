import { getCaptcha } from '@/api/system/user';

import { ref } from 'vue';

const qrSrc = (qrCode: string) => {
  if (qrCode === '') return '';
  const b64 = window.btoa(decodeURIComponent(encodeURIComponent(qrCode)));
  return `data:image/svg+xml;base64,${b64}`;
};

export default function useCaptch() {
  const captchaRef = ref();

  const fetchCaptch = async () => {
    const res = await getCaptcha();
    captchaRef.value = qrSrc(res);
  };
  return {
    fetchCaptch,
    captchaRef,
  };
}
