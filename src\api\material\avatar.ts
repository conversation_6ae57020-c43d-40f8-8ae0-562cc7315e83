import { http } from '@/utils/http/axios';

//获取table
export function getAvatarList(params) {
  return http.request({
    url: '/business/asset-avatar',
    method: 'get',
    params,
  });
}

export function getAuditAvatarList(params) {
  return http.request({
    url: '/business/asset-avatar/audit/list',
    method: 'get',
    params,
  });
}

//获取形象类型
export function getAvatarTypeList() {
  return http.request({
    url: '/business/asset-avatar/type/list',
    method: 'get',
  });
}

// 新增形象
export function addAvatar(data) {
  return http.request({
    url: '/business/asset-avatar',
    method: 'post',
    params: data,
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// 删除形象
export function deleteAvatar(id) {
  return http.request({
    url: `/business/asset-avatar/${id}/delete`,
    method: 'post',
  });
}

// 批量删除形象
export function batchDeleteAvatar(params) {
  return http.request({
    url: '/business/asset-avatar/batch/deleteBatch',
    method: 'post',
    params,
  });
}

// 获取形象详情
export function getAvatarDetail(id) {
  return http.request({
    url: `/business/asset-avatar/${id}`,
    method: 'get',
  });
}

// 审核获取形象详情
export function getAuditAvatarDetail(id) {
  return http.request({
    url: `/business/asset-avatar/${id}/audit`,
    method: 'get',
  });
}

// 编辑形象
export function editAvatar(id, params) {
  return http.request({
    url: `/business/asset-avatar/${id}`,
    method: 'POST',
    params,
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// 形象申请发布
export function applyAvatarPublish(id) {
  return http.request({
    url: `/business/asset-avatar/${id}/publish`,
    method: 'post',
  });
}

// 审核员审核形象
export function auditAvatarPublish(id, params) {
  return http.request({
    url: `/business/asset-avatar/${id}/audit`,
    method: 'post',
    params,
  });
}

// 撤回发布形象
export function revokeAvatarPublish(id) {
  return http.request({
    url: `/business/asset-avatar/${id}/revoke`,
    method: 'post',
  });
}

// 审核员下架素材
export function revisingAvatarPublish(id) {
  return http.request({
    url: `/business/asset-avatar/${id}/revising`,
    method: 'post',
  });
}
