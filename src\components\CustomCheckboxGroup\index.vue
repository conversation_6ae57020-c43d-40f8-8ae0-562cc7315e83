<template>
  <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
    {{ props.title }}
  </el-checkbox>
  <el-checkbox-group v-model="checkedValue" @change="handleCheckedValueChange">
    <el-checkbox v-for="option in props.options" :key="option.value" :label="option.value">
      {{ option.label }}
    </el-checkbox>
  </el-checkbox-group>
</template>

<script lang="ts" setup>
  import { computed, watch, ref, defineProps, onUnmounted } from 'vue';
  const props = defineProps(['modelValue', 'title', 'options', 'defaultValue']);
  const emit = defineEmits(['update:modelValue']);
  const checkAll = ref<boolean>(false);
  const isIndeterminate = ref<boolean>(false);
  const checkedValue = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    },
  });

  watch(
    [() => props.modelValue.length, () => props.options.length],
    ([modelValueLength, optionsLength]) => {
      if (modelValueLength === optionsLength) {
        checkAll.value = true;
      } else if (modelValueLength !== 0) {
        isIndeterminate.value = true;
      }
    },
    {
      immediate: true,
    }
  );

  const handleCheckAllChange = (val: boolean) => {
    checkedValue.value = val ? props.options.map((i) => i.value) : [];
    isIndeterminate.value = false;
  };
  const handleCheckedValueChange = (value: string[]) => {
    const checkedCount = value.length;
    checkAll.value = checkedCount === props.options.length;
    isIndeterminate.value = checkedCount > 0 && checkedCount < props.options.length;
  };

  onUnmounted(() => {
    emit('update:modelValue', []);
  });
</script>
