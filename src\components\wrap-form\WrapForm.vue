<template>
  <el-card class="box-card pb-4">
    <div class="px-6 py-2 text-center font-normal text-2xl">
      <slot name="form-title">{{ title }}</slot>
    </div>
    <div>
      <slot></slot>
    </div>
    <div>
      <slot name="form-footer">
        <div class="footer-btns">
          <el-button
            v-if="showCancel"
            @click="goback"
            >返回</el-button
          >
          <el-button
            type="primary"
            @click="handleSubmit"
            >{{ submitText }}</el-button
          >
        </div>
      </slot>
      <slot name="tip"></slot>
    </div>
    
  </el-card>
</template>

<script lang="ts">
  import router from '@/router';
  import { defineComponent, toRefs } from 'vue';
  export default defineComponent({
    name: 'WrapForm',
    props: {
      showCancel: {
        type: Boolean,
        default: true,
      },
      title: {
        type: String,
        default: '标题',
      },
      submitText: {
        type: String,
        default: '确定',
      },
    },
    emits: ['submit'],
    setup(props, { emit }) {
      const { title, submitText } = toRefs(props);
      const handleSubmit = () => emit('submit');
      const goback = () => router.go(-1);
      return {
        goback,
        handleSubmit,
        title,
        submitText,
      };
    },
  });
</script>

<style lang="less" scoped>
  .submit-btn {
    color: white;
  }
  .footer-btns {
    display: flex;
    justify-content: center;
    button {
      flex-grow: 1;
    }
  }
</style>
