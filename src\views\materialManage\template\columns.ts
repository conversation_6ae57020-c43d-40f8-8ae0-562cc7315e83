export interface ListData {
  icon: string;
  id: string;
  name: string;
  avatar: string;
  address: string;
  beginTime: string;
  endTime: string;
  date: string;
  status: number;
  url_icon: string
  style: {
    title: string
  }
  provider: {
    title: string
  }
  provider_id: string
  style_id: string
}
export enum EAssetStatus {
  /** 草稿 ；上传后的默认状态 */
  Draft = 1,
  /** 待审核 */
  Reviewing = 2,
  /** 已发布 */
  Published = 3,
  /** 驳回待修改 */
  Rejected = 4,
  /** 下架待修改 */
  Unshelve = 5
}
