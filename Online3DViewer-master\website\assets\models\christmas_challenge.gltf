{"asset": {"version": "2.0", "generator": "https://3dviewer.net"}, "scene": 0, "scenes": [{"nodes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100]}], "nodes": [{"translation": [0, 0, 0], "mesh": 0}, {"translation": [60, 0, 0], "mesh": 0}, {"translation": [120, 0, 0], "mesh": 0}, {"translation": [180, 0, 0], "mesh": 0}, {"translation": [240, 0, 0], "mesh": 0}, {"translation": [300, 0, 0], "mesh": 0}, {"translation": [360, 0, 0], "mesh": 0}, {"translation": [420, 0, 0], "mesh": 0}, {"translation": [480, 0, 0], "mesh": 0}, {"translation": [540, 0, 0], "mesh": 0}, {"translation": [0, 0, 60], "mesh": 0}, {"translation": [60, 0, 60], "mesh": 0}, {"translation": [120, 0, 60], "mesh": 0}, {"translation": [180, 0, 60], "mesh": 0}, {"translation": [240, 0, 60], "mesh": 0}, {"translation": [300, 0, 60], "mesh": 0}, {"translation": [360, 0, 60], "mesh": 0}, {"translation": [420, 0, 60], "mesh": 0}, {"translation": [480, 0, 60], "mesh": 0}, {"translation": [540, 0, 60], "mesh": 0}, {"translation": [0, 0, 120], "mesh": 0}, {"translation": [60, 0, 120], "mesh": 0}, {"translation": [120, 0, 120], "mesh": 0}, {"translation": [180, 0, 120], "mesh": 0}, {"translation": [240, 0, 120], "mesh": 0}, {"translation": [300, 0, 120], "mesh": 0}, {"translation": [360, 0, 120], "mesh": 0}, {"translation": [420, 0, 120], "mesh": 0}, {"translation": [480, 0, 120], "mesh": 0}, {"translation": [540, 0, 120], "mesh": 0}, {"translation": [0, 0, 180], "mesh": 0}, {"translation": [60, 0, 180], "mesh": 0}, {"translation": [120, 0, 180], "mesh": 0}, {"translation": [180, 0, 180], "mesh": 0}, {"translation": [240, 0, 180], "mesh": 0}, {"translation": [300, 0, 180], "mesh": 0}, {"translation": [360, 0, 180], "mesh": 0}, {"translation": [420, 0, 180], "mesh": 0}, {"translation": [480, 0, 180], "mesh": 0}, {"translation": [540, 0, 180], "mesh": 0}, {"translation": [0, 0, 240], "mesh": 0}, {"translation": [60, 0, 240], "mesh": 0}, {"translation": [120, 0, 240], "mesh": 0}, {"translation": [180, 0, 240], "mesh": 0}, {"translation": [240, 0, 240], "mesh": 0}, {"translation": [300, 0, 240], "mesh": 0}, {"translation": [360, 0, 240], "mesh": 0}, {"translation": [420, 0, 240], "mesh": 0}, {"translation": [480, 0, 240], "mesh": 0}, {"translation": [540, 0, 240], "mesh": 0}, {"translation": [0, 0, 300], "mesh": 0}, {"translation": [60, 0, 300], "mesh": 0}, {"translation": [120, 0, 300], "mesh": 0}, {"translation": [180, 0, 300], "mesh": 0}, {"translation": [240, 0, 300], "mesh": 0}, {"translation": [300, 0, 300], "mesh": 0}, {"translation": [360, 0, 300], "mesh": 0}, {"translation": [420, 0, 300], "mesh": 0}, {"translation": [480, 0, 300], "mesh": 0}, {"translation": [480, 0, 300], "rotation": [0, 0.7071067811865475, 0, 0.7071067811865476], "mesh": 1}, {"translation": [540, 0, 300], "mesh": 0}, {"translation": [0, 0, 360], "mesh": 0}, {"translation": [60, 0, 360], "mesh": 0}, {"translation": [120, 0, 360], "mesh": 0}, {"translation": [180, 0, 360], "mesh": 0}, {"translation": [240, 0, 360], "mesh": 0}, {"translation": [300, 0, 360], "mesh": 0}, {"translation": [360, 0, 360], "mesh": 0}, {"translation": [420, 0, 360], "mesh": 0}, {"translation": [480, 0, 360], "mesh": 0}, {"translation": [540, 0, 360], "mesh": 0}, {"translation": [0, 0, 420], "mesh": 0}, {"translation": [60, 0, 420], "mesh": 0}, {"translation": [120, 0, 420], "mesh": 0}, {"translation": [180, 0, 420], "mesh": 0}, {"translation": [240, 0, 420], "mesh": 0}, {"translation": [300, 0, 420], "mesh": 0}, {"translation": [360, 0, 420], "mesh": 0}, {"translation": [420, 0, 420], "mesh": 0}, {"translation": [480, 0, 420], "mesh": 0}, {"translation": [540, 0, 420], "mesh": 0}, {"translation": [0, 0, 480], "mesh": 0}, {"translation": [60, 0, 480], "mesh": 0}, {"translation": [120, 0, 480], "mesh": 0}, {"translation": [180, 0, 480], "mesh": 0}, {"translation": [240, 0, 480], "mesh": 0}, {"translation": [300, 0, 480], "mesh": 0}, {"translation": [360, 0, 480], "mesh": 0}, {"translation": [420, 0, 480], "mesh": 0}, {"translation": [480, 0, 480], "mesh": 0}, {"translation": [540, 0, 480], "mesh": 0}, {"translation": [0, 0, 540], "mesh": 0}, {"translation": [60, 0, 540], "mesh": 0}, {"translation": [120, 0, 540], "mesh": 0}, {"translation": [180, 0, 540], "mesh": 0}, {"translation": [240, 0, 540], "mesh": 0}, {"translation": [300, 0, 540], "mesh": 0}, {"translation": [360, 0, 540], "mesh": 0}, {"translation": [420, 0, 540], "mesh": 0}, {"translation": [480, 0, 540], "mesh": 0}, {"translation": [540, 0, 540], "mesh": 0}], "materials": [{"name": "Material", "pbrMetallicRoughness": {"baseColorFactor": [0.1607843137254902, 0.3058823529411765, 0.403921568627451, 1], "metallicFactor": 0.3, "roughnessFactor": 0.5}, "emissiveFactor": [0, 0, 0], "doubleSided": true, "alphaMode": "OPAQUE"}, {"name": "Material", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1], "metallicFactor": 1.0, "roughnessFactor": 0.0}, "emissiveFactor": [0, 0, 0], "doubleSided": true, "alphaMode": "OPAQUE"}, {"name": "Material", "pbrMetallicRoughness": {"baseColorFactor": [0.6795424696265424, 0.0036765073221525194, 0.0036765073221525194, 1], "metallicFactor": 1.0, "roughnessFactor": 0.5}, "emissiveFactor": [0, 0, 0], "doubleSided": true, "alphaMode": "OPAQUE"}], "meshes": [{"name": "Gift", "primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3}, "indices": 0, "mode": 4, "material": 0}, {"attributes": {"POSITION": 5, "NORMAL": 6, "TEXCOORD_0": 7}, "indices": 4, "mode": 4, "material": 1}]}, {"name": "Gift", "primitives": [{"attributes": {"POSITION": 9, "NORMAL": 10}, "indices": 8, "mode": 4, "material": 2}]}], "buffers": [{"uri": "christmas_challenge.bin", "byteLength": 74544}], "bufferViews": [{"buffer": 0, "byteOffset": 0, "byteLength": 288}, {"buffer": 0, "byteOffset": 288, "byteLength": 576}, {"buffer": 0, "byteOffset": 864, "byteLength": 576}, {"buffer": 0, "byteOffset": 1440, "byteLength": 384}, {"buffer": 0, "byteOffset": 1824, "byteLength": 17280}, {"buffer": 0, "byteOffset": 19104, "byteLength": 15120}, {"buffer": 0, "byteOffset": 34224, "byteLength": 15120}, {"buffer": 0, "byteOffset": 49344, "byteLength": 10080}, {"buffer": 0, "byteOffset": 59424, "byteLength": 2160}, {"buffer": 0, "byteOffset": 61584, "byteLength": 6480}, {"buffer": 0, "byteOffset": 68064, "byteLength": 6480}], "accessors": [{"bufferView": 0, "byteOffset": 0, "componentType": 5125, "count": 72, "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 0, "componentType": 5126, "count": 48, "min": [-14.650325775146484, 3.552713678800501e-15, -15.102669715881348], "max": [15.349674224853516, 25.333101272583008, 14.897330284118652], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 0, "componentType": 5126, "count": 48, "type": "VEC3"}, {"bufferView": 3, "byteOffset": 0, "componentType": 5126, "count": 48, "type": "VEC2"}, {"bufferView": 4, "byteOffset": 0, "componentType": 5125, "count": 4320, "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 0, "componentType": 5126, "count": 1260, "min": [-9.59850025177002, 25.06179428100586, -9.744625091552734], "max": [10.430252075195312, 36.69747543334961, 11.216736793518066], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 0, "componentType": 5126, "count": 1260, "type": "VEC3"}, {"bufferView": 7, "byteOffset": 0, "componentType": 5126, "count": 1260, "type": "VEC2"}, {"bufferView": 8, "byteOffset": 0, "componentType": 5125, "count": 540, "type": "SCALAR"}, {"bufferView": 9, "byteOffset": 0, "componentType": 5126, "count": 540, "min": [-9.195235252380371, 2.8779759407043457, -10.1021146774292], "max": [10.866759300231934, 23.122024536132812, 9.954167366027832], "type": "VEC3"}, {"bufferView": 10, "byteOffset": 0, "componentType": 5126, "count": 540, "type": "VEC3"}]}