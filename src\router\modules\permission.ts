import { Layout } from '@/router/constant';
import { renderIcon } from '@/utils/index';
import { PERMISSION_KEYS } from './audit';
import UserIcon from '@/components/menu-icons/cp/partner.vue';
/**
 * @param name 路由名称, 必须设置,且不能重名
 * @param meta 路由元信息（路由附带扩展信息）
 * @param redirect 重定向地址, 访问这个路由时,自定进行重定向
 * @param meta.disabled 禁用整个菜单
 * @param meta.title 菜单名称
 * @param meta.icon 菜单图标
 * @param meta.keepAlive 缓存该路由
 * @param meta.sort 排序越小越排前
 *
 * */
const routes: Array<any> = [
  {
    path: '/permission',
    name: 'Permission',
    redirect: '/permission',
    component: Layout,
    meta: {
      title: '权限管理',
      icon: renderIcon(UserIcon),
      sort: 1,
    },
    auth: [PERMISSION_KEYS.super.permission_read],
    children: [
      {
        path: '',
        name: 'permission-list',
        meta: {
          title: '',
        },
        component: () => import('@/views/permission/permission.vue'),
      },
      {
        path: 'add',
        name: 'permission-add',
        meta: {
          title: '权限新增',
          activeMenu: 'permission-list',
          hidden: true,
        },
        component: () => import('@/views/permission/info.vue'),
      },
      {
        path: 'edit',
        name: 'permission-edit',
        meta: {
          title: '权限编辑',
          activeMenu: 'permission-list',
          hidden: true,
        },
        component: () => import('@/views/permission/info.vue'),
      },
    ],
  },
];

export default routes;
