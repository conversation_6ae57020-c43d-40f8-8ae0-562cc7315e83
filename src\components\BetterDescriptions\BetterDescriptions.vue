<template>
  <div class="el-descriptions better-descriptions">
    <div class="el-descriptions__title" style="margin-bottom: 16px">
      预览
    </div>
    <el-row :gutter="20">
      <template v-for="item in props.columns">
        <el-col v-if="!item.hide" :span="item.span" class="better-descriptions-item" >
          <span class="better-descriptions-item-label">
            {{item.label}}:
          </span>
          <component :is="item.data()" v-if="typeof item.data === 'function'"/>
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="item.data"
            placement="top"
            v-else
          >
          <span class="better-descriptions-item-content">
              {{
                item.data
              }}
          </span>
          </el-tooltip>
        </el-col>
      </template>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
  const props = defineProps(['form', 'columns']);

  
</script>
<style lang="less" scoped>
  .better-descriptions-item {
    display: flex;
    padding-bottom: 12px;
    .better-descriptions-item-label {
      flex-shrink: 0;
      max-width: 100px;
      margin-right: 16px;
    }
    .better-descriptions-item-content {
      overflow: hidden;
      text-wrap: nowrap;
      text-overflow: ellipsis;
    }
  }
</style>
