{"asset": {"generator": "COLLADA2GLTF", "version": "2.0"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"children": [1], "matrix": [1, 0, 0, 0, 0, 0, -1, 0, 0, 1, 0, 0, 0, 0, 0, 1]}, {"mesh": 0}], "meshes": [{"primitives": [{"attributes": {"NORMAL": 1, "POSITION": 2}, "indices": 0, "mode": 4, "material": 0, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 0, "attributes": {"NORMAL": 0, "POSITION": 1}}}}], "name": "<PERSON><PERSON>"}], "accessors": [{"componentType": 5123, "count": 36, "max": [23], "min": [0], "type": "SCALAR"}, {"componentType": 5126, "count": 24, "max": [1.007843137254902, 1.007843137254902, 1.007843137254902], "min": [-1.007843137254902, -1.007843137254902, -1.007843137254902], "type": "VEC3"}, {"componentType": 5126, "count": 24, "max": [0.5004885197850513, 0.5004885197850513, 0.5004885197850513], "min": [-0.5004885197850513, -0.5004885197850513, -0.5004885197850513], "type": "VEC3"}], "materials": [{"pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 1}, "name": "Red", "emissiveFactor": [0, 0, 0], "alphaMode": "OPAQUE", "doubleSided": false}], "bufferViews": [{"buffer": 0, "byteOffset": 0, "byteLength": 118}], "buffers": [{"name": "Box", "byteLength": 120, "uri": "Box.bin"}], "extensionsRequired": ["KHR_draco_mesh_compression"], "extensionsUsed": ["KHR_draco_mesh_compression"]}