<template>
  <el-button
    @click="
      () => {
        dialogVisible = true;
      }
    "
    type="danger"
  >
    驳回
  </el-button>

  <el-dialog v-model="dialogVisible" title="审核意见" width="40%" append-to-body>
    <el-form ref="ruleFormRef" :model="detailForm" :rules="rules" label-width="95px" status-icon>
      <el-form-item label="审核意见：" prop="comment">
        <el-input
          v-model="detailForm.comment"
          type="textarea"
          placeholder="请描述"
          maxlength="100"
          rows="6"
          style="width: 100%"
          @input="(value) => handleInput('comment', value)"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">返回</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { useFilterInputHander } from '@/views/hooks';
import { FormInstance, FormRules } from 'element-plus';
  import { onMounted, reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  const props = defineProps(['id', 'postFn', 'backpath']);
  const router = useRouter();
  const dialogVisible = ref(false);
  const ruleFormRef = ref<FormInstance>();
  const detailForm = reactive({
    username: '',
    phone: '',
    name: '',
    id_card_img: '',
    id_card_imgback: '',
    company_img: '',
    comment: '',
  });
  const handleInput = useFilterInputHander(detailForm);

  const rules = reactive<FormRules>({
    comment: [
      {
        required: true,
        message: '请输入审核意见',
        trigger: 'blur',
      },
    ],
  });

  onMounted(async () => {});

  const auditAction = async () => {
    const { comment } = detailForm;
    const params = {
      audit_reason: comment,
      audit_result: '0',
    };
    const { code } = await props.postFn(props.id, params);
    if (code === 0) {
      router.push({
        name: props.backpath
      });
    }
  };

  const handleSubmit = async () => {
    if (!ruleFormRef.value) return;
    await ruleFormRef.value.validate();
    await auditAction();
  };
</script>
<style lang="less" scoped>
  .box-card {
    min-height: 740px;
  }

  .content {
    margin-bottom: 110px;
  }

  .content-bottom {
    height: 100px;
    width: 100%;
    background: #fff;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 0;
  }
</style>
