<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no">

	<title>Online 3D Viewer</title>

	<script type="text/javascript" src="../build/engine_dev/o3dv.min.js"></script>

    <style>
        div.online_3d_viewer
        {
            float: left;
            border: 1px solid #eeeeee;
            width: 640px;
            height: 480px;
        }
    </style>

    <script type='text/javascript'>
        function OnModelLoaded (model)
        {
            let calculationDiv = document.getElementById ('calculation_result');
            let boundingBox = OV.GetBoundingBox (model);
            calculationDiv.innerHTML =
                'Surface area: ' + OV.CalculateSurfaceArea (model).toFixed (2) + '<br>' +
                'Volume: ' + OV.CalculateVolume (model).toFixed (2) + '<br>' +
                'Size: (' +
                    (boundingBox.max.x - boundingBox.min.x) + ', ' +
                    (boundingBox.max.y - boundingBox.min.y) + ', ' +
                    (boundingBox.max.z - boundingBox.min.z) +
                ')<br>';
        }

        window.addEventListener ('load', () => {
            // get the parent element of the viewer
            let parentDiv = document.getElementById ('viewer');

            // initialize the viewer with the parent element and some parameters
            let viewer = new OV.EmbeddedViewer (parentDiv, {
                backgroundColor : new OV.RGBAColor (255, 255, 255, 255),
                defaultColor : new OV.RGBColor (200, 200, 200),
                edgeSettings : new OV.EdgeSettings (false, new OV.RGBColor (0, 0, 0), 1),
                environmentSettings : new OV.EnvironmentSettings (
                    [
                        '../website/assets/envmaps/fishermans_bastion/posx.jpg',
                        '../website/assets/envmaps/fishermans_bastion/negx.jpg',
                        '../website/assets/envmaps/fishermans_bastion/posy.jpg',
                        '../website/assets/envmaps/fishermans_bastion/negy.jpg',
                        '../website/assets/envmaps/fishermans_bastion/posz.jpg',
                        '../website/assets/envmaps/fishermans_bastion/negz.jpg'
                    ],
                    true
                ),
                onModelLoaded : () => {
                    OnModelLoaded (viewer.GetModel ());
                }
            });

            // load a model providing model urls
            viewer.LoadModelFromUrlList ([
                '../../test/testfiles/obj/hundred_cubes.obj',
                '../../test/testfiles/obj/hundred_cubes.mtl'
            ]);
        });
    </script>
</head>

<body>
    <div class="online_3d_viewer" id="viewer"></div>
    <div id="calculation_result"></div>
</body>

</html>
