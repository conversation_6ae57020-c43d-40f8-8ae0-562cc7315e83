import { Layout } from '@/router/constant';
import { renderIcon } from '@/utils/index';
import CpManageIcon from '@/components/menu-icons/audit/cpmanage.vue';
import OverviewIcon from '@/components/menu-icons/cp/overview.vue';
import StatisticIcon from '@/components/menu-icons/cp/statistics.vue';
import BgmAIcon from '@/components/menu-icons/audit/bgma.vue';

export const PERMISSION_KEYS = {
  /** cp用户 */
  cpuser: {
    /** 查看合作伙伴 */
    provider_use: 'provider_use',
    /** 查看素材 */
    asset_read: 'asset_read',
    /** 创建素材 */
    asset_create: 'asset_create',
    /** 查看预置形象 */
    asset_avatar_read: 'asset_avatar_read',
    /** 创建预置形象 */
    asset_avatar_create: 'asset_avatar_create',
    /** 查看背景音乐 */
    asset_bgm_read: 'asset_bgm_read',
    /** 创建背景音乐 */
    asset_bgm_create: 'asset_bgm_create',
    /** 编辑素材 */
    asset_edit: 'asset_edit',
    /** 编辑预置形象 */
    asset_avatar_edit: 'asset_avatar_edit',
    /** 编辑背景音乐 */
    asset_bgm_edit: 'asset_bgm_edit',
    /** 发布素材 */
    asset_publish: 'asset_publish',
    /** 撤销发布素材 */
    asset_unpublish: 'asset_unpublish',
    /** 发布预置形象 */
    asset_avatar_publish: 'asset_avatar_publish',
    /** 撤销发布预置形象 */
    asset_avatar_unpublish: 'asset_avatar_unpublish',
    /** 发布背景音乐 */
    asset_bgm_publish: 'asset_bgm_publish',
    /** 撤销发布背景音乐 */
    asset_bgm_unpublish: 'asset_bgm_unpublish',
    /** 删除素材 */
    asset_remove: 'asset_remove',
    /** 删除预置形象 */
    asset_avatar_remove: 'asset_avatar_remove',
    /** 删除背景音 */
    asset_bgm_remove: 'asset_bgm_remove',
    /** 数据统计 */
    asset_refs4cp: 'asset_refs4cp',
    /** 资产概览 */
    asset_overview4cp: 'asset_overview4cp',
    /** 能力伙伴风格 */
    provider_style_use: 'provider_style_use',
    /** 模板 */
    template_read: 'template_read',
    template_publish: 'template_publish',
    template_edit: 'template_edit',
    template_create: 'template_create',
    template_remove: 'template_remove',
    template_unpublish: 'template_unpublish'
  },
  /** 审核 */
  auditor: {
    /** 审核CP用户 */
    audit_user: 'audit_user',
    /** 审核素材 */
    audit_asset: 'audit_asset',
    /** 审核预置形象 */
    audit_asset_avatar: 'audit_asset_avatar',
    /** 审核背景音乐 */
    audit_asset_bgm: 'audit_asset_bgm',
    /** 审核人员查看数据统计 */
    audit_asset_refs4audit: 'audit_asset_refs4audit',
    /** 审核人员查看资产概览 */
    audit_asset_overview4audit: 'audit_asset_overview4audit',
    /** 审核模板 */
    audit_template: 'audit_template',
  },
  /** 运营人员 */
  operator: {
    /** 查看合作伙伴 */
    provider_read: 'provider_read',
    /** 添加合作伙伴 */
    provider_add: 'provider_add',
    /** 编辑合作伙伴 */
    provider_edit: 'provider_edit',
    /** 删除合作伙伴 */
    provider_remove: 'provider_remove',
    /** 上传基础模型 */
    provider_model_upload: 'provider_model_upload',
    /** 查看能力伙伴风格 */
    provider_style_read: 'provider_style_read',
    /** 新增能力伙伴风格 */
    provider_style_add: 'provider_style_add',
    /** 编辑能力伙伴风格 */
    provider_style_edit: 'provider_style_edit',
    /** 删除能力伙伴风格 */
    provider_style_remove: 'provider_style_remove',
    /** 同步素材 */
    asset_sync: 'asset_sync',
    template_sync: 'template_sync',
  },
  /** 超管 */
  super: {
    /** 查看账户列表 */
    user_read_list: 'user_read_list',
    /** 添加账户 */
    user_add: 'user_add',
    /** 编辑账户 */
    user_edit: 'user_edit',
    /** 禁用账户 */
    user_disable: 'user_disable',
    /** 启用账户 */
    user_enable: 'user_enable',
    /** 删除账户 */
    user_remove: 'user_remove',
    /** 查看角色 */
    role_read: 'role_read',
    /** 添加角色 */
    role_add: 'role_add',
    /** 编辑角色 */
    role_edit: 'role_edit',
    /** 删除角色 */
    role_remove: 'role_remove',
    /** 禁用角色 */
    role_disable: 'role_disable',
    /** 启用角色 */
    role_enable: 'role_enable',
    /** 查看安全日志 */
    system_log_read: 'system_log_read',
    /** 查看操作日志 */
    operate_log_read: 'operate_log_read',
    /** 查看权限 */
    permission_read: 'permission_read',
    /** 编辑权限 */
    permission_edit: 'permission_edit',
  },
};

const routes: Array<any> = [
  {
    path: '/audit/cp',
    redirect: '/audit/cp/list',
    component: Layout,
    meta: {
      sort: -1,
      isRoot: true,
      activeMenu: 'audit-cp-list',
      icon: renderIcon(CpManageIcon),
    },
    auth: [PERMISSION_KEYS.auditor.audit_user],
    children: [
      {
        path: 'list',
        name: `audit-cp-list`,
        meta: {
          title: 'CP管理',
          activeMenu: 'audit-cp-list',
        },
        auth: [PERMISSION_KEYS.auditor.audit_user],
        component: () => import('@/views/cp/index.vue'),
      },
      {
        path: 'detail',
        name: `cp_detail`,
        meta: {
          title: '审核',
          hidden: true,
          activeMenu: 'audit-cp-list',
        },
        component: () => import('@/views/cp/detail.vue'),
      },
    ],
  },
  {
    path: '/audit/overview',
    component: Layout,
    meta: {
      sort: 11,
      isRoot: true,
      activeMenu: 'audit-overview',
      icon: renderIcon(OverviewIcon),
    },
    auth: [PERMISSION_KEYS.auditor.audit_user],
    children: [
      {
        path: '',
        name: `audit-overview`,
        meta: {
          title: '资产概览',
          activeMenu: 'audit-overview',
        },
        component: () => import('@/views/overview/audit-overview.vue'),
      },
    ],
  },
  {
    path: '/audit/statistics',
    component: Layout,
    meta: {
      sort: 10,
      isRoot: true,
      activeMenu: 'audit-statistics',
      icon: renderIcon(StatisticIcon),
    },
    auth: [PERMISSION_KEYS.auditor.audit_user],
    children: [
      {
        path: '',
        name: `audit-statistics`,
        meta: {
          title: '数据统计',
          activeMenu: 'audit-statistics',
        },
        component: () => import('@/views/statistics/audit-statistics.vue'),
      },
    ],
  },
  {
    path: '/audit/bgm/list',
    component: Layout,
    meta: {
      sort: 8,
      isRoot: true,
      activeMenu: 'audit-bgm',
      icon: renderIcon(BgmAIcon),
    },
    auth: [PERMISSION_KEYS.auditor.audit_user],
    children: [
      {
        path: '',
        name: 'audit-bgm',
        meta: {
          title: '背景音乐审核',
          activeMenu: 'audit-bgm',
        },
        component: () => import('@/views/bgm/audit-bgm-list.vue'),
      },
    ],
  },
];

export default routes;
