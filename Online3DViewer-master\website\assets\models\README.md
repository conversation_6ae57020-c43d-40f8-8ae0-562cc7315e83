## Model file licenses

### ArchDetail.FCStd

GNU LIBRARY GENERAL PUBLIC LICENSE, Copyright (C) 1991 Free Software Foundation, Inc.

Source: https://github.com/FreeCAD/FreeCAD

### as1_pe_203.brep

Source: https://www.mbx-if.org/cax/cax_stepLib.php

### as1_pe_203.igs

Source: https://www.mbx-if.org/cax/cax_stepLib.php

### as1_pe_203.stp

Source: https://www.mbx-if.org/cax/cax_stepLib.php

### car.glb

MIT License, Copyright (c) 2023 Viktor <PERSON>

### cow.ply

GNU LGPL license

Source: https://people.sc.fsu.edu/~jburkardt/data/ply/ply.html

### cube.off

Source: https://en.wikipedia.org/wiki/OFF_(file_format)

### cubes.3ds

MIT License, Copyright (c) 2023 Viktor <PERSON>

### DamagedHelmet.glb

Created by [theblueturtle_](https://sketchfab.com/theblueturtle_), published under a Creative Commons Attribution-NonCommercial license

Source: https://sketchfab.com/models/b81008d513954189a063ff901f7abfe4

### extrusion3.wrl

MIT License, Copyright © 2010-2023 three.js authors

Source: https://github.com/mrdoob/three.js

### haus.ifc

MIT License, Copyright (c) 2021 Antonio González Viegas

Source: https://github.com/IFCjs/test-ifc-files

### MultipleMeshes.bim

MIT License, Copyright (c) 2022 Wojciech Radaczyński

Source: https://github.com/paireks/dotbim

### RhinoLogo.3dm

MIT License, Copyright (c) 2020 Robert McNeel & Associates

Source: https://github.com/mcneel/rview

### rhombicuboctahedron.3mf

BSD 2-Clause License, Copyright (c) 2018, 3MF Consortium

Source: https://github.com/3MFConsortium/3mf-samples

### rook.amf

MIT License, Copyright © 2010-2023 three.js authors

Source: https://github.com/mrdoob/three.js

### solids.obj, solids.mtl

MIT License, Copyright (c) 2023 Viktor Kovacs

### utah_teapot.stl

Creative Commons CC0 1.0 Universal Public Domain Dedication

Source: https://commons.wikimedia.org/wiki/File:Utah_teapot_(solid).stl

### X_Bot.dae

Source: https://www.mixamo.com

### Y_Bot.fbx

Source: https://www.mixamo.com
