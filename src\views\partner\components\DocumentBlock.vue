<template>
  <div class="wrapper p-5 m-5">
    <h4>{{ name }}</h4>
    <template v-if="url || description"> 
      <div v-if="url" class="m-3 font-medium"><span>文档地址：</span><a :href="url" target="_blank" rel="noopenner noreferrer">{{url}}</a></div>
      <div v-if="description" class="m-3 font-medium">
        <span>文档说明：</span>
        <span>{{ description }}</span>
      </div>
    </template>
    <div v-else>
      暂无数据
    </div>
  </div>
</template>

<script lang="ts">

import { defineComponent } from 'vue'

export default defineComponent({
  props: {
    name: {
      type: String,
      default: '模型文档'
    },
    url: {
      type: String,
      default: '',
    },
    description: {
      type: String,
      default: '',
    }
  }
})
</script>

<style lang="less" scoped>
.wrapper {
  background: #e5e7eb;
  box-sizing: border-box;
}
</style>
