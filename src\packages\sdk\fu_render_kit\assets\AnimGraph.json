{"MetaData": {"Generator": "NamaEngine v1.0.0.", "CreateTime": "2021-12-22 17:51:12", "Version": "1.0.0.0"}, "boneMasks": {"HeadMask": {"type": "Positive", "mask": ["Head_M", "<PERSON>_jawUpper_jnt", "M_jaw_open", "upperHead", "foreHead", "L_brow", "L_innerBrow", "L_middleBrow", "L_outerBrow", "R_brow", "R_innerBrow", "R_middle<PERSON><PERSON>", "R_outerBrow", "L_eye", "L_outer_eyeLid_jnt", "L_inter_eyeLid_jnt", "L_eScale", "Eye_L", "L_eye_lower", "L_lower_eyeLid_jnt", "L_lower_eyeLidIn_jnt", "L_lower_eyeLidOut_jnt", "L_eye_upper", "L_upper_eyeLid_jnt", "L_upper_eyeLidIn_jnt", "L_upper_eyeLidOut_jnt", "<PERSON>_eye", "R_outer_eyeLid_jnt", "R_inter_eyeLid_jnt", "R_eScale", "Eye_R", "R_eye_lower", "R_lower_eyeLid_jnt", "R_lower_eyeLidIn_jnt", "R_lower_eyeLidOut_jnt", "R_eye_upper", "R_upper_eyeLid_jnt", "R_upper_eyeLidIn_jnt", "R_upper_eyeLidOut_jnt"]}, "FullBodyMask": {"type": "Negative", "mask": ["Ankle_<PERSON>", "<PERSON><PERSON>_<PERSON>", "Chest_M", "Cup_L", "Cup_R", "ElbowPart1_L", "ElbowPart1_R", "Elbow_L", "El<PERSON>_R", "Eye_L", "Eye_R", "Global", "Head_M", "HipPart1_L", "HipPart1_R", "Hip_L", "Hip_R", "IndexFinger1_L", "IndexFinger1_R", "IndexFinger2_L", "IndexFinger2_R", "IndexFinger3_L", "IndexFinger3_R", "IndexFinger4_L", "IndexFinger4_R", "KneePart1_L", "KneePart1_R", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_<PERSON>", "L_brow", "L_eScale", "L_eye", "L_eye_lower", "L_eye_upper", "L_innerBrow", "L_inter_eyeLid_jnt", "L_lower_eyeLidIn_jnt", "L_lower_eyeLidOut_jnt", "L_lower_eyeLid_jnt", "L_middleBrow", "L_outerBrow", "L_outer_eyeLid_jnt", "L_upper_eyeLidIn_jnt", "L_upper_eyeLidOut_jnt", "L_upper_eyeLid_jnt", "<PERSON>_jawUpper_jnt", "M_jaw_open", "MiddleFinger1_L", "MiddleFinger1_R", "MiddleFinger2_L", "MiddleFinger2_R", "MiddleFinger3_L", "MiddleFinger3_R", "MiddleFinger4_L", "MiddleFinger4_R", "NeckPart1_M", "Neck_M", "PinkyFinger1_L", "PinkyFinger1_R", "PinkyFinger2_L", "PinkyFinger2_R", "PinkyFinger3_L", "PinkyFinger3_R", "PinkyFinger4_L", "PinkyFinger4_R", "R_brow", "R_eScale", "<PERSON>_eye", "R_eye_lower", "R_eye_upper", "R_innerBrow", "R_inter_eyeRid_jnt", "R_lower_eyeLidIn_jnt", "R_lower_eyeLidOut_jnt", "R_lower_eyeLid_jnt", "R_middle<PERSON><PERSON>", "R_outerBrow", "R_outer_eyeRid_jnt", "R_upper_eyeLidIn_jnt", "R_upper_eyeLidOut_jnt", "R_upper_eyeLid_jnt", "RingFinger1_L", "RingFinger1_R", "RingFinger2_L", "RingFinger2_R", "RingFinger3_L", "RingFinger3_R", "RingFinger4_L", "RingFinger4_R", "Root_M", "Scapula_L", "Scapula_R", "ShoulderPart1_L", "ShoulderPart1_R", "Shoulder_L", "Shoulder_R", "Spine1Part1_M", "Spine1_M", "ThumbFinger1_L", "ThumbFinger1_R", "ThumbFinger2_L", "ThumbFinger2_R", "ThumbFinger3_L", "ThumbFinger3_R", "ThumbFinger4_L", "ThumbFinger4_R", "ToesEnd_L", "ToesEnd_R", "To<PERSON>_<PERSON>", "<PERSON><PERSON>_<PERSON>", "Wrist_L", "Wrist_<PERSON>", "foreHead", "upperHead"]}, "all": {"type": "Negative", "mask": [""]}, "none": {"type": "Positive", "mask": [""]}}, "blendShapeMasks": {"all": {"type": "Negative", "mask": [""]}, "none": {"type": "Positive", "mask": [""]}, "face_processor": {"type": "Positive", "mask": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55"]}, "mouth": {"type": "Negative", "mask": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18"]}}, "paramNodes": {"ResultNodeWeight": {"type": "Float", "value": 1.0}, "BaseBlendNodeActiveIndex": {"type": "Int", "value": 0}, "BaseBlendNodeBlendAlpha": {"type": "Float", "value": 1.0}, "BaseBlendNodeBlendComplete": {"type": "Bool", "value": true}, "DefaultStateBlendTime": {"type": "Float", "value": 0.5}, "IdleStateBlendTime": {"type": "Float", "value": 0.5}, "TalkStateBlendTime": {"type": "Float", "value": 0.5}, "ListenStateBlendTime": {"type": "Float", "value": 0.5}, "DefaultAnimNodeSpeed": {"type": "Float", "value": 1.0}, "DefaultAnimNodeManualControlProgress": {"type": "Bool", "value": false}, "DefaultAnimNodeProgress": {"type": "Float", "value": 0.0}, "DefaultAnimNodeProgressEnd": {"type": "Bool", "value": false}, "IdleAnimNodeSpeed": {"type": "Float", "value": 1.0}, "IdleAnimNodeProgress": {"type": "Float", "value": 0.0}, "TalkAnimNodeSpeed": {"type": "Float", "value": 1.0}, "TalkAnimNodeProgress": {"type": "Float", "value": 0.0}, "ListenAnimNodeSpeed": {"type": "Float", "value": 1.0}, "ListenAnimNodeProgress": {"type": "Float", "value": 0.0}, "HeadBlendNodeBlendTime0": {"type": "Float", "value": 0.5}, "HeadAnimNodeSpeed": {"type": "Float", "value": 1.0}, "HeadAnimNodeProgress": {"type": "Float", "value": 0.0}, "ItemBlendNodeActiveIndex": {"type": "Int", "value": 0}, "ItemBlendNodeBlendTime0": {"type": "Float", "value": 0.5}, "ItemAnimNodeSpeed": {"type": "Float", "value": 1.0}, "ItemAnimNodeProgress": {"type": "Float", "value": 0.0}, "ItemTriggerBlendNodeBlendTime0": {"type": "Float", "value": 0.0}, "ItemTriggerAnimNodeSpeed": {"type": "Float", "value": 1.0}, "ItemTriggerAnimNodeProgress": {"type": "Float", "value": 0.0}, "HeadAnimActiveBlendTime": {"type": "Float", "value": 0.5}, "ItemAnimActiveBlendTime": {"type": "Float", "value": 0.0}, "HeadAnimActive": {"type": "Bool", "value": false}, "ItemAnimActive": {"type": "Bool", "value": false}, "AllBoneMaskBlendTime": {"type": "Float", "value": 0.0}, "AllBoneMaskActive": {"type": "Bool", "value": false}, "MaskActive": {"type": "Bool", "value": false}}, "animGraphNodes": [{"UID": 1, "name": "ResultNode", "type": "Result", "updateMode": "DisableOnZeroWeight", "animGraphNodeInputSlots": [{"name": "Result", "linkNodeUID": 12, "linkNodeName": "SecondBlendWithMaskNode"}], "paramNodeInputSlots": {"Weight": {"paramNodeName": "ResultNodeWeight"}}, "paramNodeOutputSlots": {"Weight": {"paramNodeName": "ResultNodeWeight"}}, "specialValue": {"blending": "Override", "weight": 1.0, "maskName": ""}}, {"UID": 2, "name": "BaseBlendNode", "type": "BlendByIntWithCache", "updateMode": "DisableOnZeroWeight", "animGraphNodeInputSlots": [{"name": "Blend Pose 0", "linkNodeUID": 3, "linkNodeName": "DefaultAnimNode"}, {"name": "Blend Pose 1", "linkNodeUID": 4, "linkNodeName": "IdleAnimNode"}, {"name": "Blend Pose 2", "linkNodeUID": 5, "linkNodeName": "TalkAnimNode"}, {"name": "Blend Pose 3", "linkNodeUID": 6, "linkNodeName": "ListenAnimNode"}], "paramNodeInputSlots": {"ActiveIndex": {"paramNodeName": "BaseBlendNodeActiveIndex"}, "BlendTime0": {"paramNodeName": "DefaultStateBlendTime"}, "BlendTime1": {"paramNodeName": "IdleStateBlendTime"}, "BlendTime2": {"paramNodeName": "TalkStateBlendTime"}, "BlendTime3": {"paramNodeName": "ListenStateBlendTime"}}, "paramNodeOutputSlots": {"ActiveIndex": {"paramNodeName": "BaseBlendNodeActiveIndex"}, "BlendTime0": {"paramNodeName": "DefaultStateBlendTime"}, "BlendTime1": {"paramNodeName": "IdleStateBlendTime"}, "BlendTime2": {"paramNodeName": "TalkStateBlendTime"}, "BlendTime3": {"paramNodeName": "ListenStateBlendTime"}, "BlendAlpha": {"paramNodeName": "BaseBlendNodeBlendAlpha"}, "BlendComplete": {"paramNodeName": "BaseBlendNodeBlendComplete"}}, "specialValue": {"blendTimes": [0.5, 0.5, 0.5, 0.5], "resetAnimOnActivation": true, "activeIndex": 0}}, {"UID": 3, "name": "DefaultAnimNode", "type": "<PERSON><PERSON><PERSON><PERSON>", "updateMode": "DisableOnZeroWeight", "paramNodeInputSlots": {"Speed": {"paramNodeName": "DefaultAnimNodeSpeed"}, "Progress": {"paramNodeName": "DefaultAnimNodeProgress"}, "ManualControlProgress": {"paramNodeName": "DefaultAnimNodeManualControlProgress"}}, "paramNodeOutputSlots": {"Speed": {"paramNodeName": "DefaultAnimNodeSpeed"}, "Progress": {"paramNodeName": "DefaultAnimNodeProgress"}, "ManualControlProgress": {"paramNodeName": "DefaultAnimNodeManualControlProgress"}, "ProgressEnd": {"paramNodeName": "DefaultAnimNodeProgressEnd"}}, "specialValue": {"speed": 1.0, "blending": "Override", "isLoop": true}}, {"UID": 4, "name": "IdleAnimNode", "type": "<PERSON><PERSON><PERSON><PERSON>", "updateMode": "DisableOnZeroWeight", "paramNodeInputSlots": {"Speed": {"paramNodeName": "IdleAnimNodeSpeed"}}, "paramNodeOutputSlots": {"Speed": {"paramNodeName": "IdleAnimNodeSpeed"}, "Progress": {"paramNodeName": "IdleAnimNodeProgress"}}, "specialValue": {"speed": 1.0, "blending": "Override", "isLoop": false}}, {"UID": 5, "name": "TalkAnimNode", "type": "<PERSON><PERSON><PERSON><PERSON>", "updateMode": "DisableOnZeroWeight", "paramNodeInputSlots": {"Speed": {"paramNodeName": "TalkAnimNodeSpeed"}}, "paramNodeOutputSlots": {"Speed": {"paramNodeName": "TalkAnimNodeSpeed"}, "Progress": {"paramNodeName": "TalkAnimNodeProgress"}}, "specialValue": {"speed": 1.0, "blending": "Override", "isLoop": false}}, {"UID": 6, "name": "ListenAnimNode", "type": "<PERSON><PERSON><PERSON><PERSON>", "updateMode": "DisableOnZeroWeight", "paramNodeInputSlots": {"Speed": {"paramNodeName": "ListenAnimNodeSpeed"}}, "paramNodeOutputSlots": {"Speed": {"paramNodeName": "ListenAnimNodeSpeed"}, "Progress": {"paramNodeName": "ListenAnimNodeProgress"}}, "specialValue": {"speed": 1.0, "blending": "Override", "isLoop": false}}, {"UID": 7, "name": "HeadBlendNode", "type": "BlendByIntWithCache", "updateMode": "DisableOnZeroWeight", "animGraphNodeInputSlots": [{"name": "Blend Pose 0", "linkNodeUID": 8, "paramNodeName": "HeadAnimNode"}], "paramNodeInputSlots": {"BlendTime0": {"paramNodeName": "HeadBlendNodeBlendTime0"}}, "paramNodeOutputSlots": {"BlendTime0": {"paramNodeName": "HeadBlendNodeBlendTime0"}}, "specialValue": {"blendTimes": [0.5], "resetAnimOnActivation": true, "activeIndex": 0}}, {"UID": 8, "name": "HeadAnimNode", "type": "<PERSON><PERSON><PERSON><PERSON>", "updateMode": "DisableOnZeroWeight", "paramNodeInputSlots": {"Speed": {"paramNodeName": "HeadAnimNodeSpeed"}}, "paramNodeOutputSlots": {"Speed": {"paramNodeName": "HeadAnimNodeSpeed"}, "Progress": {"paramNodeName": "HeadAnimNodeProgress"}}, "specialValue": {"speed": 1.0, "blending": "Override", "isLoop": true}}, {"UID": 9, "name": "ItemBlendNode", "type": "BlendByIntWithCache", "updateMode": "DisableOnZeroWeight", "animGraphNodeInputSlots": [{"name": "Blend Pose 0", "linkNodeUID": 10, "linkNodeName": "ItemAnimNode"}, {"name": "Blend Pose 1", "linkNodeUID": 11, "linkNodeName": "ItemTriggerAnimNode"}], "paramNodeInputSlots": {"ActiveIndex": {"paramNodeName": "ItemBlendNodeActiveIndex"}, "BlendTime0": {"paramNodeName": "ItemBlendNodeBlendTime0"}, "BlendTime1": {"paramNodeName": "ItemTriggerBlendNodeBlendTime0"}}, "paramNodeOutputSlots": {"ActiveIndex": {"paramNodeName": "ItemBlendNodeActiveIndex"}, "BlendTime0": {"paramNodeName": "ItemBlendNodeBlendTime0"}, "BlendTime1": {"paramNodeName": "ItemTriggerBlendNodeBlendTime0"}}, "specialValue": {"blendTimes": [0.5], "resetAnimOnActivation": true, "activeIndex": 0}}, {"UID": 10, "name": "ItemAnimNode", "type": "<PERSON><PERSON><PERSON><PERSON>", "updateMode": "DisableOnZeroWeight", "paramNodeInputSlots": {"Speed": {"paramNodeName": "ItemAnimNodeSpeed"}}, "paramNodeOutputSlots": {"Speed": {"paramNodeName": "ItemAnimNodeSpeed"}, "Progress": {"paramNodeName": "ItemAnimNodeProgress"}}, "specialValue": {"speed": 1.0, "blending": "Override", "isLoop": true}}, {"UID": 11, "name": "ItemTriggerAnimNode", "type": "<PERSON><PERSON><PERSON><PERSON>", "updateMode": "DisableOnZeroWeight", "paramNodeInputSlots": {"Speed": {"paramNodeName": "ItemTriggerAnimNodeSpeed"}}, "paramNodeOutputSlots": {"Speed": {"paramNodeName": "ItemTriggerAnimNodeSpeed"}, "Progress": {"paramNodeName": "ItemTriggerAnimNodeProgress"}}, "specialValue": {"speed": 1.0, "blending": "Override", "isLoop": true}}, {"UID": 12, "name": "SecondBlendWithMaskNode", "type": "BlendWithMask", "updateMode": "DisableOnZeroWeight", "animGraphNodeInputSlots": [{"name": "Base Pose", "linkNodeUID": 16, "linkNodeName": "BaseBlendWithMaskNode"}, {"name": "Mask Pose 0", "linkNodeUID": 7, "linkNodeName": "HeadBlendNode"}, {"name": "Mask Pose 1", "linkNodeUID": 9, "linkNodeName": "ItemBlendNode"}], "paramNodeInputSlots": {"BlendTime0": {"paramNodeName": "HeadAnimActiveBlendTime"}, "BlendTime1": {"paramNodeName": "ItemAnimActiveBlendTime"}, "Active0": {"paramNodeName": "HeadAnimActive"}, "Active1": {"paramNodeName": "ItemAnimActive"}}, "paramNodeOutputSlots": {"BlendTime0": {"paramNodeName": "HeadAnimActiveBlendTime"}, "BlendTime1": {"paramNodeName": "ItemAnimActiveBlendTime"}, "Active0": {"paramNodeName": "HeadAnimActive"}, "Active1": {"paramNodeName": "ItemAnimActive"}}, "specialValue": {"maskNames": ["HeadMask", "FullBodyMask"], "blendShapeMaskNames": ["all", "none"], "blendTimes": [0.5, 0.0], "actives": [true, true]}}, {"UID": 13, "name": "HumanProcessorNode", "type": "AnimClipExternal", "updateMode": "DisableOnZeroWeight", "specialValue": {"blending": "Override"}}, {"UID": 15, "name": "TposeAnimNode0", "type": "BindPose", "updateMode": "DisableOnZeroWeight"}, {"UID": 14, "name": "AIBS", "type": "AnimClipExternal", "updateMode": "DisableOnZeroWeight", "specialValue": {"blending": "Override"}}, {"UID": 17, "name": "FaceProcessorNode", "type": "AnimClipExternal", "updateMode": "DisableOnZeroWeight", "specialValue": {"blending": "Override"}}, {"UID": 16, "name": "BaseBlendWithMaskNode", "type": "BlendWithMask", "updateMode": "DisableOnZeroWeight", "animGraphNodeInputSlots": [{"name": "Base Pose", "linkNodeUID": 2, "linkNodeName": "BaseBlendNode"}, {"name": "Mask Pose 0", "linkNodeUID": 15, "linkNodeName": "TposeAnimNode0"}, {"name": "Mask Pose 1", "linkNodeUID": 13, "linkNodeName": "HumanProcessorNode"}, {"name": "Mask Pose 2", "linkNodeUID": 17, "linkNodeName": "FaceProcessorNode"}, {"name": "Mask Pose 3", "linkNodeUID": 14, "linkNodeName": "AIBS"}], "paramNodeInputSlots": {"BlendTime0": {"paramNodeName": "AllBoneMaskBlendTime"}, "Active0": {"paramNodeName": "AllBoneMaskActive"}, "BlendTime1": {"paramNodeName": ""}, "Active1": {"paramNodeName": ""}, "BlendTime2": {"paramNodeName": ""}, "Active2": {"paramNodeName": ""}, "BlendTime3": {"paramNodeName": ""}, "Active3": {"paramNodeName": "MaskActive"}}, "paramNodeOutputSlots": {"BlendTime0": {"paramNodeName": "AllBoneMaskBlendTime"}, "Active0": {"paramNodeName": "AllBoneMaskActive"}, "BlendTime1": {"paramNodeName": ""}, "Active1": {"paramNodeName": ""}, "BlendTime2": {"paramNodeName": ""}, "Active2": {"paramNodeName": ""}, "BlendTime3": {"paramNodeName": ""}, "Active3": {"paramNodeName": ""}}, "specialValue": {"maskNames": ["all", "all", "none", "none"], "blendShapeMaskNames": ["all", "none", "face_processor", "mouth"], "blendTimes": [0.0, 0.0, 0.0, 0.0], "actives": [false, false, false, false]}}]}