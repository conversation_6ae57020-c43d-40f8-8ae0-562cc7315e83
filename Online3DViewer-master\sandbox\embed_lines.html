<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no">

	<title>Online 3D Viewer</title>

	<script type="text/javascript" src="../build/engine_dev/o3dv.min.js"></script>

    <script type='text/javascript'>
        window.addEventListener ('load', () => {
            OV.Init3DViewerElements ();
        });
    </script>

    <style>
        iframe, div.online_3d_viewer
        {
            float: left;
            border: 1px solid #eeeeee;
            margin: 0px 4px 4px 0px;
            width: 360px;
            height: 240px;
        }
    </style>
</head>

<body>
    <iframe
        src="../../website/embed.html#model=../test/testfiles/obj/cube_with_edges.obj">
    </iframe>
    <iframe
        src="../../website/embed.html#model=../test/testfiles/obj/cube_with_edges.obj$defaultcolor=0,200,0$defaultlinecolor=0,100,0">
    </iframe>
    <iframe
        src="../../website/embed.html#model=../test/testfiles/obj/cube_with_edges.obj,../test/testfiles/obj/cube_with_edges.mtl">
    </iframe>
    <iframe
        src="../../website/embed.html#model=../test/testfiles/obj/cube_with_edges.obj,../test/testfiles/obj/cube_with_edges.mtl$defaultcolor=0,200,0$defaultlinecolor=0,100,0">
    </iframe>
    <div class="online_3d_viewer"
        model="../test/testfiles/obj/cube_with_edges.obj">
    </div>
    <div class="online_3d_viewer"
        model="../test/testfiles/obj/cube_with_edges.obj"
        defaultcolor="0,200,0"
        defaultlinecolor="0,100,0">
    </div>
    <div class="online_3d_viewer"
        model="../test/testfiles/obj/cube_with_edges.obj,../test/testfiles/obj/cube_with_edges.mtl">
    </div>
    <div class="online_3d_viewer"
        model="../test/testfiles/obj/cube_with_edges.obj,../test/testfiles/obj/cube_with_edges.mtl"
        defaultcolor="0,200,0"
        defaultlinecolor="0,100,0">
    </div>
</body>

</html>
