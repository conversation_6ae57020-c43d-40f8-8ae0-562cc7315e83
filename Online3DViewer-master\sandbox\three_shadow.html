<!DOCTYPE html>
<html>

<head>
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<meta name="viewport" content="width=device-width, user-scalable=no">

	<title>Online 3D Viewer</title>
	<style>
		canvas
		{
			border: 1px solid #cccccc;
		}
	</style>

	<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/three@0.147.0/build/three.min.js"></script>
	<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/three@0.147.0/examples/js/shaders/HorizontalBlurShader.js"></script>
	<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/three@0.147.0/examples/js/shaders/VerticalBlurShader.js"></script>
	<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/three@0.147.0/examples/js/controls/OrbitControls.js"></script>
	<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/three@0.147.0/examples/js/controls/OrbitControls.js"></script>
	<script type='text/javascript'>
		class BlurEffect
		{
			constructor (parentGroup, renderTarget, sizeX, sizeY)
			{
				this.parentGroup = parentGroup;
				this.renderTarget = renderTarget;

				this.blurGroup = new THREE.Object3D ();
				this.parentGroup.add (this.blurGroup);

				this.blurPlaneGeometry = new THREE.PlaneGeometry (sizeX, sizeY);
				this.blurPlaneMesh = new THREE.Mesh (this.blurPlaneGeometry);
				this.blurPlaneMesh.visible = false;
				this.blurGroup.add (this.blurPlaneMesh);

				this.blurCamera = new THREE.OrthographicCamera (-sizeX / 2.0, sizeX / 2.0, sizeY / 2.0, -sizeY / 2.0, 0.0, 1.0);
				this.blurGroup.add (this.blurCamera);

				this.blurRenderTarget = new THREE.WebGLRenderTarget (renderTarget.width, renderTarget.height);
				this.blurRenderTarget.texture.generateMipmaps = false;

				this.horizontalBlurMaterial = new THREE.ShaderMaterial (THREE.HorizontalBlurShader);
				this.horizontalBlurMaterial.depthTest = false;

				this.verticalBlurMaterial = new THREE.ShaderMaterial (THREE.VerticalBlurShader);
				this.verticalBlurMaterial.depthTest = false;
			}

			Render (renderer, amount)
			{
				this.blurPlaneMesh.visible = true;

				this.blurPlaneMesh.material = this.horizontalBlurMaterial;
				this.blurPlaneMesh.material.uniforms.tDiffuse.value = this.renderTarget.texture;
				this.horizontalBlurMaterial.uniforms.h.value = amount * 1 / 256;
				renderer.setRenderTarget (this.blurRenderTarget);
				renderer.render (this.blurPlaneMesh, this.blurCamera);

				this.blurPlaneMesh.material = this.verticalBlurMaterial;
				this.blurPlaneMesh.material.uniforms.tDiffuse.value = this.blurRenderTarget.texture;
				this.verticalBlurMaterial.uniforms.v.value = amount * 1 / 256;
				renderer.setRenderTarget (this.renderTarget);
				renderer.render (this.blurPlaneMesh, this.blurCamera);

				this.blurPlaneMesh.visible = false;
			}
		}

		class ShadowPlane
		{
			constructor (scene, planeXSize, planeYSize, planeElevation, frustumHeight, shadowColor, direction)
			{
				this.scene = scene;

				this.shadowGroup = new THREE.Object3D ();
				this.scene.add (this.shadowGroup);

				this.shadowRenderTarget = new THREE.WebGLRenderTarget (512, 512);
				this.shadowRenderTarget.texture.generateMipmaps = false;

				this.shadowPlaneGeometry = new THREE.PlaneGeometry (planeXSize, planeYSize)
				this.shadowPlaneMaterial = new THREE.MeshBasicMaterial ({
					map : this.shadowRenderTarget.texture,
					//color : '#00cc00',
					depthWrite : false
				});

				this.shadowPlaneMesh = new THREE.Mesh (this.shadowPlaneGeometry, this.shadowPlaneMaterial);
				this.shadowGroup.add (this.shadowPlaneMesh);

				this.shadowCamera = new THREE.OrthographicCamera (
					planeXSize / 2.0, -planeXSize / 2.0,
					-planeYSize / 2.0, planeYSize / 2.0,
					0.0, frustumHeight
				);
				this.shadowGroup.add (this.shadowCamera);

				let planePosition = null;
				let planeLookDirection = null;
				let planeScale = null;

				if (direction === 1) { // x
					planePosition = new THREE.Vector3 (-2.0, 0.0, 0.0);
					planeLookDirection = new THREE.Vector3 (1.0, 0.0, 0.0);
					planeScale = new THREE.Vector3 (1.0, 1.0, -1.0);
				} else if (direction === 2) { // y
					planePosition = new THREE.Vector3 (0.0, -2.0, 0.0);
					planeLookDirection = new THREE.Vector3 (0.0, 1.0, 0.0);
					planeScale = new THREE.Vector3 (1.0, 1.0, -1.0);
				} else if (direction === 3) { // z
					planePosition = new THREE.Vector3 (0.0, 0.0, -2.0);
					planeLookDirection = new THREE.Vector3 (0.0, 0.0, 1.0);
					planeScale = new THREE.Vector3 (1.0, -1.0, 1.0);
				}

				this.shadowPlaneGeometry.lookAt (planeLookDirection);
				this.shadowPlaneGeometry.translate (planePosition.x, planePosition.y, planePosition.z);
				this.shadowPlaneMesh.scale.set (planeScale.x, planeScale.y, planeScale.z);

				this.shadowCamera.lookAt (planeLookDirection);
				this.shadowCamera.rotation.z = Math.PI;
				this.shadowCamera.position.set (planePosition.x, planePosition.y, planePosition.z);

				this.shadowMaterial = new THREE.MeshBasicMaterial ({
					color : shadowColor
				});

				this.blur = new BlurEffect (this.shadowGroup, this.shadowRenderTarget, planeXSize, planeYSize);
			}

			Render (renderer, blurAmount)
			{
				this.scene.overrideMaterial = this.shadowMaterial;
				renderer.setRenderTarget (this.shadowRenderTarget);
				renderer.render (this.scene, this.shadowCamera);
				this.scene.overrideMaterial = null;

				this.blur.Render (renderer, blurAmount);
				this.blur.Render (renderer, blurAmount * 0.5);
				this.blur.Render (renderer, blurAmount * 0.2);
			}
		}

		function Sandbox3D ()
		{
			let canvas = document.getElementById ('canvas');

			let parameters = {
				canvas : canvas,
				antialias : true
			};

			let width = 800;
			let height = 600;
			let renderer = new THREE.WebGLRenderer (parameters);
			renderer.setClearColor ('#ffffff', 1);
			renderer.setSize (width, height);
			renderer.localClippingEnabled = true;

			let scene = new THREE.Scene ();

			let ambientLight = new THREE.AmbientLight (0x888888);
			scene.add (ambientLight);

			let light = new THREE.DirectionalLight (0x888888);
			light.position.set (5.0, 3.0, 1.5);
			scene.add (light);

			let camera = new THREE.PerspectiveCamera (45.0, width / height, 0.1, 1000.0);
			camera.position.set (5.0, 3.0, 1.5);
			camera.up.set (0.0, 1.0, 0.0);
			camera.lookAt (new THREE.Vector3 (0.0, 0.0, 0.0));
			scene.add (camera);

			let meshes = new THREE.Object3D ();
			let box1 = new THREE.BoxGeometry (0.5, 2.5, 0.75);
			let box2 = new THREE.BoxGeometry (0.5, 0.75, 1.6);
			let boxMaterial = new THREE.MeshPhongMaterial ({
				color : 0xcc0000,
				side : THREE.DoubleSide
			});
			let boxMesh1 = new THREE.Mesh (box1, boxMaterial);
			let boxMesh2 = new THREE.Mesh (box2, boxMaterial);
			boxMesh1.position.z = 1.0;
			boxMesh2.position.z = 0.3;
			meshes.add (boxMesh1);
			meshes.add (boxMesh2);
			scene.add (meshes);

			new THREE.OrbitControls (camera, renderer.domElement);

			let shadow1 = new ShadowPlane (scene, 4.0, 4.0, -2.0, 3.0, 0xcc0000, 1);
			let shadow2 = new ShadowPlane (scene, 4.0, 4.0, -2.0, 3.0, 0x00cc00, 2);
			let shadow3 = new ShadowPlane (scene, 4.0, 4.0, -2.0, 3.0, 0x0000cc, 3);

			scene.background = new THREE.Color (0.8, 0.8, 0.8);
			renderer.setAnimationLoop ((time) => {
				meshes.rotation.x = time / 3000;
				meshes.rotation.y = time / 3000;
				meshes.rotation.z = time / 3000;

				shadow1.Render (renderer, 5.0);
				shadow2.Render (renderer, 5.0);
				shadow3.Render (renderer, 5.0);

				renderer.setRenderTarget (null);
				renderer.render (scene, camera);
			});
		}

		window.onload = function () {
			Sandbox3D ();
		};
	</script>
</head>

<body>
	<canvas id="canvas"></canvas>
</body>

</html>
