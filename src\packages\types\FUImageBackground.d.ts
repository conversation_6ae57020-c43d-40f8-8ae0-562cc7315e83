import { CFloat3, ESpriteFitType } from '@/packages/types';

export class FUImageBackground {
    /**
     * @brief 更新当前图片的内容
     * @param data webassembly 中的数据地址
     * @param width 宽度
     * @param height 高度
     * @param channel 通道数
     * @param fit_type 图像形变参数, 默认为拉伸
     * @param positions 图像4个顶点的位置, 默认为{{-1.0f, -1.0f, 0.0f}, {1.0f, -1.0f, 0.0f}, {1.0f, 1.0f, 0.0f}, {-1.0f, 1.0f, 0.0f}};
     */
    update(data: number, width: number, height: number, channel: number, fit_type: ESpriteFitType, positions: CFloat3['number'], arg7: any, arg8: any): void

    /**
     * @brief 清除当前图片的内容
     */
    clear(): void
}

export class FUImageForeground {
    /**
     * @brief 更新当前图片的内容
     * @param data webassembly 中的数据地址
     * @param width 宽度
     * @param height 高度
     * @param channel 通道数
     * @param fit_type 图像形变参数, 默认为拉伸
     * @param positions 图像4个顶点的位置, 默认为{{-1.0f, -1.0f, 0.0f}, {1.0f, -1.0f, 0.0f}, {1.0f, 1.0f, 0.0f}, {-1.0f, 1.0f, 0.0f}};
     */
    update(data: number, width: number, height: number, channel: number, fit_type: ESpriteFitType, positions: CFloat3, arg7: any): void

    /**
     * @brief 清除当前图片的内容
     */
    clear(): void
}
