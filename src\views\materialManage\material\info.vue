<template>
    <el-card class="n-layout-page-header">
      <el-form :model="form" ref="formRef" label-width="80px" :rules="rules">
        <el-form-item label="资源名称" required prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入资源名称"
            maxlength="100"
            @input="(value) => handleInput('name', value)"
          />
        </el-form-item>
        <el-form-item label="素材类型" required prop="type">
          <el-select v-model="form.type">
            <el-option
              v-for="item in typeOptions"
              :key="item.name"
              :label="item.chinese"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <!-- 风格类型隐藏，使用默认值 -->
        <el-form-item label="风格类型" required prop="style_id" style="display: none;">
          <el-select v-model="form.style_id" disabled>
            <el-option
              v-for="item in styles"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="适用范围" required prop="gender">
          <el-select v-model="form.gender">
            <el-option
              label="男性"
              value="male"
            />
            <el-option
              label="女性"
              value="female"
            />
            <el-option
              label="通用"
              value="all"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="缩略图" required prop="icon_id">
          <CustomUpload
            v-model="iconInfo"
            scope="asset_icon"
            :show-file-list="false"
            list-type="picture-card"
            accept=".jpeg,.png,.jpg"
            :maxSize="4"
          >
            <template v-if="iconInfo.url">
                <img alt="图片" :src="iconInfo.url" class="avatar">
            </template>
            <template v-else>
              <el-icon size="26"><Plus /></el-icon>
              <div class="el-upload__text">
                点击或拖拽文件上传
              </div>
            </template>
            <template #tip>
                <div class="el-upload__tip">
                建议尺寸：640*320，小于4M的jpg、jpeg、png格式
                </div>
            </template>
          </CustomUpload>
        </el-form-item>
        <el-form-item label="资源文件" required prop="file_id">
          <CustomUpload
            v-model="fileInfo"
            scope="asset"
            :showFileList="false"
            accept=".bundle"
            :maxSize="50"
            @upload="(v) => {
              fileUploading = v
            }"
          >
          <div style="display: flex;flex-direction: column;align-items: center;" v-loading="fileUploading">
              <template v-if="fileInfo.url" >
                <img alt="图片" src="../../../assets/images/Bundle.png" class="avatar" style="width: 100px;height: 100px;">
                {{fileInfo.name}}
              </template>
              <template v-else>
                <el-icon size="26"><Plus /></el-icon>
                <div class="el-upload__text">
                  点击或拖拽文件上传（.fbx、.glb、.obj、.gltf、.stl、.dae、.ply文件）
                </div>
              </template>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                小于50M的.fbx、.glb、.obj、.gltf、.stl、.dae、.ply文件，最多上传1个
              </div>
            </template>
          </CustomUpload>
        </el-form-item>
        <el-form-item label="备注" required prop="description">
          <el-input
            v-model="form.description" 
            type="textarea"
            rows="6"
            maxlength="100"
            @input="(value) => handleInput('description', value)"
          />
        </el-form-item>
      </el-form>
      <el-button @click="submit">提交</el-button>
      <el-button @click="goback">返回</el-button>
    </el-card>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus'
  import type {FormInstance} from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'
  import {addAsset, getAssetTypeList, getAssetDetail, editAsset} from '@/api/material/material'
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import CustomUpload from '@/views/operator/components/CustomUpload.vue';
import { useFilterInputHander } from '@/views/hooks';

  const router = useRouter()
  const route = useRoute();
  const userStore = useUserStore();
  const { styles, defaultStyle, currentProviderId } = storeToRefs(userStore);
  const fileUploading = ref(false)
  const formRef = ref<FormInstance>()
  const form = reactive({
      name: '',
      type: '',
      style_id: '', // 初始化为空，在 onMounted 中设置
      gender: '',
      description: '',
      provider_id: '', // 初始化为空，在 onMounted 中设置
      icon_id: '',
      file_id: '',
  })
  const handleInput = useFilterInputHander(form);

  const rules = {
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' },
    ],
    type: [
      { required: true, message: '请选择类型', trigger: 'change' },
    ],
    style_id: [
      { required: true, message: '请选择风格类型', trigger: 'blur' },
    ],
    gender: [
      { required: true, message: '请选择适用范围', trigger: 'blur' },
    ],
    description: [
      { required: true, message: '请输入描述', trigger: 'blur' },
    ],
    icon_id: [
      {
        validator: (_, __, callback) => {
          if (!iconInfo.value.id) {
            callback(new Error('请上传图标'))
          } else {
            callback()
          }
        }
      }
    ],
    file_id: [
      {
        validator: (_, __, callback) => {
          if (!fileInfo.value.id) {
            callback(new Error('请上传资源文件'))
          } else {
            callback()
          }
        }
      }
    ]
  }

  const iconInfo = ref<any>({})
  const fileInfo = ref<any>({})

  const {id} = route.query
  onMounted(async () => {
    try {
      await queryMaterialType()

      // 确保风格类型和提供商ID有默认值
      if (!form.style_id) {
        const stylesArray = styles.value || [];
        const defaultStyleValue = defaultStyle.value;

        if (defaultStyleValue !== undefined) {
          form.style_id = String(defaultStyleValue);
        } else if (stylesArray.length > 0) {
          form.style_id = String(stylesArray[0]?.id || '');
        }
      }

      if (!form.provider_id && currentProviderId.value) {
        form.provider_id = String(currentProviderId.value);
      }

      if (id) {
        const res = await getAssetDetail(id)
        for (let k in form) {
          form[k] = res.data[k]
        }
        iconInfo.value = {
          url: res.data.url_icon,
          id: res.data.icon_id,
        }
        fileInfo.value = {
          name: res.data.file.originalname,
          url: res.data.url,
          id: res.data.file.id
        }
      }
    } catch (error) {
      console.error('初始化页面时出错:', error);
    }
  })

  const typeOptions = ref<any[]>([])
  async function queryMaterialType() {
    try {
      const res = await getAssetTypeList()
      if (res && res.data && Array.isArray(res.data)) {
        typeOptions.value = res.data.reduce((prev, item) => {
          return [...prev, ...(item.children || [])]
        }, [])
      }
    } catch (error) {
      console.error('获取素材类型失败:', error);
      typeOptions.value = [];
    }
  }
  watch(iconInfo, () => {
    form.icon_id = iconInfo.value.id
    formRef.value?.validateField('icon_id')
  }, {deep: true})
  watch(fileInfo, () => {
    form.file_id = fileInfo.value.id
    formRef.value?.validateField('file_id')
    if (fileInfo.value.gender) {
      form.gender = fileInfo.value.gender
    }
    // 移除自动风格选择逻辑，因为模型不再包含风格文件
    // 保持使用默认风格值
  }, {deep: true})
  async function submit() {
    console.log(form.style_id)
    if (!formRef.value) return
    formRef.value.validate(async (valid) => {
      if (valid) {
        if (id) {
          await editAsset(
            id,
            {
              ...form,
            }
          )
          ElMessage({
            message: '编辑成功！',
            type: 'success'
          })
        } else {
          await addAsset({
            ...form,
          })
          ElMessage({
            message: '新增成功！',
            type: 'success'
          })
        }
        setTimeout(() => {
          goback()
        }, 1000)
      }
    })
  }

  function goback() {
    router.push({ path: '/material/material-list'});
  }

</script>

<style lang="less" scoped>
  .upload-demo {
    :deep(.el-upload--picture-card) {
      --el-upload-picture-card-size: unset;
      .el-upload-dragger {
        height: 100%;
        .avatar {
          max-width: 250px;
          max-height: 250px;
        }
      }
    }
    :deep(.el-upload-list--picture-card) {
      min-height: 156px;
      .el-upload--picture-card:nth-child(2) {
        display: none;
      }
    }
  }
</style>
