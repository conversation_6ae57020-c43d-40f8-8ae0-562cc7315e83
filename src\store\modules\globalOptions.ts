import { defineStore } from 'pinia';
import { store } from '@/store';

import { getUserInfo as getUserInfoApi } from '@/api/system/user';

export interface IUserState {
  /**
   * 素材风格
   */
  styleOptions: any[];
}

export const useGlobalOptionsStore = defineStore({
  id: 'global-options',
  state: (): IUserState => ({
    styleOptions: [],
  }),
  getters: {
    getStyleOptions(): any[] {
      return this.styleOptions;
    },
  },
  actions: {
    // 获取风格
    async getStyleOptions() {
      const { data } = await getUserInfoApi();
      this.styleOptions = data.styles;
    },
  },
});

export function useGlobalOptions() {
  return useGlobalOptionsStore(store);
}
