# user  root;
worker_processes 1;

pcre_jit on;

error_log logs/error.log warn;

pid /var/run/nginx.pid;

# 完整URL，例如：http://127.0.0.1:5000
# 后端接口服务地址
env API_URL;

events {
    worker_connections 10240;
}

http {
    include mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
    '$status $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log logs/access.log main;
    # client_max_body_size: 1g;
    sendfile on;
    #tcp_nopush     on;

    keepalive_timeout 65;

    gzip on;

    include /etc/nginx/conf.d/*.conf;

    server {
        listen 8080;
        # server_name *.faceunity.com;
        server_name 127.0.0.1;

        root /app/public;

        location / {
            index index.html;
            try_files $uri $uri/ /index.html;
            add_header Cache-Control no-cache;
        }
        location ^~ /assets/ {
            gzip_static on;
            expires max;
            add_header Cache-Control public;
        }
        location /app.config.js {
            add_header Cache-Control no-cache;
        }
        location /favicon.ico {
            add_header Cache-Control no-cache;
        }
        location ^~ /oss/ {
            gzip_static on;
            expires max;
            alias /app/uploads/;
            add_header Cache-Control public;
        }
        # location ^~ /api/ {
        #     set_by_lua_block $proxy_pass_url {
        #         return os.getenv("API_URL") .. ngx.var.request_uri;
        #     }
        #     proxy_pass $proxy_pass_url;
        #     proxy_redirect off;
        #     add_header Cache-Control no-cache;
        #     client_max_body_size 500M;
        # }
    }
}
