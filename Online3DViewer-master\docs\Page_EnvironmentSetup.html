<!DOCTYPE html>
<html>

<head>
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<meta name="viewport" content="width=device-width, user-scalable=no">
	<link rel="icon" type="image/png" href="static/3dviewer_net_favicon.ico">

	<title>Online 3D Viewer - Environment Setup</title>

	<link rel="stylesheet" href="static/highlightjs/styles/github.min.css"/>
	<script src="static/highlightjs/highlight.min.js"></script>

    <link rel="stylesheet" type="text/css" href="static/style.css"/>
	<script type="text/javascript" src="static/script.js"></script>
</head>

<body>
<div id="navigation_toggle" class="navigation_toggle"><img id="navigation_icon" src="static/menu.svg"/></div>
<div id="navigation" class="navigation thin_scrollbar">
<div class="navigation_section">
<div class="navigation_title">Pages</div>
<div id="nav-Home" class="navigation_item"><a href="index.html" target="_self">Home</a></div>
<div id="nav-GitHub" class="navigation_item"><a href="https://github.com/kovacsv/Online3DViewer" target="_blank">GitHub</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Engine Usage</div>
<div id="nav-Installation" class="navigation_item"><a href="Page_Installation.html" target="_self">Installation</a></div>
<div id="nav-Usage" class="navigation_item"><a href="Page_Usage.html" target="_self">Usage</a></div>
<div id="nav-Migration Guide" class="navigation_item"><a href="Page_MigrationGuide.html" target="_self">Migration Guide</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Contribution</div>
<div id="nav-Contribution Guidelines" class="navigation_item"><a href="Page_ContributionGuidelines.html" target="_self">Contribution Guidelines</a></div>
<div id="nav-Environment Setup" class="navigation_item"><a href="Page_EnvironmentSetup.html" target="_self">Environment Setup</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Classes</div>
<div id="nav-Camera" class="navigation_item"><a href="Class_Camera.html" target="_self">Camera</a></div>
<div id="nav-EdgeSettings" class="navigation_item"><a href="Class_EdgeSettings.html" target="_self">EdgeSettings</a></div>
<div id="nav-EmbeddedViewer" class="navigation_item"><a href="Class_EmbeddedViewer.html" target="_self">EmbeddedViewer</a></div>
<div id="nav-EnvironmentSettings" class="navigation_item"><a href="Class_EnvironmentSettings.html" target="_self">EnvironmentSettings</a></div>
<div id="nav-InputFile" class="navigation_item"><a href="Class_InputFile.html" target="_self">InputFile</a></div>
<div id="nav-RGBAColor" class="navigation_item"><a href="Class_RGBAColor.html" target="_self">RGBAColor</a></div>
<div id="nav-RGBColor" class="navigation_item"><a href="Class_RGBColor.html" target="_self">RGBColor</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Functions</div>
<div id="nav-Init3DViewerElements" class="navigation_item"><a href="Function_Init3DViewerElements.html" target="_self">Init3DViewerElements</a></div>
<div id="nav-Init3DViewerFromFileList" class="navigation_item"><a href="Function_Init3DViewerFromFileList.html" target="_self">Init3DViewerFromFileList</a></div>
<div id="nav-Init3DViewerFromUrlList" class="navigation_item"><a href="Function_Init3DViewerFromUrlList.html" target="_self">Init3DViewerFromUrlList</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Enums</div>
<div id="nav-FileSource" class="navigation_item"><a href="Enum_FileSource.html" target="_self">FileSource</a></div>
<div id="nav-NavigationMode" class="navigation_item"><a href="Enum_NavigationMode.html" target="_self">NavigationMode</a></div>
<div id="nav-ProjectionMode" class="navigation_item"><a href="Enum_ProjectionMode.html" target="_self">ProjectionMode</a></div>
</div>

</div>
<div id="main" class="main">
<div class="page">
<h1>Environment Setup</h1>

<p>This document helps you setting up the developer environment. It is useful if you would like to contribute to the codebase of the engine or the website.</p>

<h2>Quick Start</h2>

<p>After cloning the repository run these commands from the root directory.</p>

<pre><code class="language-plaintext">npm install
npm start</code></pre>

<p>This will compile the development version of the code and start a web server. Now you can open <code class="inline">http://localhost:8080</code> to check the result.</p>

<h2>Environments</h2>

<p>The solution is implemented as ES6 modules. It means that it must be bundled together to one file to make it usable on the website. The development and production environment is separated, see the Scripts section for more details.</p>

<ul>
    <li><b>Development: </b> The development bundle will be created in the <code class="inline">build</code> folder. The code of the website references this directly during development, so you can just open the <code class="inline">website</code> folder and try it out.</li>
    <li><b>Production: </b> The entire self-containing package will be created in the <code class="inline">build</code> folder. In this case all the required files are copied to this folder so you can just upload it to any web server to make it work.</li>
</ul>

<h2>Source Structure</h2>

<p>The source contains the following folders.</p>

<ul>
    <li><b>assets:</b> Various assets used in the code or in any other media platforms.</li>
    <li><b>docs:</b> Code of this documentation.</li>
    <li><b>sandbox:</b> Various examples mostly for using the engine.</li>
    <li><b>source:</b> Source code of the engine and the website.
        <ul>
            <li><b>engine:</b> Source code of the engine. It doesn't contain any website specific codes.</li>
            <li><b>website:</b> Source code of the website.</li>
        </ul>
    </li>
    <li><b>test:</b> Automatic tests for the engine.</li>
    <li><b>tools:</b> Tools for building the library.</li>
    <li><b>website:</b> Static resources for the website.</li>
</ul>

<h2>Scripts</h2>

<p>Several scripts are available for building, testing and publishing.</p>

<p><b>Building</b></p>

<ul>
    <li><code class="inline">npm run build_dev</code>: Builds the development version of the engine and the website.</li>
    <li><code class="inline">npm run build_engine_dev</code>: Builds the development version of the engine.</li>
    <li><code class="inline">npm run build_engine</code>: Builds the production version of the engine.</li>
    <li><code class="inline">npm run build_engine_module</code>: Builds the ES6 module version of the engine. This is the main entry point when using the npm package.</li>
    <li><code class="inline">npm run build_website_dev</code>: Builds the development version of the website.</li>
    <li><code class="inline">npm run build_website</code>: Builds the production version of the website.</li>
</ul>

<p><b>Testing</b></p>

<ul>
    <li><code class="inline">npm run test</code>: Runs all unit tests.</li>
    <li><code class="inline">npm run lint</code>: Runs eslint on the codebase.</li>
</ul>

<p><b>Packaging</b></p>

<ul>
    <li><code class="inline">npm run create_package</code>: Generates the production version of the engine and the website.</li>
    <li><code class="inline">npm run create_dist</code>: Generates the production version of the engine and the website, and runs all the possible checks.</li>
</ul>

<p><b>Tools</b></p>

<ul>
    <li><code class="inline">npm run generate_docs</code>: Generates this documentation.</li>
    <li><code class="inline">npm run generate_icon_font</code>: Generates the icon font used on the website.</li>
</ul>

</div>
</div>
<script type="text/javascript">Init ('Environment Setup')</script>
</body>

</html>
