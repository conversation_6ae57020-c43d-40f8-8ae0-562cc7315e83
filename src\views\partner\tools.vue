<template>
  <el-card class="box-card">
    <h2>工具详情</h2>
    <template v-if="config.toolConfig.length">
      <div class="wrapper p-5 m-5" :key="item.fileurl" v-for="item of config.toolConfig">
        <h4>{{ item.name }}</h4>
        <div v-if="item.fileurl" class="m-3 font-medium"
          ><span>工具下载地址：</span
          ><a :href="item.fileurl" target="_blank" rel="noopenner noreferrer">{{ item.filename }}</a>
        </div>
        <div v-if="item.description" class="m-3 font-medium">
          <span>工具说明：</span><span>{{ item.description }}</span>
        </div>
      </div>
    </template>
    <div v-else class="text-center">暂无数据</div>
  </el-card>
  <el-card class="box-card mt-2">
    <div class="flex justify-between">
      <h2>基础模型</h2>
      <el-form-item label="风格类型：" class="w-60 flex items-center" label-width="120px">
        <el-select v-model="config.seletedStyleId" placeholder="">
          <el-option
            v-for="option in config.modelStyles"
            :key="option.id"
            :label="option.title"
            :value="option.id"
          />
        </el-select>
      </el-form-item>
    </div>
    <div class="box flex flex-wrap" v-if="modelList.length > 0">
      <div class="item-box flex m-3 p-5" :key="model.style_id" v-for="model in modelList">
        <img alt="图片" :src="DocIcon" class="w-12 h-12" />
        <div class="box flex flex-col ml-3">
          <div>{{ model.name }}</div>
          <el-button
            type="primary"
            :text="true"
            @click="() => handleDownload(model.fileurl, model.name)"
            class=""
            >点击下载</el-button
          >
        </div>
      </div>
    </div>
    <div v-else class="text-center">暂无数据</div>
  </el-card>
</template>
<script lang="ts" setup>
  import { getAbilityProviderTool } from '@/api/ability-provider';
  import DocIcon from '@/assets/icons/doc.svg';
  import { downloadByUrl } from '@/utils/downloadFile';
  import { useBrowserLocation } from '@vueuse/core';
  import { ElMessage } from 'element-plus';
  import { computed, onMounted, reactive } from 'vue';
  import { useRoute } from 'vue-router';

  const route = useRoute();
  const loaction = useBrowserLocation();

  const config = reactive<{
    modelConfig: Array<{ name: string; style_id: number; fileurl: string }>;
    modelStyles: Array<{ id: number; title: string }>;
    toolConfig: Array<{ fileurl: string; name: string; filename: string; description: string }>;
    seletedStyleId: number;
  }>({
    modelConfig: [],
    modelStyles: [{ id: 0, title: '请求选择风格类型' }],
    toolConfig: [],
    seletedStyleId: 0,
  });

  const fetchDetail = async (id) => {
    const { code, data } = await getAbilityProviderTool(id);
    if (code === 0) {
      const { modelConfig, modelStyles, toolConfig } = data;
      config.modelConfig = modelConfig;
      config.toolConfig = toolConfig;
      config.modelStyles = modelStyles;
      if (config.modelStyles.length) {
        config.seletedStyleId = config.modelStyles[0].id;
      }
    }
  };

  const modelList = computed(() => {
    return config.modelConfig.filter((item) => item.style_id === config.seletedStyleId);
  });

  const handleDownload = async (url, fileName) => {
    try {
      const origin = loaction.value.origin;
      await downloadByUrl({ url: `${origin}${url}`, fileName });
      ElMessage({
        showClose: true,
        message: `${fileName}下载成功`,
        type: 'success',
      });
    } catch (e) {
      ElMessage({
        showClose: true,
        message: `${fileName}下载失败！`,
        type: 'error',
      });
    }
  };

  onMounted(async () => {
    const id = route.query.id;
    if (id) {
      await fetchDetail(id);
    }
  });
</script>
