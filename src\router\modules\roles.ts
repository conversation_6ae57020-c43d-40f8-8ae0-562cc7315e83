import { Layout } from '@/router/constant';
import { renderIcon } from '@/utils/index';
import { PERMISSION_KEYS } from './audit';
import RoleIcon from '@/components/menu-icons/super/role.vue';

/**
 * @param name 路由名称, 必须设置,且不能重名
 * @param meta 路由元信息（路由附带扩展信息）
 * @param redirect 重定向地址, 访问这个路由时,自定进行重定向
 * @param meta.disabled 禁用整个菜单
 * @param meta.title 菜单名称
 * @param meta.icon 菜单图标
 * @param meta.keepAlive 缓存该路由
 * @param meta.sort 排序越小越排前
 *
 * */
const routes: Array<any> = [
  {
    path: '/role',
    name: 'Role',
    redirect: '/role',
    component: Layout,
    meta: {
      title: '角色管理',
      icon: renderIcon(RoleIcon),
      sort: 1,
    },
    auth: [PERMISSION_KEYS.super.role_read],
    children: [
      {
        path: '',
        name: 'role-list',
        meta: {
          title: '',
        },
        component: () => import('@/views/system/role/role.vue'),
      },
      {
        path: 'add',
        name: 'role-add',
        meta: {
          title: '角色新增',
          activeMenu: 'role-list',
          hidden: true,
        },
        component: () => import('@/views/system/role/info.vue'),
      },
      {
        path: 'edit',
        name: 'role-edit',
        meta: {
          title: '角色编辑',
          activeMenu: 'role-list',
          hidden: true,
        },
        component: () => import('@/views/system/role/info.vue'),
      },
    ],
  },
];

export default routes;
