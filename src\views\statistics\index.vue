<template>
  <el-card class="box-card">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClickTab">
      <el-tab-pane label="素材" :name="0" />
      <el-tab-pane label="预置形象" :name="1" />
      <el-tab-pane label="背景音乐" :name="2" />
    </el-tabs>
    <cp-statistics-table :resourceType="activeName" />
  </el-card>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import CpStatisticsTable from './cp-statistics-table.vue';

  const activeName = ref(0);

  const handleClickTab = (tab) => {
    activeName.value = tab.props.name;
  };
</script>
