import { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/router/constant';
import { ProjectOutlined } from '@vicons/antd';
import { renderIcon } from '@/utils/index';
import { PERMISSION_KEYS } from './audit';

const routes: Array<any> = [
    {
        path: '/welcome',
        name: 'welcome',
        redirect: '/welcome/page',
        component: Layout,
        meta: {
            // title: '欢迎登录',
            sort: 1,
            isRoot: true,
            hidden: true,
            icon: renderIcon(ProjectOutlined),
        },
        children: [
            {
                path: 'page',
                name: 'welcomepage',
                meta: {
                    title: '欢迎登录',
                },
                component: () => import('@/views/welcome/index.vue'),
            },
        ],
    },
];

export default routes;
