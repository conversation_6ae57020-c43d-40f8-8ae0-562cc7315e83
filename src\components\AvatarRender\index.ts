import AvatarRender from './AvatarRender.vue';
import * as api from "@/packages/api/index";
import { FUResourceManager } from "@/packages/sdk/fu_resource_manager";
import FURenderKitSDK from "@/packages/sdk/fu_render_kit"
import Socket from "@/packages/sdk/socket";
import { Talk } from '@/packages/sdk/talk';

export const RESOURCE_MANAGER = new FUResourceManager({
    cacheLimit: 10,
    api,
    preferIDBFS: !window.location.search.includes("disable_cache"),
    maxHistoryLength: 10,
});

export const RENDER_SDK = new FURenderKitSDK({
    resourceManager: RESOURCE_MANAGER
})

export const SOCKET_INS = new Socket()

export const TALK_INS = new Talk({
    PCMConfig: {
      encoding: "16bitInt", // PCM 格式
      channels: 1, // 声道数
      sampleRate: 16000, // 采样率
    },
    locateFile: (path) => {
      return path?.startsWith('/') ? `${import.meta.env.VITE_PUBLIC_PATH}${path}` : `${import.meta.env.VITE_PUBLIC_PATH}/${path}`
    },
    manul: false, // 是否手动控制播放启动
  })

export { AvatarRender };
