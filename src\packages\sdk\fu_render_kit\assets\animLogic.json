{"MetaData": {"Generator": "NamaEngine v1.0.0.", "CreateTime": "2021-12-22 17:51:12", "Version": "*******"}, "animLogicNodes": [{"name": "DefaultState", "executeOrder": 0, "type": "<PERSON><PERSON><PERSON>", "animGraphNodes": {"animNodeAnimClipName": "DefaultAnimNode", "blendNodeBlendByIntWithCacheName": "BaseBlendNode"}}, {"name": "IdleState", "executeOrder": 0, "type": "Random", "animGraphNodes": {"animNodeAnimClipName": "IdleAnimNode", "blendNodeBlendByIntWithCacheName": "BaseBlendNode"}}, {"name": "TalkState", "executeOrder": 0, "type": "Random", "animGraphNodes": {"animNodeAnimClipName": "TalkAnimNode", "blendNodeBlendByIntWithCacheName": "BaseBlendNode"}}, {"name": "ListenState", "executeOrder": 0, "type": "RandomGroup", "animGraphNodes": {"animNodeAnimClipName": "ListenAnimNode", "blendNodeBlendByIntWithCacheName": "BaseBlendNode"}}, {"name": "HeadAnims", "executeOrder": 0, "type": "<PERSON><PERSON><PERSON>", "animGraphNodes": {"animNodeAnimClipName": "HeadAnimNode", "blendNodeBlendByIntWithCacheName": "HeadBlendNode"}}, {"name": "ItemAnims", "executeOrder": 0, "type": "<PERSON><PERSON><PERSON>", "animGraphNodes": {"animNodeAnimClipName": "ItemAnimNode", "blendNodeBlendByIntWithCacheName": "ItemBlendNode"}}, {"name": "ItemTriggerAnims", "executeOrder": 0, "type": "<PERSON><PERSON><PERSON>", "animGraphNodes": {"animNodeAnimClipName": "ItemTriggerAnimNode", "blendNodeBlendByIntWithCacheName": "ItemBlendNode"}}]}