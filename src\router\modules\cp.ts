import { Layout } from '@/router/constant';
import { renderIcon } from '@/utils/index';
import { PERMISSION_KEYS } from './audit';
import PartnerIcon from '@/components/menu-icons/cp/partner.vue';
import BgmIcon from '@/components/menu-icons/cp/bgm.vue';
import OverviewIcon from '@/components/menu-icons/cp/overview.vue';
import StatisticIcon from '@/components/menu-icons/cp/statistics.vue';

const routes: Array<any> = [
  {
    path: '/cp/statistics',
    // name: 'cp-statistics',
    component: Layout,
    meta: {
      sort: 10,
      isRoot: true,
      activeMenu: 'cp-statistics-list',
      icon: renderIcon(StatisticIcon),
    },
    auth: [PERMISSION_KEYS.cpuser.asset_refs4cp],
    children: [
      {
        path: '',
        name: `cp-statistics-list`,
        meta: {
          title: '数据统计',
          activeMenu: 'cp-statistics-list',
        },
        component: () => import('@/views/statistics/index.vue'),
      },
    ],
  },
  {
    path: '/cp/overview',
    component: Layout,
    meta: {
      sort: 10,
      isRoot: true,
      activeMenu: 'cp-overview',
      icon: renderIcon(OverviewIcon),
    },
    auth: [PERMISSION_KEYS.cpuser.asset_overview4cp],
    children: [
      {
        path: '',
        name: `cp-overview`,
        meta: {
          title: '资产概览',
          activeMenu: 'cp-overview',
        },
        component: () => import('@/views/overview/index.vue'),
      },
    ],
  },
  {
    path: '/cp/partner',
    redirect: 'cp/partner/list',
    component: Layout,
    meta: {
      sort: 1,
      isRoot: true,
      activeMenu: 'cp-partner-list',
      icon: renderIcon(PartnerIcon),
    },
    auth: [PERMISSION_KEYS.cpuser.provider_use],
    children: [
      {
        path: 'list',
        name: 'cp-partner-list',
        meta: {
          title: '查看合作伙伴',
          sort: 1,
          activeMenu: 'cp-partner-list',
        },
        component: () => import('@/views/partner/cp-partner-list.vue'),
      },
      {
        path: 'document',
        name: 'cp-document',
        meta: {
          title: '查看技术规范',
          hidden: true,
          activeMenu: 'cp-partner-list',
        },
        component: () => import('@/views/partner/document.vue'),
      },
      {
        path: 'tools',
        name: 'cp-tools',
        meta: {
          title: '查看工具',
          hidden: true,
          activeMenu: 'cp-partner-list',
        },
        component: () => import('@/views/partner/tools.vue'),
      },
    ],
  },
  {
    path: '/bgm',
    component: Layout,
    redirect: '/bgm/list',
    meta: {
      sort: 9,
      isRoot: true,
      activeMenu: 'bgm-list',
      icon: renderIcon(BgmIcon),
    },
    auth: [PERMISSION_KEYS.cpuser.asset_bgm_read],
    children: [
      {
        path: 'list',
        name: 'bgm-list',
        meta: {
          title: '背景音乐管理',
          activeMenu: 'bgm-list',
        },
        auth: [PERMISSION_KEYS.cpuser.asset_bgm_read],
        component: () => import('@/views/bgm/cp-bgm-list.vue'),
      },
      {
        path: 'add',
        name: `bgm-add`,
        meta: {
          title: '新增音乐',
          hidden: true,
          activeMenu: 'bgm-list',
        },
        auth: [PERMISSION_KEYS.cpuser.asset_bgm_read],
        component: () => import('@/views/bgm/addMusic.vue'),
      },
      {
        path: 'edit',
        name: `bgm-edit`,
        meta: {
          title: '编辑音乐',
          hidden: true,
          activeMenu: 'bgm-list',
        },
        auth: [PERMISSION_KEYS.cpuser.asset_bgm_read],
        component: () => import('@/views/bgm/addMusic.vue'),
      },
    ],
  },
];

export default routes;
