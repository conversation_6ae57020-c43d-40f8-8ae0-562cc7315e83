import { http } from '@/utils/http/axios';

/**
 * @description: CP查看统计
 */
export function getStatisticsData(params = { page: 1, limit: 10, keyword: '', provider_id: '' }) {
  return http.request({
    url: '/business/statistic/refs4cp',
    method: 'GET',
    params,
  });
}

/**
 * @description: CP查看各类资产总量
 */
export function getStatisticsTotal(params) {
  return http.request({
    url: '/business/statistic/total4cp',
    method: 'GET',
    params,
  });
}

/**
 * @description: 审核员查看资产总量
 */
export function getAuditStatisticsTotal() {
  return http.request({
    url: '/business/statistic/total4audit',
    method: 'GET',
  });
}

/**
 * @description: CP查看资源数量变化
 */
export function getSumday(params) {
  return http.request({
    url: '/business/statistic/sumday4cp',
    method: 'GET',
    params,
  });
}

/**
 * @description: 审核员查看数据统计
 */
export function getAuditStatistic(params) {
  return http.request({
    url: '/business/statistic/refs4audit',
    method: 'GET',
    params,
  });
}

/**
 * @description: 审核人员查看资产
 */
export function getAuditOverviewTop10(params) {
  return http.request({
    url: '/business/statistic/overview4auditTop10',
    // /api/business/statistic/overview4cpTop10
    method: 'GET',
    params,
  });
}

/**
 * @description: CP查看资产Top10
 */
export function getOverviewTop10(params) {
  return http.request({
    url: '/business/statistic/overview4cpTop10',
    method: 'GET',
    params,
  });
}

/**
 * @description: 审核人员查看资产使用次数趋势图
 */
export function getAuditOverviewTrendLine(params) {
  return http.request({
    url: '/business/statistic/overview4auditLine',
    method: 'GET',
    params,
  });
}

/**
 * @description: 审核人员查看详情
 */
export function getAuditDetail(params) {
  return http.request({
    url: '/business/statistic/refs4audit/detail',
    method: 'GET',
    params,
  });
}

/**
 * @description: CP查看详情
 */
export function getCpDetail(id, params) {
  return http.request({
    url: `/business/statistic/refs4cp/${id}`,
    method: 'GET',
    params,
  });
}
