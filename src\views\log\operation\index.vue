<template>
  <el-card class="box-card">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClickTab">
      <el-tab-pane label="操作日志" :name="1" />
      <el-tab-pane label="操作行为统计" :name="2" />
    </el-tabs>
    <component :is="activeName == 1 ? OperationLog : OperationBehavior" />
  </el-card>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import OperationLog from './operation-log.vue';
  import OperationBehavior from './operation-behavior.vue';

  const activeName = ref(1);

  const handleClickTab = (tab) => {
    activeName.value = tab.props.name;
  };
</script>
