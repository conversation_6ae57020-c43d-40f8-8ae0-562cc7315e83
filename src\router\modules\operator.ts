import { Layout } from '@/router/constant';
import { renderIcon } from '@/utils/index';
import { PERMISSION_KEYS } from './audit';
import ConfigIcon from '@/components/menu-icons/operator/config.vue';

const routes: Array<any> = [
  {
    path: '/operator',
    redirect: '/operator/config',
    // name: 'operator',
    component: Layout,
    meta: {
      // title: '运营配置',
      sort: 1,
      isRoot: true,
      activeMenu: 'operator_config',
      icon: renderIcon(ConfigIcon),
    },
    auth: [PERMISSION_KEYS.operator.asset_sync],
    children: [
      {
        path: 'config',
        name: 'operator_config',
        meta: {
          title: '运营配置',
          activeMenu: 'operator_config',
        },
        component: () => import('@/views/operator/config.vue'),
      },
    ],
  },
];

export default routes;
