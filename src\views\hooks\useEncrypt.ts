import { getCurrentInstance } from 'vue';

export default function useEncrypt() {
  const globalProperties: any = getCurrentInstance()?.appContext.config.globalProperties;
  return (originParams = {}) => {
    const params = Object.keys(originParams).reduce((pre, cur) => {
      const value = originParams[cur];
      if (value === null || value === undefined) {
        return pre;
      }
      pre[cur] = globalProperties.encryptValue(String(value));
      return pre;
    }, {});
    // console.log('originParams:', originParams);
    // console.log('params:', params);
    return params;
  };
}
