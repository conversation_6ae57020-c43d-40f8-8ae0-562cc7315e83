<template>
  <wrap-form title="选择能力伙伴" submitText="确定" @submit="handleSubmit">
    <el-form ref="formRef" size="large" :model="formValue" :rules="rules">
      <el-form-item path="seleted">
        <div class="flex flex-col w-full">
          <el-select class="w-full" v-model="formValue.selected">
            <el-option
              v-for="option in providers"
              :key="option.id"
              :label="option.title"
              :value="option.id"
            />
          </el-select>
          <p>您可以在右上角选择要切换的能力供应商哦～</p>
        </div>
      </el-form-item>
    </el-form>
  </wrap-form>
</template>

<script setup lang="ts">
  import WrapForm from '@/components/wrap-form';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import { onMounted, reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import { useTimeoutFn } from '@vueuse/core';

  const router = useRouter();
  const formRef = ref();

  const userStore = useUserStore();

  const { currentProviderId, providers, isExpired } = storeToRefs(userStore);

  onMounted(async () => {
    await userStore.getProviders();
    formValue.selected = currentProviderId.value ?? '';
  });

  const rules = {
    selected: {
      required: true,
      message: '请选择能力合作伙伴',
      trigger: 'blur',
    },
  };

  const formValue = reactive({
    selected: currentProviderId?.value ?? '',
  });

  const handleSubmit = async () => {
    const providerId = Number(formValue.selected);
    const result = await userStore.selectProvider(providerId);
    if (result) {
      router.push('/');
      if (isExpired.value) {
        useTimeoutFn(
          () =>
            ElMessage({
              type: 'warning',
              message: '您的密码长时间未更新，为保证账户安全，请及时修改密码！',
              duration: 3000,
              showClose: true,
            }),
          1000
        );
      }
    }
  };
</script>
