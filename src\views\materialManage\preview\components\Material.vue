<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-change="pause()">
    <el-tab-pane label="背景" name="first">
      <div class="animation-wrapper">
        <template v-for="bg in backgroundList">
          <div :class="'animation-item ' + (bg.file_path === backgroundPath && 'active')">
            <img
              class="animation-icon"
              :src="bg.url_icon || 'src/views/materialManage/preview/components/empty_icon.png'"
              alt=""
              @click="() => changeItem('background', bg)"
            />
            <p class="animation-title">{{ bg.name }}</p>
          </div>
        </template>
      </div>
    </el-tab-pane>
    <el-tab-pane label="前景" name="second">
      <div class="animation-wrapper">
        <template v-for="fg in foregroundList">
          <div :class="'animation-item ' + (fg.file_path === foregroundPath && 'active')">
            <img
              class="animation-icon"
              :src="fg.url_icon || 'src/views/materialManage/preview/components/empty_icon.png'"
              alt=""
              @click="() => changeItem('foreground', fg)"
            />
            <p class="animation-title">{{ fg.name }}</p>
          </div>
        </template>
      </div>
    </el-tab-pane>
    <el-tab-pane label="音乐" name="third">
      <div class="music-wrapper">
        <template v-for="music in musicList">
          <div :class="'music-item ' + (music.url_bgm === musicUrl && 'active')" @click="() => changeBgm(music)">
            <div class="music-icon" :style="{backgroundImage: `url(${music.url_bgm_icon})`}">
              <div class="btn">
                <img src="./pause.png" alt="" v-if="currentBgm === music.url_bgm && isPlaying"
                  @click.stop="() => handleTryListen(music)">
                <img src="./play.png" alt="" v-else @click.stop="() => handleTryListen(music)">
              </div>
            </div>
            <p class="music-title">{{ music.name }}</p>
          </div>
        </template>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>
<script lang="ts" setup>
import { getAssetBgmList } from '@/api/bgm';
import { getSyncAssetList } from '@/api/material/material';
import { PERMISSION_KEYS, useUserStore } from '@/store/modules/user';
import { storeToRefs } from 'pinia';
import { computed, onMounted, ref } from 'vue';
import { EAssetStatus } from '../../avatar/columns';
import { useRoute } from 'vue-router';
import { usePermission } from '@/hooks/web/usePermission';
import { inject } from 'vue';
import { Ref } from 'vue';

const route = useRoute();
const userStore = useUserStore();
const { currentProviderId } = storeToRefs(userStore);
const props = defineProps(['resetAvatar', 'gender', 'background', 'avatar', 'foreground', 'music', 'musicId']);
const emit = defineEmits(['updateBackground', 'updateForeground', 'updateMusic', 'updateMusicId']);
const currentBgm = inject('currentBgm') as Ref<string>
const isPlaying = inject('isPlaying') as any
const setSrc = inject('setSrc') as any
const pause = inject('pause') as any
const activeName = ref('first')
const backgroundPath = computed({
  get() {
    console.log('backgroundPath', props.background)
    return props.background;
  },
  set(value) {
    emit('updateBackground', value);
  },
});

const foregroundPath = computed({
  get() {
    return props.foreground;
  },
  set(value) {
    emit('updateForeground', value);
  },
});

const musicUrl = computed({
  get() {
    console.log('props', props)
    return props.music;
  },
  set(value) {
    emit('updateMusic', value)
  }
})

const musicId = computed({
  get() {
    return props.musicId
  },
  set(value) {
    emit('updateMusicId', value)
  }
})

const backgroundList = ref<any[]>([])
const foregroundList = ref<any[]>([])
const musicList = ref<any[]>([])
const { hasPermission } = usePermission();
onMounted(async () => {
  if (hasPermission([PERMISSION_KEYS.cpuser.asset_read]) && route.query.styleId) {
    const res = await getSyncAssetList({
      style_id: route.query.styleId,
      type: 'background,foreground',
      page: 1,
      limit: 999,
    })
    const arr1: any[] = []
    const arr2: any[] = []
    res.data.rows.forEach(i => {
      if (i.bundleType === 'background') {
        arr1.push(i)
      } else {
        arr2.push(i)
      }
    })
    backgroundList.value = arr1
    foregroundList.value = arr2
  
    const res1 = await getAssetBgmList({
      provider_id: currentProviderId.value,
      page: 1,
      limit: 999,
      status: EAssetStatus.Published
    })
    musicList.value = res1.data.rows
  }
})

const changeItem = async (type, item) => {
  if (type === 'background') {
    if (backgroundPath.value === item.file_path) {
      backgroundPath.value = ''
    } else {
      backgroundPath.value = item.file_path
    }
  } else {
    if (foregroundPath.value === item.file_path) {
      foregroundPath.value = ''
    } else {
      foregroundPath.value = item.file_path
    }
  }
}

const changeBgm = (music) => {
  console.log('music', music)
  if (musicUrl.value === music.url_bgm) {
    musicUrl.value = ''
    musicId.value = ''
  } else {
    musicUrl.value = music.url_bgm
    musicId.value = music.id
  }
}

const handleTryListen = (row) => {
  console.log('isPlaying.value', isPlaying.value)
  if (isPlaying.value) {
    if (currentBgm.value === row.url_bgm) {  
      pause();
    } else {
      setSrc(row.url_bgm, { autoPlay: true });
    }
  } else {
    setSrc(row.url_bgm, { autoPlay: true });
  }
};

</script>
<style lang="less" scoped>
.demo-tabs {
  padding: 8px 16px;
}

.animation-wrapper,
.music-wrapper {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  width: calc(100% + 20px);
  max-height: calc(100vh - 250px);
  overflow: auto;
  &:empty {
    &::before {
      content: '暂无数据';
      text-align: center;
      font-size: 24px;
      width: 100%;
      line-height: 40px;
      opacity: .6;
    }
  }
}

.animation-item {
  width: 138px;

  &.active {
    .animation-icon {
      border: 2px solid rgba(64, 126, 255, 1);
    }
  }

  .animation-icon {
    width: 100%;
    height: 138px;
    border: 2px solid rgba(64, 126, 255, 0);
    border-radius: 6px;
    cursor: pointer;
    object-fit: contain;
  }

  .animation-title {
    width: 100%;
    text-wrap: nowrap;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.music-wrapper {
  flex-direction: row;
}

.music-item {
  display: flex;
  width: calc(50% - 8px);
  overflow: hidden;
  padding: 16px 32px;
  background: rgba(249, 249, 248, 1);
  border: 2px solid rgba(64, 126, 255, 0);
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  gap: 16px;
  border-radius: 8px;
  cursor: pointer;

  &.active {
    border: 2px solid rgba(64, 126, 255, 1);
  }

  .music-icon {
    width: 60px;
    height: 60px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    flex-shrink: 0;

    .btn {
      display: none;
      background: rgba(0, 0, 0, .5);
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      justify-content: center;
      align-items: center;

      img {
        width: 30px;
        height: 30px;
        cursor: pointer;
      }
    }

    &:hover {
      .btn {
        display: flex
      }
    }
  }

  .music-title {
    flex-grow: 1;
    text-wrap: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
