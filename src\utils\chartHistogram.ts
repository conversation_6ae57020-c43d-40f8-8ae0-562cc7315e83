export default function chartHistogram (id, chart, options, dataArr) {
  // let colorArr = dataArr.map(item => {
  //   return item.color
  // })
  // let { total, title } = options
  let option = {
      xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
          type: 'value'
      },
      series: [{
          data: [100, 150, 120, 90, 50, 130, 110],
          type: 'bar'
      }]
  };
  let dom = document.getElementById(id)
  if (!chart) chart = echarts.init(dom)
  chart.setOption(option, true)
  return chart
  }