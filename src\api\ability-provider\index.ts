import { http } from '@/utils/http/axios';

/**
 * @description: 能力伙伴列表
 */
export function getAbilityProviderList(params = { page: 1, limit: 10, keyword: '' }) {
  return http.request({
    url: '/business/ability-provider',
    method: 'GET',
    params,
  });
}

/**
 * @description: 能力伙伴列表(简)
 */
export function getProviderList() {
  return http.request({
    url: '/business/ability-provider/list/all',
    method: 'GET',
  });
}

/**
 * @description: 合作伙伴的风格列表
 */
export function getProviderStyleList(id) {
  return http.request({
    url: `/business/provider-style/list/${id}`,
    method: 'GET',
  });
}

/**
 * @description: 新增伙伴列表
 */
export function createAbilityProvider(params) {
  return http.request({
    url: '/business/ability-provider',
    method: 'POST',
    params,
  });
}

/**
 * @description: 能力伙伴详情
 */
export function getAbilityProvider(id) {
  return http.request({
    url: `/business/ability-provider/${id}`,
    method: 'GET',
  });
}

/**
 * @description: 编辑伙伴
 */
export function editAbilityProvider(id, params) {
  return http.request({
    url: `/business/ability-provider/${id}`,
    method: 'POST',
    params,
  });
}

/**
 * @description: 删除伙伴
 */
export function deleteAbilityProvider(id) {
  return http.request({
    url: `/business/ability-provider/${id}/delete`,
    method: 'post',
  });
}

/**
 * @description: 工具详情
 */
export function getAbilityProviderTool(id) {
  return http.request({
    url: `/business/ability-provider/tool/${id}`,
    method: 'GET',
  });
}

/**
 * @description: 技术文档
 */
export function getAbilityProviderDocument(id) {
  return http.request({
    url: `/business/ability-provider/document/${id}`,
    method: 'GET',
  });
}
