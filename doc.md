# AvatarX 项目说明

- build 项目配置脚本
- mock 模拟数据
- openresty ningx部署配置
- public 公共静态文件
- src 源码目录
  - api 接口
  - assets 图片等资源文件
  - components 公共组件
  - config 站点配置
  - directives  vue自定义指令
  - enums 公用枚举值
  - layout 布局组件
  - store Pinia 共享容器
  - styles 公共样式文件
  - utils 工具函数
  - router 路由文件
    - index.ts 路由汇总文件
    - guards.ts 路由拦截文件
    - modules 子路模块
      - audit.ts 审核员相关路由
      - cp.ts CP人员相关路由
      - partner.ts 合作伙伴管理相关路由
      - operator.ts 运营管理相关路由
  - views 页面级组件
    - backgroundMusic 背景音乐
    - login 登陆页
    - manage 管理页面
    - operator 运营管理相关页面
    - partner 合作伙伴管理学相关页面
    - redirect 页面重定向组件
    - register 注册页面
    - reset 密码重置页面
    - setting 配置页面
    - statistics 数据统计相关页面
    - system 系统管理相关页面
    - App.vue 应用级组件
    - main.ts 主入口文件
- .env 环境配置相关文件
- dockerfile Docker配置文件
- tsconfig.json TypeScript配置文件
- vite.config.js Vite配置文件
- tailwind.config.js Tailwind配置文件
- postcss.config.js Postcss 配置文件
- package.json 项目依赖
