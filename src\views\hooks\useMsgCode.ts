import { getMsgCode } from '@/api/system/user';
import { ElMessage } from 'element-plus';
import useEncrypt from './useEncrypt';
import useCaptch from './useCaptch';

export default function useMsgCode(formRef) {
  const encrypt = useEncrypt();
  const { captchaRef, fetchCaptch } = useCaptch();

  const getMsgCodeAction = async ({ phone, image_code, type }, { auto = false } = {}) => {
    try {
      const params: { phone?: string; image_code?: string; type: number } = {
        type,
        image_code,
        phone,
      };
      try {
        await formRef.value?.validateField(['phone', 'image_code']);
      } catch (e) {
        ElMessage({
          type: 'error',
          message: '请输入图片验证码和手机号码！',
        });
        throw e;
      }
      const { code, message: msg, data } = await getMsgCode(encrypt(params));
      if (code === 0) {
        ElMessage({
          type: 'success',
          message: '验证码发送成功！',
        });
        if (auto) {
        }
      } else {
        ElMessage({
          type: 'error',
          message: `${msg ?? '验证码发送失败!'}`,
        });
        throw new Error(msg);
      }
      return typeof data === 'string' ? data : '';
    } catch (e: any) {
      fetchCaptch()
      throw e;

    }
  };

  return {
    captchaRef,
    fetchCaptch,
    getMsgCodeAction,
  };
}
