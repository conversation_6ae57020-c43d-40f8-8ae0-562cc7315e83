ISO-10303-21;
HEADER;
FILE_DESCRIPTION((''),'2;1');
FILE_NAME('AS1_PE_ASM','2008-09-04T',('mmeadows'),(''),
'PRO/ENGINEER BY PARAMETRIC TECHNOLOGY CORPORATION, 2008340',
'PRO/ENGINEER BY PARAMETRIC TECHNOLOGY CORPORATION, 2008340','');
FILE_SCHEMA((
'AP203_CONFIGURATION_CONTROLLED_3D_DESIGN_OF_MECHANICAL_PARTS_AND_ASSEMBLIES_MIM_LF'));
ENDSEC;
DATA;
#16=DIRECTION('',(0.E0,1.E0,0.E0));
#17=VECTOR('',#16,2.E1);
#18=CARTESIAN_POINT('',(4.E1,-2.E1,-7.5E1));
#19=LINE('',#18,#17);
#20=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#21=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#22=PRESENTATION_STYLE_ASSIGNMENT((#21));
#23=STYLED_ITEM('',(#22),#19);
#24=DIRECTION('',(-1.E0,0.E0,0.E0));
#25=VECTOR('',#24,1.8E2);
#26=CARTESIAN_POINT('',(4.E1,0.E0,-7.5E1));
#27=LINE('',#26,#25);
#28=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#29=PRESENTATION_STYLE_ASSIGNMENT((#28));
#30=STYLED_ITEM('',(#29),#27);
#31=DIRECTION('',(0.E0,-1.E0,0.E0));
#32=VECTOR('',#31,2.E1);
#33=CARTESIAN_POINT('',(-1.4E2,0.E0,-7.5E1));
#34=LINE('',#33,#32);
#35=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#36=PRESENTATION_STYLE_ASSIGNMENT((#35));
#37=STYLED_ITEM('',(#36),#34);
#38=DIRECTION('',(1.E0,0.E0,0.E0));
#39=VECTOR('',#38,1.8E2);
#40=CARTESIAN_POINT('',(-1.4E2,-2.E1,-7.5E1));
#41=LINE('',#40,#39);
#42=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#43=PRESENTATION_STYLE_ASSIGNMENT((#42));
#44=STYLED_ITEM('',(#43),#41);
#45=DIRECTION('',(0.E0,0.E0,1.E0));
#46=VECTOR('',#45,1.5E2);
#47=CARTESIAN_POINT('',(4.E1,-2.E1,-7.5E1));
#48=LINE('',#47,#46);
#49=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#50=PRESENTATION_STYLE_ASSIGNMENT((#49));
#51=STYLED_ITEM('',(#50),#48);
#52=CARTESIAN_POINT('',(-9.25E1,-2.E1,-1.299038105677E1));
#53=DIRECTION('',(0.E0,1.E0,0.E0));
#54=DIRECTION('',(-1.E0,0.E0,0.E0));
#55=AXIS2_PLACEMENT_3D('',#52,#53,#54);
#57=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#58=PRESENTATION_STYLE_ASSIGNMENT((#57));
#59=STYLED_ITEM('',(#58),#56);
#60=CARTESIAN_POINT('',(-9.25E1,-2.E1,-1.299038105677E1));
#61=DIRECTION('',(0.E0,1.E0,0.E0));
#62=DIRECTION('',(1.E0,0.E0,0.E0));
#63=AXIS2_PLACEMENT_3D('',#60,#61,#62);
#65=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#66=PRESENTATION_STYLE_ASSIGNMENT((#65));
#67=STYLED_ITEM('',(#66),#64);
#68=CARTESIAN_POINT('',(-9.25E1,-2.E1,1.299038105677E1));
#69=DIRECTION('',(0.E0,1.E0,0.E0));
#70=DIRECTION('',(-1.E0,0.E0,0.E0));
#71=AXIS2_PLACEMENT_3D('',#68,#69,#70);
#73=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#74=PRESENTATION_STYLE_ASSIGNMENT((#73));
#75=STYLED_ITEM('',(#74),#72);
#76=CARTESIAN_POINT('',(-9.25E1,-2.E1,1.299038105677E1));
#77=DIRECTION('',(0.E0,1.E0,0.E0));
#78=DIRECTION('',(1.E0,0.E0,0.E0));
#79=AXIS2_PLACEMENT_3D('',#76,#77,#78);
#81=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#82=PRESENTATION_STYLE_ASSIGNMENT((#81));
#83=STYLED_ITEM('',(#82),#80);
#84=CARTESIAN_POINT('',(-7.5E0,-2.E1,-1.299038105677E1));
#85=DIRECTION('',(0.E0,-1.E0,0.E0));
#86=DIRECTION('',(1.E0,0.E0,0.E0));
#87=AXIS2_PLACEMENT_3D('',#84,#85,#86);
#89=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#90=PRESENTATION_STYLE_ASSIGNMENT((#89));
#91=STYLED_ITEM('',(#90),#88);
#92=CARTESIAN_POINT('',(-7.5E0,-2.E1,-1.299038105677E1));
#93=DIRECTION('',(0.E0,-1.E0,0.E0));
#94=DIRECTION('',(-1.E0,0.E0,0.E0));
#95=AXIS2_PLACEMENT_3D('',#92,#93,#94);
#97=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#98=PRESENTATION_STYLE_ASSIGNMENT((#97));
#99=STYLED_ITEM('',(#98),#96);
#100=CARTESIAN_POINT('',(-7.5E0,-2.E1,1.299038105677E1));
#101=DIRECTION('',(0.E0,-1.E0,0.E0));
#102=DIRECTION('',(1.E0,0.E0,0.E0));
#103=AXIS2_PLACEMENT_3D('',#100,#101,#102);
#105=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#106=PRESENTATION_STYLE_ASSIGNMENT((#105));
#107=STYLED_ITEM('',(#106),#104);
#108=CARTESIAN_POINT('',(-7.5E0,-2.E1,1.299038105677E1));
#109=DIRECTION('',(0.E0,-1.E0,0.E0));
#110=DIRECTION('',(-1.E0,0.E0,0.E0));
#111=AXIS2_PLACEMENT_3D('',#108,#109,#110);
#113=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#114=PRESENTATION_STYLE_ASSIGNMENT((#113));
#115=STYLED_ITEM('',(#114),#112);
#116=DIRECTION('',(0.E0,0.E0,1.E0));
#117=VECTOR('',#116,1.5E2);
#118=CARTESIAN_POINT('',(-1.4E2,-2.E1,-7.5E1));
#119=LINE('',#118,#117);
#120=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#121=PRESENTATION_STYLE_ASSIGNMENT((#120));
#122=STYLED_ITEM('',(#121),#119);
#123=CARTESIAN_POINT('',(1.5E1,-2.E1,0.E0));
#124=DIRECTION('',(0.E0,-1.E0,0.E0));
#125=DIRECTION('',(1.E0,0.E0,0.E0));
#126=AXIS2_PLACEMENT_3D('',#123,#124,#125);
#128=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#129=PRESENTATION_STYLE_ASSIGNMENT((#128));
#130=STYLED_ITEM('',(#129),#127);
#131=CARTESIAN_POINT('',(1.5E1,-2.E1,0.E0));
#132=DIRECTION('',(0.E0,-1.E0,0.E0));
#133=DIRECTION('',(-1.E0,0.E0,0.E0));
#134=AXIS2_PLACEMENT_3D('',#131,#132,#133);
#136=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#137=PRESENTATION_STYLE_ASSIGNMENT((#136));
#138=STYLED_ITEM('',(#137),#135);
#139=CARTESIAN_POINT('',(-1.15E2,-2.E1,0.E0));
#140=DIRECTION('',(0.E0,1.E0,0.E0));
#141=DIRECTION('',(-1.E0,0.E0,0.E0));
#142=AXIS2_PLACEMENT_3D('',#139,#140,#141);
#144=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#145=PRESENTATION_STYLE_ASSIGNMENT((#144));
#146=STYLED_ITEM('',(#145),#143);
#147=CARTESIAN_POINT('',(-1.15E2,-2.E1,0.E0));
#148=DIRECTION('',(0.E0,1.E0,0.E0));
#149=DIRECTION('',(1.E0,0.E0,0.E0));
#150=AXIS2_PLACEMENT_3D('',#147,#148,#149);
#152=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#153=PRESENTATION_STYLE_ASSIGNMENT((#152));
#154=STYLED_ITEM('',(#153),#151);
#155=DIRECTION('',(0.E0,-1.E0,0.E0));
#156=VECTOR('',#155,2.E1);
#157=CARTESIAN_POINT('',(-9.75E1,0.E0,-1.299038105677E1));
#158=LINE('',#157,#156);
#159=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#160=PRESENTATION_STYLE_ASSIGNMENT((#159));
#161=STYLED_ITEM('',(#160),#158);
#162=CARTESIAN_POINT('',(-9.25E1,0.E0,-1.299038105677E1));
#163=DIRECTION('',(0.E0,1.E0,0.E0));
#164=DIRECTION('',(-1.E0,0.E0,0.E0));
#165=AXIS2_PLACEMENT_3D('',#162,#163,#164);
#167=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#168=PRESENTATION_STYLE_ASSIGNMENT((#167));
#169=STYLED_ITEM('',(#168),#166);
#170=CARTESIAN_POINT('',(-9.25E1,0.E0,-1.299038105677E1));
#171=DIRECTION('',(0.E0,1.E0,0.E0));
#172=DIRECTION('',(1.E0,0.E0,0.E0));
#173=AXIS2_PLACEMENT_3D('',#170,#171,#172);
#175=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#176=PRESENTATION_STYLE_ASSIGNMENT((#175));
#177=STYLED_ITEM('',(#176),#174);
#178=CARTESIAN_POINT('',(-9.25E1,0.E0,1.299038105677E1));
#179=DIRECTION('',(0.E0,1.E0,0.E0));
#180=DIRECTION('',(-1.E0,0.E0,0.E0));
#181=AXIS2_PLACEMENT_3D('',#178,#179,#180);
#183=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#184=PRESENTATION_STYLE_ASSIGNMENT((#183));
#185=STYLED_ITEM('',(#184),#182);
#186=CARTESIAN_POINT('',(-9.25E1,0.E0,1.299038105677E1));
#187=DIRECTION('',(0.E0,1.E0,0.E0));
#188=DIRECTION('',(1.E0,0.E0,0.E0));
#189=AXIS2_PLACEMENT_3D('',#186,#187,#188);
#191=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#192=PRESENTATION_STYLE_ASSIGNMENT((#191));
#193=STYLED_ITEM('',(#192),#190);
#194=CARTESIAN_POINT('',(-7.5E0,0.E0,-1.299038105677E1));
#195=DIRECTION('',(0.E0,-1.E0,0.E0));
#196=DIRECTION('',(1.E0,0.E0,0.E0));
#197=AXIS2_PLACEMENT_3D('',#194,#195,#196);
#199=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#200=PRESENTATION_STYLE_ASSIGNMENT((#199));
#201=STYLED_ITEM('',(#200),#198);
#202=CARTESIAN_POINT('',(-7.5E0,0.E0,-1.299038105677E1));
#203=DIRECTION('',(0.E0,-1.E0,0.E0));
#204=DIRECTION('',(-1.E0,0.E0,0.E0));
#205=AXIS2_PLACEMENT_3D('',#202,#203,#204);
#207=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#208=PRESENTATION_STYLE_ASSIGNMENT((#207));
#209=STYLED_ITEM('',(#208),#206);
#210=CARTESIAN_POINT('',(-7.5E0,0.E0,1.299038105677E1));
#211=DIRECTION('',(0.E0,-1.E0,0.E0));
#212=DIRECTION('',(1.E0,0.E0,0.E0));
#213=AXIS2_PLACEMENT_3D('',#210,#211,#212);
#215=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#216=PRESENTATION_STYLE_ASSIGNMENT((#215));
#217=STYLED_ITEM('',(#216),#214);
#218=CARTESIAN_POINT('',(-7.5E0,0.E0,1.299038105677E1));
#219=DIRECTION('',(0.E0,-1.E0,0.E0));
#220=DIRECTION('',(-1.E0,0.E0,0.E0));
#221=AXIS2_PLACEMENT_3D('',#218,#219,#220);
#223=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#224=PRESENTATION_STYLE_ASSIGNMENT((#223));
#225=STYLED_ITEM('',(#224),#222);
#226=DIRECTION('',(0.E0,0.E0,1.E0));
#227=VECTOR('',#226,1.5E2);
#228=CARTESIAN_POINT('',(4.E1,0.E0,-7.5E1));
#229=LINE('',#228,#227);
#230=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#231=PRESENTATION_STYLE_ASSIGNMENT((#230));
#232=STYLED_ITEM('',(#231),#229);
#233=CARTESIAN_POINT('',(1.5E1,0.E0,0.E0));
#234=DIRECTION('',(0.E0,-1.E0,0.E0));
#235=DIRECTION('',(1.E0,0.E0,0.E0));
#236=AXIS2_PLACEMENT_3D('',#233,#234,#235);
#238=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#239=PRESENTATION_STYLE_ASSIGNMENT((#238));
#240=STYLED_ITEM('',(#239),#237);
#241=CARTESIAN_POINT('',(1.5E1,0.E0,0.E0));
#242=DIRECTION('',(0.E0,-1.E0,0.E0));
#243=DIRECTION('',(-1.E0,0.E0,0.E0));
#244=AXIS2_PLACEMENT_3D('',#241,#242,#243);
#246=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#247=PRESENTATION_STYLE_ASSIGNMENT((#246));
#248=STYLED_ITEM('',(#247),#245);
#249=CARTESIAN_POINT('',(-1.15E2,0.E0,0.E0));
#250=DIRECTION('',(0.E0,1.E0,0.E0));
#251=DIRECTION('',(-1.E0,0.E0,0.E0));
#252=AXIS2_PLACEMENT_3D('',#249,#250,#251);
#254=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#255=PRESENTATION_STYLE_ASSIGNMENT((#254));
#256=STYLED_ITEM('',(#255),#253);
#257=CARTESIAN_POINT('',(-1.15E2,0.E0,0.E0));
#258=DIRECTION('',(0.E0,1.E0,0.E0));
#259=DIRECTION('',(1.E0,0.E0,0.E0));
#260=AXIS2_PLACEMENT_3D('',#257,#258,#259);
#262=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#263=PRESENTATION_STYLE_ASSIGNMENT((#262));
#264=STYLED_ITEM('',(#263),#261);
#265=DIRECTION('',(0.E0,-1.E0,0.E0));
#266=VECTOR('',#265,2.E1);
#267=CARTESIAN_POINT('',(-8.75E1,0.E0,-1.299038105677E1));
#268=LINE('',#267,#266);
#269=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#270=PRESENTATION_STYLE_ASSIGNMENT((#269));
#271=STYLED_ITEM('',(#270),#268);
#272=DIRECTION('',(0.E0,-1.E0,0.E0));
#273=VECTOR('',#272,2.E1);
#274=CARTESIAN_POINT('',(-9.75E1,0.E0,1.299038105677E1));
#275=LINE('',#274,#273);
#276=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#277=PRESENTATION_STYLE_ASSIGNMENT((#276));
#278=STYLED_ITEM('',(#277),#275);
#279=DIRECTION('',(0.E0,-1.E0,0.E0));
#280=VECTOR('',#279,2.E1);
#281=CARTESIAN_POINT('',(-8.75E1,0.E0,1.299038105677E1));
#282=LINE('',#281,#280);
#283=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#284=PRESENTATION_STYLE_ASSIGNMENT((#283));
#285=STYLED_ITEM('',(#284),#282);
#286=DIRECTION('',(0.E0,-1.E0,0.E0));
#287=VECTOR('',#286,2.E1);
#288=CARTESIAN_POINT('',(-2.5E0,0.E0,-1.299038105677E1));
#289=LINE('',#288,#287);
#290=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#291=PRESENTATION_STYLE_ASSIGNMENT((#290));
#292=STYLED_ITEM('',(#291),#289);
#293=DIRECTION('',(0.E0,-1.E0,0.E0));
#294=VECTOR('',#293,2.E1);
#295=CARTESIAN_POINT('',(-1.25E1,0.E0,-1.299038105677E1));
#296=LINE('',#295,#294);
#297=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#298=PRESENTATION_STYLE_ASSIGNMENT((#297));
#299=STYLED_ITEM('',(#298),#296);
#300=DIRECTION('',(0.E0,-1.E0,0.E0));
#301=VECTOR('',#300,2.E1);
#302=CARTESIAN_POINT('',(-2.5E0,0.E0,1.299038105677E1));
#303=LINE('',#302,#301);
#304=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#305=PRESENTATION_STYLE_ASSIGNMENT((#304));
#306=STYLED_ITEM('',(#305),#303);
#307=DIRECTION('',(0.E0,-1.E0,0.E0));
#308=VECTOR('',#307,2.E1);
#309=CARTESIAN_POINT('',(-1.25E1,0.E0,1.299038105677E1));
#310=LINE('',#309,#308);
#311=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#312=PRESENTATION_STYLE_ASSIGNMENT((#311));
#313=STYLED_ITEM('',(#312),#310);
#314=DIRECTION('',(0.E0,1.E0,0.E0));
#315=VECTOR('',#314,2.E1);
#316=CARTESIAN_POINT('',(4.E1,-2.E1,7.5E1));
#317=LINE('',#316,#315);
#318=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#319=PRESENTATION_STYLE_ASSIGNMENT((#318));
#320=STYLED_ITEM('',(#319),#317);
#321=DIRECTION('',(1.E0,0.E0,0.E0));
#322=VECTOR('',#321,1.8E2);
#323=CARTESIAN_POINT('',(-1.4E2,-2.E1,7.5E1));
#324=LINE('',#323,#322);
#325=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#326=PRESENTATION_STYLE_ASSIGNMENT((#325));
#327=STYLED_ITEM('',(#326),#324);
#328=DIRECTION('',(0.E0,-1.E0,0.E0));
#329=VECTOR('',#328,2.E1);
#330=CARTESIAN_POINT('',(-1.4E2,0.E0,7.5E1));
#331=LINE('',#330,#329);
#332=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#333=PRESENTATION_STYLE_ASSIGNMENT((#332));
#334=STYLED_ITEM('',(#333),#331);
#335=DIRECTION('',(-1.E0,0.E0,0.E0));
#336=VECTOR('',#335,1.8E2);
#337=CARTESIAN_POINT('',(4.E1,0.E0,7.5E1));
#338=LINE('',#337,#336);
#339=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#340=PRESENTATION_STYLE_ASSIGNMENT((#339));
#341=STYLED_ITEM('',(#340),#338);
#342=DIRECTION('',(0.E0,0.E0,1.E0));
#343=VECTOR('',#342,1.5E2);
#344=CARTESIAN_POINT('',(-1.4E2,0.E0,-7.5E1));
#345=LINE('',#344,#343);
#346=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#347=PRESENTATION_STYLE_ASSIGNMENT((#346));
#348=STYLED_ITEM('',(#347),#345);
#349=DIRECTION('',(0.E0,-1.E0,0.E0));
#350=VECTOR('',#349,2.E1);
#351=CARTESIAN_POINT('',(2.E1,0.E0,0.E0));
#352=LINE('',#351,#350);
#353=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#354=PRESENTATION_STYLE_ASSIGNMENT((#353));
#355=STYLED_ITEM('',(#354),#352);
#356=DIRECTION('',(0.E0,-1.E0,0.E0));
#357=VECTOR('',#356,2.E1);
#358=CARTESIAN_POINT('',(1.E1,0.E0,0.E0));
#359=LINE('',#358,#357);
#360=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#361=PRESENTATION_STYLE_ASSIGNMENT((#360));
#362=STYLED_ITEM('',(#361),#359);
#363=DIRECTION('',(0.E0,-1.E0,0.E0));
#364=VECTOR('',#363,2.E1);
#365=CARTESIAN_POINT('',(-1.2E2,0.E0,0.E0));
#366=LINE('',#365,#364);
#367=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#368=PRESENTATION_STYLE_ASSIGNMENT((#367));
#369=STYLED_ITEM('',(#368),#366);
#370=DIRECTION('',(0.E0,-1.E0,0.E0));
#371=VECTOR('',#370,2.E1);
#372=CARTESIAN_POINT('',(-1.1E2,0.E0,0.E0));
#373=LINE('',#372,#371);
#374=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1);
#375=PRESENTATION_STYLE_ASSIGNMENT((#374));
#376=STYLED_ITEM('',(#375),#373);
#377=CARTESIAN_POINT('',(4.E1,-2.E1,-7.5E1));
#378=CARTESIAN_POINT('',(4.E1,0.E0,-7.5E1));
#379=VERTEX_POINT('',#377);
#380=VERTEX_POINT('',#378);
#381=CARTESIAN_POINT('',(-1.4E2,0.E0,-7.5E1));
#382=VERTEX_POINT('',#381);
#383=CARTESIAN_POINT('',(-1.4E2,-2.E1,-7.5E1));
#384=VERTEX_POINT('',#383);
#385=CARTESIAN_POINT('',(4.E1,-2.E1,7.5E1));
#386=CARTESIAN_POINT('',(4.E1,0.E0,7.5E1));
#387=VERTEX_POINT('',#385);
#388=VERTEX_POINT('',#386);
#389=CARTESIAN_POINT('',(-1.4E2,0.E0,7.5E1));
#390=VERTEX_POINT('',#389);
#391=CARTESIAN_POINT('',(-1.4E2,-2.E1,7.5E1));
#392=VERTEX_POINT('',#391);
#393=CARTESIAN_POINT('',(2.E1,0.E0,0.E0));
#394=CARTESIAN_POINT('',(1.E1,0.E0,0.E0));
#395=VERTEX_POINT('',#393);
#396=VERTEX_POINT('',#394);
#397=CARTESIAN_POINT('',(2.E1,-2.E1,0.E0));
#398=CARTESIAN_POINT('',(1.E1,-2.E1,0.E0));
#399=VERTEX_POINT('',#397);
#400=VERTEX_POINT('',#398);
#401=CARTESIAN_POINT('',(-2.5E0,0.E0,1.299038105677E1));
#402=CARTESIAN_POINT('',(-1.25E1,0.E0,1.299038105677E1));
#403=VERTEX_POINT('',#401);
#404=VERTEX_POINT('',#402);
#405=CARTESIAN_POINT('',(-2.5E0,-2.E1,1.299038105677E1));
#406=CARTESIAN_POINT('',(-1.25E1,-2.E1,1.299038105677E1));
#407=VERTEX_POINT('',#405);
#408=VERTEX_POINT('',#406);
#409=CARTESIAN_POINT('',(-2.5E0,0.E0,-1.299038105677E1));
#410=CARTESIAN_POINT('',(-1.25E1,0.E0,-1.299038105677E1));
#411=VERTEX_POINT('',#409);
#412=VERTEX_POINT('',#410);
#413=CARTESIAN_POINT('',(-2.5E0,-2.E1,-1.299038105677E1));
#414=CARTESIAN_POINT('',(-1.25E1,-2.E1,-1.299038105677E1));
#415=VERTEX_POINT('',#413);
#416=VERTEX_POINT('',#414);
#417=CARTESIAN_POINT('',(-1.2E2,0.E0,0.E0));
#418=CARTESIAN_POINT('',(-1.1E2,0.E0,0.E0));
#419=VERTEX_POINT('',#417);
#420=VERTEX_POINT('',#418);
#421=CARTESIAN_POINT('',(-1.2E2,-2.E1,0.E0));
#422=CARTESIAN_POINT('',(-1.1E2,-2.E1,0.E0));
#423=VERTEX_POINT('',#421);
#424=VERTEX_POINT('',#422);
#425=CARTESIAN_POINT('',(-9.75E1,0.E0,1.299038105677E1));
#426=CARTESIAN_POINT('',(-8.75E1,0.E0,1.299038105677E1));
#427=VERTEX_POINT('',#425);
#428=VERTEX_POINT('',#426);
#429=CARTESIAN_POINT('',(-9.75E1,-2.E1,1.299038105677E1));
#430=CARTESIAN_POINT('',(-8.75E1,-2.E1,1.299038105677E1));
#431=VERTEX_POINT('',#429);
#432=VERTEX_POINT('',#430);
#433=CARTESIAN_POINT('',(-9.75E1,0.E0,-1.299038105677E1));
#434=CARTESIAN_POINT('',(-8.75E1,0.E0,-1.299038105677E1));
#435=VERTEX_POINT('',#433);
#436=VERTEX_POINT('',#434);
#437=CARTESIAN_POINT('',(-9.75E1,-2.E1,-1.299038105677E1));
#438=CARTESIAN_POINT('',(-8.75E1,-2.E1,-1.299038105677E1));
#439=VERTEX_POINT('',#437);
#440=VERTEX_POINT('',#438);
#441=CARTESIAN_POINT('',(0.E0,0.E0,-7.5E1));
#442=DIRECTION('',(0.E0,0.E0,1.E0));
#443=DIRECTION('',(-1.E0,0.E0,0.E0));
#444=AXIS2_PLACEMENT_3D('',#441,#442,#443);
#445=PLANE('',#444);
#447=ORIENTED_EDGE('',*,*,#446,.T.);
#449=ORIENTED_EDGE('',*,*,#448,.T.);
#451=ORIENTED_EDGE('',*,*,#450,.T.);
#453=ORIENTED_EDGE('',*,*,#452,.T.);
#454=EDGE_LOOP('',(#447,#449,#451,#453));
#455=FACE_OUTER_BOUND('',#454,.F.);
#457=CARTESIAN_POINT('',(4.E1,-2.E1,-7.5E1));
#458=DIRECTION('',(1.E0,0.E0,0.E0));
#459=DIRECTION('',(0.E0,1.E0,0.E0));
#460=AXIS2_PLACEMENT_3D('',#457,#458,#459);
#461=PLANE('',#460);
#462=ORIENTED_EDGE('',*,*,#446,.F.);
#464=ORIENTED_EDGE('',*,*,#463,.T.);
#466=ORIENTED_EDGE('',*,*,#465,.T.);
#468=ORIENTED_EDGE('',*,*,#467,.F.);
#469=EDGE_LOOP('',(#462,#464,#466,#468));
#470=FACE_OUTER_BOUND('',#469,.F.);
#472=CARTESIAN_POINT('',(-1.4E2,-2.E1,-7.5E1));
#473=DIRECTION('',(0.E0,-1.E0,0.E0));
#474=DIRECTION('',(1.E0,0.E0,0.E0));
#475=AXIS2_PLACEMENT_3D('',#472,#473,#474);
#476=PLANE('',#475);
#477=ORIENTED_EDGE('',*,*,#452,.F.);
#479=ORIENTED_EDGE('',*,*,#478,.T.);
#481=ORIENTED_EDGE('',*,*,#480,.T.);
#482=ORIENTED_EDGE('',*,*,#463,.F.);
#483=EDGE_LOOP('',(#477,#479,#481,#482));
#484=FACE_OUTER_BOUND('',#483,.F.);
#486=ORIENTED_EDGE('',*,*,#485,.F.);
#488=ORIENTED_EDGE('',*,*,#487,.F.);
#489=EDGE_LOOP('',(#486,#488));
#490=FACE_BOUND('',#489,.F.);
#492=ORIENTED_EDGE('',*,*,#491,.F.);
#494=ORIENTED_EDGE('',*,*,#493,.F.);
#495=EDGE_LOOP('',(#492,#494));
#496=FACE_BOUND('',#495,.F.);
#498=ORIENTED_EDGE('',*,*,#497,.T.);
#500=ORIENTED_EDGE('',*,*,#499,.T.);
#501=EDGE_LOOP('',(#498,#500));
#502=FACE_BOUND('',#501,.F.);
#504=ORIENTED_EDGE('',*,*,#503,.T.);
#506=ORIENTED_EDGE('',*,*,#505,.T.);
#507=EDGE_LOOP('',(#504,#506));
#508=FACE_BOUND('',#507,.F.);
#510=ORIENTED_EDGE('',*,*,#509,.T.);
#512=ORIENTED_EDGE('',*,*,#511,.T.);
#513=EDGE_LOOP('',(#510,#512));
#514=FACE_BOUND('',#513,.F.);
#516=ORIENTED_EDGE('',*,*,#515,.F.);
#518=ORIENTED_EDGE('',*,*,#517,.F.);
#519=EDGE_LOOP('',(#516,#518));
#520=FACE_BOUND('',#519,.F.);
#522=CARTESIAN_POINT('',(-9.25E1,0.E0,-1.299038105677E1));
#523=DIRECTION('',(0.E0,1.E0,0.E0));
#524=DIRECTION('',(-1.E0,0.E0,0.E0));
#525=AXIS2_PLACEMENT_3D('',#522,#523,#524);
#526=CYLINDRICAL_SURFACE('',#525,5.E0);
#528=ORIENTED_EDGE('',*,*,#527,.F.);
#530=ORIENTED_EDGE('',*,*,#529,.T.);
#531=ORIENTED_EDGE('',*,*,#485,.T.);
#533=ORIENTED_EDGE('',*,*,#532,.F.);
#534=EDGE_LOOP('',(#528,#530,#531,#533));
#535=FACE_OUTER_BOUND('',#534,.F.);
#537=CARTESIAN_POINT('',(4.E1,0.E0,-7.5E1));
#538=DIRECTION('',(0.E0,1.E0,0.E0));
#539=DIRECTION('',(-1.E0,0.E0,0.E0));
#540=AXIS2_PLACEMENT_3D('',#537,#538,#539);
#541=PLANE('',#540);
#542=ORIENTED_EDGE('',*,*,#448,.F.);
#543=ORIENTED_EDGE('',*,*,#467,.T.);
#545=ORIENTED_EDGE('',*,*,#544,.T.);
#547=ORIENTED_EDGE('',*,*,#546,.F.);
#548=EDGE_LOOP('',(#542,#543,#545,#547));
#549=FACE_OUTER_BOUND('',#548,.F.);
#550=ORIENTED_EDGE('',*,*,#527,.T.);
#552=ORIENTED_EDGE('',*,*,#551,.T.);
#553=EDGE_LOOP('',(#550,#552));
#554=FACE_BOUND('',#553,.F.);
#556=ORIENTED_EDGE('',*,*,#555,.T.);
#558=ORIENTED_EDGE('',*,*,#557,.T.);
#559=EDGE_LOOP('',(#556,#558));
#560=FACE_BOUND('',#559,.F.);
#562=ORIENTED_EDGE('',*,*,#561,.F.);
#564=ORIENTED_EDGE('',*,*,#563,.F.);
#565=EDGE_LOOP('',(#562,#564));
#566=FACE_BOUND('',#565,.F.);
#568=ORIENTED_EDGE('',*,*,#567,.F.);
#570=ORIENTED_EDGE('',*,*,#569,.F.);
#571=EDGE_LOOP('',(#568,#570));
#572=FACE_BOUND('',#571,.F.);
#574=ORIENTED_EDGE('',*,*,#573,.F.);
#576=ORIENTED_EDGE('',*,*,#575,.F.);
#577=EDGE_LOOP('',(#574,#576));
#578=FACE_BOUND('',#577,.F.);
#580=ORIENTED_EDGE('',*,*,#579,.T.);
#582=ORIENTED_EDGE('',*,*,#581,.T.);
#583=EDGE_LOOP('',(#580,#582));
#584=FACE_BOUND('',#583,.F.);
#586=CARTESIAN_POINT('',(-9.25E1,0.E0,-1.299038105677E1));
#587=DIRECTION('',(0.E0,1.E0,0.E0));
#588=DIRECTION('',(-1.E0,0.E0,0.E0));
#589=AXIS2_PLACEMENT_3D('',#586,#587,#588);
#590=CYLINDRICAL_SURFACE('',#589,5.E0);
#591=ORIENTED_EDGE('',*,*,#551,.F.);
#592=ORIENTED_EDGE('',*,*,#532,.T.);
#593=ORIENTED_EDGE('',*,*,#487,.T.);
#594=ORIENTED_EDGE('',*,*,#529,.F.);
#595=EDGE_LOOP('',(#591,#592,#593,#594));
#596=FACE_OUTER_BOUND('',#595,.F.);
#598=CARTESIAN_POINT('',(-9.25E1,0.E0,1.299038105677E1));
#599=DIRECTION('',(0.E0,1.E0,0.E0));
#600=DIRECTION('',(-1.E0,0.E0,0.E0));
#601=AXIS2_PLACEMENT_3D('',#598,#599,#600);
#602=CYLINDRICAL_SURFACE('',#601,5.E0);
#603=ORIENTED_EDGE('',*,*,#555,.F.);
#605=ORIENTED_EDGE('',*,*,#604,.T.);
#606=ORIENTED_EDGE('',*,*,#491,.T.);
#608=ORIENTED_EDGE('',*,*,#607,.F.);
#609=EDGE_LOOP('',(#603,#605,#606,#608));
#610=FACE_OUTER_BOUND('',#609,.F.);
#612=CARTESIAN_POINT('',(-9.25E1,0.E0,1.299038105677E1));
#613=DIRECTION('',(0.E0,1.E0,0.E0));
#614=DIRECTION('',(-1.E0,0.E0,0.E0));
#615=AXIS2_PLACEMENT_3D('',#612,#613,#614);
#616=CYLINDRICAL_SURFACE('',#615,5.E0);
#617=ORIENTED_EDGE('',*,*,#557,.F.);
#618=ORIENTED_EDGE('',*,*,#607,.T.);
#619=ORIENTED_EDGE('',*,*,#493,.T.);
#620=ORIENTED_EDGE('',*,*,#604,.F.);
#621=EDGE_LOOP('',(#617,#618,#619,#620));
#622=FACE_OUTER_BOUND('',#621,.F.);
#624=CARTESIAN_POINT('',(-7.5E0,0.E0,-1.299038105677E1));
#625=DIRECTION('',(0.E0,-1.E0,0.E0));
#626=DIRECTION('',(1.E0,0.E0,0.E0));
#627=AXIS2_PLACEMENT_3D('',#624,#625,#626);
#628=CYLINDRICAL_SURFACE('',#627,5.E0);
#629=ORIENTED_EDGE('',*,*,#561,.T.);
#631=ORIENTED_EDGE('',*,*,#630,.T.);
#632=ORIENTED_EDGE('',*,*,#497,.F.);
#634=ORIENTED_EDGE('',*,*,#633,.F.);
#635=EDGE_LOOP('',(#629,#631,#632,#634));
#636=FACE_OUTER_BOUND('',#635,.F.);
#638=CARTESIAN_POINT('',(-7.5E0,0.E0,-1.299038105677E1));
#639=DIRECTION('',(0.E0,-1.E0,0.E0));
#640=DIRECTION('',(1.E0,0.E0,0.E0));
#641=AXIS2_PLACEMENT_3D('',#638,#639,#640);
#642=CYLINDRICAL_SURFACE('',#641,5.E0);
#643=ORIENTED_EDGE('',*,*,#563,.T.);
#644=ORIENTED_EDGE('',*,*,#633,.T.);
#645=ORIENTED_EDGE('',*,*,#499,.F.);
#646=ORIENTED_EDGE('',*,*,#630,.F.);
#647=EDGE_LOOP('',(#643,#644,#645,#646));
#648=FACE_OUTER_BOUND('',#647,.F.);
#650=CARTESIAN_POINT('',(-7.5E0,0.E0,1.299038105677E1));
#651=DIRECTION('',(0.E0,-1.E0,0.E0));
#652=DIRECTION('',(1.E0,0.E0,0.E0));
#653=AXIS2_PLACEMENT_3D('',#650,#651,#652);
#654=CYLINDRICAL_SURFACE('',#653,5.E0);
#655=ORIENTED_EDGE('',*,*,#567,.T.);
#657=ORIENTED_EDGE('',*,*,#656,.T.);
#658=ORIENTED_EDGE('',*,*,#503,.F.);
#660=ORIENTED_EDGE('',*,*,#659,.F.);
#661=EDGE_LOOP('',(#655,#657,#658,#660));
#662=FACE_OUTER_BOUND('',#661,.F.);
#664=CARTESIAN_POINT('',(-7.5E0,0.E0,1.299038105677E1));
#665=DIRECTION('',(0.E0,-1.E0,0.E0));
#666=DIRECTION('',(1.E0,0.E0,0.E0));
#667=AXIS2_PLACEMENT_3D('',#664,#665,#666);
#668=CYLINDRICAL_SURFACE('',#667,5.E0);
#669=ORIENTED_EDGE('',*,*,#569,.T.);
#670=ORIENTED_EDGE('',*,*,#659,.T.);
#671=ORIENTED_EDGE('',*,*,#505,.F.);
#672=ORIENTED_EDGE('',*,*,#656,.F.);
#673=EDGE_LOOP('',(#669,#670,#671,#672));
#674=FACE_OUTER_BOUND('',#673,.F.);
#676=CARTESIAN_POINT('',(0.E0,0.E0,7.5E1));
#677=DIRECTION('',(0.E0,0.E0,1.E0));
#678=DIRECTION('',(-1.E0,0.E0,0.E0));
#679=AXIS2_PLACEMENT_3D('',#676,#677,#678);
#680=PLANE('',#679);
#681=ORIENTED_EDGE('',*,*,#465,.F.);
#682=ORIENTED_EDGE('',*,*,#480,.F.);
#684=ORIENTED_EDGE('',*,*,#683,.F.);
#685=ORIENTED_EDGE('',*,*,#544,.F.);
#686=EDGE_LOOP('',(#681,#682,#684,#685));
#687=FACE_OUTER_BOUND('',#686,.F.);
#689=CARTESIAN_POINT('',(-1.4E2,0.E0,-7.5E1));
#690=DIRECTION('',(-1.E0,0.E0,0.E0));
#691=DIRECTION('',(0.E0,-1.E0,0.E0));
#692=AXIS2_PLACEMENT_3D('',#689,#690,#691);
#693=PLANE('',#692);
#694=ORIENTED_EDGE('',*,*,#450,.F.);
#695=ORIENTED_EDGE('',*,*,#546,.T.);
#696=ORIENTED_EDGE('',*,*,#683,.T.);
#697=ORIENTED_EDGE('',*,*,#478,.F.);
#698=EDGE_LOOP('',(#694,#695,#696,#697));
#699=FACE_OUTER_BOUND('',#698,.F.);
#701=CARTESIAN_POINT('',(1.5E1,0.E0,0.E0));
#702=DIRECTION('',(0.E0,-1.E0,0.E0));
#703=DIRECTION('',(1.E0,0.E0,0.E0));
#704=AXIS2_PLACEMENT_3D('',#701,#702,#703);
#705=CYLINDRICAL_SURFACE('',#704,5.E0);
#706=ORIENTED_EDGE('',*,*,#573,.T.);
#708=ORIENTED_EDGE('',*,*,#707,.T.);
#709=ORIENTED_EDGE('',*,*,#509,.F.);
#711=ORIENTED_EDGE('',*,*,#710,.F.);
#712=EDGE_LOOP('',(#706,#708,#709,#711));
#713=FACE_OUTER_BOUND('',#712,.F.);
#715=CARTESIAN_POINT('',(1.5E1,0.E0,0.E0));
#716=DIRECTION('',(0.E0,-1.E0,0.E0));
#717=DIRECTION('',(1.E0,0.E0,0.E0));
#718=AXIS2_PLACEMENT_3D('',#715,#716,#717);
#719=CYLINDRICAL_SURFACE('',#718,5.E0);
#720=ORIENTED_EDGE('',*,*,#575,.T.);
#721=ORIENTED_EDGE('',*,*,#710,.T.);
#722=ORIENTED_EDGE('',*,*,#511,.F.);
#723=ORIENTED_EDGE('',*,*,#707,.F.);
#724=EDGE_LOOP('',(#720,#721,#722,#723));
#725=FACE_OUTER_BOUND('',#724,.F.);
#727=CARTESIAN_POINT('',(-1.15E2,0.E0,0.E0));
#728=DIRECTION('',(0.E0,1.E0,0.E0));
#729=DIRECTION('',(-1.E0,0.E0,0.E0));
#730=AXIS2_PLACEMENT_3D('',#727,#728,#729);
#731=CYLINDRICAL_SURFACE('',#730,5.E0);
#732=ORIENTED_EDGE('',*,*,#579,.F.);
#734=ORIENTED_EDGE('',*,*,#733,.T.);
#735=ORIENTED_EDGE('',*,*,#515,.T.);
#737=ORIENTED_EDGE('',*,*,#736,.F.);
#738=EDGE_LOOP('',(#732,#734,#735,#737));
#739=FACE_OUTER_BOUND('',#738,.F.);
#741=CARTESIAN_POINT('',(-1.15E2,0.E0,0.E0));
#742=DIRECTION('',(0.E0,1.E0,0.E0));
#743=DIRECTION('',(-1.E0,0.E0,0.E0));
#744=AXIS2_PLACEMENT_3D('',#741,#742,#743);
#745=CYLINDRICAL_SURFACE('',#744,5.E0);
#746=ORIENTED_EDGE('',*,*,#581,.F.);
#747=ORIENTED_EDGE('',*,*,#736,.T.);
#748=ORIENTED_EDGE('',*,*,#517,.T.);
#749=ORIENTED_EDGE('',*,*,#733,.F.);
#750=EDGE_LOOP('',(#746,#747,#748,#749));
#751=FACE_OUTER_BOUND('',#750,.F.);
#753=CLOSED_SHELL('',(#456,#471,#521,#536,#585,#597,#611,#623,#637,#649,#663,
#675,#688,#700,#714,#726,#740,#752));
#754=MANIFOLD_SOLID_BREP('',#753);
#755=FILL_AREA_STYLE_COLOUR('',#1);
#756=FILL_AREA_STYLE('',(#755));
#757=SURFACE_STYLE_FILL_AREA(#756);
#758=SURFACE_SIDE_STYLE('',(#757));
#759=SURFACE_STYLE_USAGE(.BOTH.,#758);
#760=PRESENTATION_STYLE_ASSIGNMENT((#759));
#15=STYLED_ITEM('',(#760),#754);
#761=DIRECTION('',(0.E0,1.E0,0.E0));
#762=VECTOR('',#761,2.E1);
#763=CARTESIAN_POINT('',(0.E0,-2.E1,0.E0));
#764=LINE('',#763,#762);
#766=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#767=PRESENTATION_STYLE_ASSIGNMENT((#766));
#768=STYLED_ITEM('',(#767),#765);
#770=DIRECTION('',(0.E0,-1.E0,0.E0));
#771=VECTOR('',#770,2.E1);
#772=CARTESIAN_POINT('',(1.5E1,0.E0,0.E0));
#773=LINE('',#772,#771);
#775=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#776=PRESENTATION_STYLE_ASSIGNMENT((#775));
#777=STYLED_ITEM('',(#776),#774);
#778=DIRECTION('',(0.E0,-1.E0,0.E0));
#779=VECTOR('',#778,2.E1);
#780=CARTESIAN_POINT('',(-7.5E0,0.E0,1.299038105677E1));
#781=LINE('',#780,#779);
#783=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#784=PRESENTATION_STYLE_ASSIGNMENT((#783));
#785=STYLED_ITEM('',(#784),#782);
#786=DIRECTION('',(0.E0,-1.E0,0.E0));
#787=VECTOR('',#786,2.E1);
#788=CARTESIAN_POINT('',(-7.5E0,0.E0,-1.299038105677E1));
#789=LINE('',#788,#787);
#791=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#792=PRESENTATION_STYLE_ASSIGNMENT((#791));
#793=STYLED_ITEM('',(#792),#790);
#794=DIRECTION('',(0.E0,1.E0,0.E0));
#795=VECTOR('',#794,2.E1);
#796=CARTESIAN_POINT('',(-1.15E2,-2.E1,0.E0));
#797=LINE('',#796,#795);
#799=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#800=PRESENTATION_STYLE_ASSIGNMENT((#799));
#801=STYLED_ITEM('',(#800),#798);
#802=DIRECTION('',(0.E0,1.E0,0.E0));
#803=VECTOR('',#802,2.E1);
#804=CARTESIAN_POINT('',(-9.25E1,-2.E1,1.299038105677E1));
#805=LINE('',#804,#803);
#807=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#808=PRESENTATION_STYLE_ASSIGNMENT((#807));
#809=STYLED_ITEM('',(#808),#806);
#810=DIRECTION('',(0.E0,1.E0,0.E0));
#811=VECTOR('',#810,2.E1);
#812=CARTESIAN_POINT('',(-9.25E1,-2.E1,-1.299038105677E1));
#813=LINE('',#812,#811);
#815=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#816=PRESENTATION_STYLE_ASSIGNMENT((#815));
#817=STYLED_ITEM('',(#816),#814);
#818=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#819=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#820=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#819);
#821=(CONVERSION_BASED_UNIT('INCH',#820)LENGTH_UNIT()NAMED_UNIT(#818));
#822=DIMENSIONAL_EXPONENTS(0.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#823=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#824=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),#823);
#825=(CONVERSION_BASED_UNIT('DEGREE',#824)NAMED_UNIT(#822)PLANE_ANGLE_UNIT());
#826=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#827=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.351501139453E-2),#821,
'closure',
'Maximum model space distance between geometric entities at asserted connectivities');
#829=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#830=DIRECTION('',(0.E0,0.E0,1.E0));
#831=DIRECTION('',(1.E0,0.E0,0.E0));
#769=GEOMETRIC_SET('',(#765,#774,#782,#790,#798,#806,#814));
#834=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#835=DIRECTION('',(0.E0,0.E0,1.E0));
#836=DIRECTION('',(1.E0,0.E0,0.E0));
#839=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#840=DIRECTION('',(0.E0,0.E0,1.E0));
#841=DIRECTION('',(1.E0,0.E0,0.E0));
#844=SHAPE_REPRESENTATION_RELATIONSHIP('','',#843,#833);
#845=SHAPE_REPRESENTATION_RELATIONSHIP('','',#843,#838);
#846=APPLICATION_CONTEXT(
'CONFIGURATION CONTROLLED 3D DESIGNS OF MECHANICAL PARTS AND ASSEMBLIES');
#847=APPLICATION_PROTOCOL_DEFINITION('draft international standard',
'Configuration_control_3d_design_ed2_mim_lf',2004,#846);
#848=DESIGN_CONTEXT('',#846,'design');
#849=MECHANICAL_CONTEXT('',#846,'mechanical');
#850=PRODUCT('PLATE','PLATE','NOT SPECIFIED',(#849));
#851=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('10','LAST_VERSION',
#850,.MADE.);
#858=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#859=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#860=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#859);
#861=(CONVERSION_BASED_UNIT('INCH',#860)LENGTH_UNIT()NAMED_UNIT(#858));
#862=DERIVED_UNIT_ELEMENT(#861,2.E0);
#863=DERIVED_UNIT((#862));
#864=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
7.002743208453E4),#863);
#868=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#869=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#870=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#869);
#871=(CONVERSION_BASED_UNIT('INCH',#870)LENGTH_UNIT()NAMED_UNIT(#868));
#872=DERIVED_UNIT_ELEMENT(#871,3.E0);
#873=DERIVED_UNIT((#872));
#874=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
5.305752176936E5),#873);
#878=CARTESIAN_POINT('centre point',(-5.E1,-1.E1,1.551408518876E-4));
#888=CARTESIAN_POINT('centre point',(-5.E1,-1.E1,1.551408518876E-4));
#892=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#893=DIRECTION('',(0.E0,0.E0,1.E0));
#894=DIRECTION('',(1.E0,0.E0,0.E0));
#895=AXIS2_PLACEMENT_3D('',#892,#893,#894);
#896=ITEM_DEFINED_TRANSFORMATION('','',#842,#895);
#897=(REPRESENTATION_RELATIONSHIP('','',#843,#885)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#896)SHAPE_REPRESENTATION_RELATIONSHIP());
#898=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#897,#887);
#899=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#900=DIRECTION('',(0.E0,0.E0,1.E0));
#901=DIRECTION('',(1.E0,0.E0,0.E0));
#902=AXIS2_PLACEMENT_3D('ASM_DEF_CSYS',#899,#900,#901);
#903=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#7);
#904=PRESENTATION_STYLE_ASSIGNMENT((#903));
#905=STYLED_ITEM('',(#904),#902);
#907=DIRECTION('',(0.E0,0.E0,-1.E0));
#908=VECTOR('',#907,5.E1);
#909=CARTESIAN_POINT('',(-5.E1,0.E0,5.E1));
#910=LINE('',#909,#908);
#911=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#912=PRESENTATION_STYLE_ASSIGNMENT((#911));
#913=STYLED_ITEM('',(#912),#910);
#914=DIRECTION('',(0.E0,1.E0,0.E0));
#915=VECTOR('',#914,6.E1);
#916=CARTESIAN_POINT('',(-5.E1,0.E0,0.E0));
#917=LINE('',#916,#915);
#918=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#919=PRESENTATION_STYLE_ASSIGNMENT((#918));
#920=STYLED_ITEM('',(#919),#917);
#921=DIRECTION('',(0.E0,0.E0,1.E0));
#922=VECTOR('',#921,1.E1);
#923=CARTESIAN_POINT('',(-5.E1,6.E1,0.E0));
#924=LINE('',#923,#922);
#925=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#926=PRESENTATION_STYLE_ASSIGNMENT((#925));
#927=STYLED_ITEM('',(#926),#924);
#928=DIRECTION('',(0.E0,-1.E0,0.E0));
#929=VECTOR('',#928,5.E1);
#930=CARTESIAN_POINT('',(-5.E1,6.E1,1.E1));
#931=LINE('',#930,#929);
#932=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#933=PRESENTATION_STYLE_ASSIGNMENT((#932));
#934=STYLED_ITEM('',(#933),#931);
#935=DIRECTION('',(0.E0,0.E0,1.E0));
#936=VECTOR('',#935,4.E1);
#937=CARTESIAN_POINT('',(-5.E1,1.E1,1.E1));
#938=LINE('',#937,#936);
#939=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#940=PRESENTATION_STYLE_ASSIGNMENT((#939));
#941=STYLED_ITEM('',(#940),#938);
#942=DIRECTION('',(0.E0,-1.E0,0.E0));
#943=VECTOR('',#942,1.E1);
#944=CARTESIAN_POINT('',(-5.E1,1.E1,5.E1));
#945=LINE('',#944,#943);
#946=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#947=PRESENTATION_STYLE_ASSIGNMENT((#946));
#948=STYLED_ITEM('',(#947),#945);
#949=CARTESIAN_POINT('',(1.299038105677E1,0.E0,4.25E1));
#950=DIRECTION('',(0.E0,-1.E0,0.E0));
#951=DIRECTION('',(1.E0,0.E0,0.E0));
#952=AXIS2_PLACEMENT_3D('',#949,#950,#951);
#954=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#955=PRESENTATION_STYLE_ASSIGNMENT((#954));
#956=STYLED_ITEM('',(#955),#953);
#957=CARTESIAN_POINT('',(1.299038105677E1,0.E0,4.25E1));
#958=DIRECTION('',(0.E0,-1.E0,0.E0));
#959=DIRECTION('',(-1.E0,0.E0,0.E0));
#960=AXIS2_PLACEMENT_3D('',#957,#958,#959);
#962=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#963=PRESENTATION_STYLE_ASSIGNMENT((#962));
#964=STYLED_ITEM('',(#963),#961);
#965=CARTESIAN_POINT('',(-1.299038105677E1,0.E0,4.25E1));
#966=DIRECTION('',(0.E0,-1.E0,0.E0));
#967=DIRECTION('',(1.E0,0.E0,0.E0));
#968=AXIS2_PLACEMENT_3D('',#965,#966,#967);
#970=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#971=PRESENTATION_STYLE_ASSIGNMENT((#970));
#972=STYLED_ITEM('',(#971),#969);
#973=CARTESIAN_POINT('',(-1.299038105677E1,0.E0,4.25E1));
#974=DIRECTION('',(0.E0,-1.E0,0.E0));
#975=DIRECTION('',(-1.E0,0.E0,0.E0));
#976=AXIS2_PLACEMENT_3D('',#973,#974,#975);
#978=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#979=PRESENTATION_STYLE_ASSIGNMENT((#978));
#980=STYLED_ITEM('',(#979),#977);
#981=DIRECTION('',(1.E0,0.E0,0.E0));
#982=VECTOR('',#981,1.E2);
#983=CARTESIAN_POINT('',(-5.E1,0.E0,5.E1));
#984=LINE('',#983,#982);
#985=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#986=PRESENTATION_STYLE_ASSIGNMENT((#985));
#987=STYLED_ITEM('',(#986),#984);
#988=CARTESIAN_POINT('',(0.E0,0.E0,2.E1));
#989=DIRECTION('',(0.E0,-1.E0,0.E0));
#990=DIRECTION('',(1.E0,0.E0,0.E0));
#991=AXIS2_PLACEMENT_3D('',#988,#989,#990);
#993=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#994=PRESENTATION_STYLE_ASSIGNMENT((#993));
#995=STYLED_ITEM('',(#994),#992);
#996=CARTESIAN_POINT('',(0.E0,0.E0,2.E1));
#997=DIRECTION('',(0.E0,-1.E0,0.E0));
#998=DIRECTION('',(-1.E0,0.E0,0.E0));
#999=AXIS2_PLACEMENT_3D('',#996,#997,#998);
#1001=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1002=PRESENTATION_STYLE_ASSIGNMENT((#1001));
#1003=STYLED_ITEM('',(#1002),#1000);
#1004=DIRECTION('',(0.E0,-1.E0,0.E0));
#1005=VECTOR('',#1004,1.E1);
#1006=CARTESIAN_POINT('',(1.799038105677E1,1.E1,4.25E1));
#1007=LINE('',#1006,#1005);
#1008=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1009=PRESENTATION_STYLE_ASSIGNMENT((#1008));
#1010=STYLED_ITEM('',(#1009),#1007);
#1011=DIRECTION('',(0.E0,-1.E0,0.E0));
#1012=VECTOR('',#1011,1.E1);
#1013=CARTESIAN_POINT('',(7.990381056767E0,1.E1,4.25E1));
#1014=LINE('',#1013,#1012);
#1015=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1016=PRESENTATION_STYLE_ASSIGNMENT((#1015));
#1017=STYLED_ITEM('',(#1016),#1014);
#1018=CARTESIAN_POINT('',(1.299038105677E1,1.E1,4.25E1));
#1019=DIRECTION('',(0.E0,-1.E0,0.E0));
#1020=DIRECTION('',(1.E0,0.E0,0.E0));
#1021=AXIS2_PLACEMENT_3D('',#1018,#1019,#1020);
#1023=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1024=PRESENTATION_STYLE_ASSIGNMENT((#1023));
#1025=STYLED_ITEM('',(#1024),#1022);
#1026=CARTESIAN_POINT('',(1.299038105677E1,1.E1,4.25E1));
#1027=DIRECTION('',(0.E0,-1.E0,0.E0));
#1028=DIRECTION('',(-1.E0,0.E0,0.E0));
#1029=AXIS2_PLACEMENT_3D('',#1026,#1027,#1028);
#1031=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1032=PRESENTATION_STYLE_ASSIGNMENT((#1031));
#1033=STYLED_ITEM('',(#1032),#1030);
#1034=CARTESIAN_POINT('',(-1.299038105677E1,1.E1,4.25E1));
#1035=DIRECTION('',(0.E0,-1.E0,0.E0));
#1036=DIRECTION('',(1.E0,0.E0,0.E0));
#1037=AXIS2_PLACEMENT_3D('',#1034,#1035,#1036);
#1039=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1040=PRESENTATION_STYLE_ASSIGNMENT((#1039));
#1041=STYLED_ITEM('',(#1040),#1038);
#1042=CARTESIAN_POINT('',(-1.299038105677E1,1.E1,4.25E1));
#1043=DIRECTION('',(0.E0,-1.E0,0.E0));
#1044=DIRECTION('',(-1.E0,0.E0,0.E0));
#1045=AXIS2_PLACEMENT_3D('',#1042,#1043,#1044);
#1047=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1048=PRESENTATION_STYLE_ASSIGNMENT((#1047));
#1049=STYLED_ITEM('',(#1048),#1046);
#1050=DIRECTION('',(1.E0,0.E0,0.E0));
#1051=VECTOR('',#1050,1.E2);
#1052=CARTESIAN_POINT('',(-5.E1,1.E1,1.E1));
#1053=LINE('',#1052,#1051);
#1054=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1055=PRESENTATION_STYLE_ASSIGNMENT((#1054));
#1056=STYLED_ITEM('',(#1055),#1053);
#1057=CARTESIAN_POINT('',(0.E0,1.E1,2.E1));
#1058=DIRECTION('',(0.E0,-1.E0,0.E0));
#1059=DIRECTION('',(1.E0,0.E0,0.E0));
#1060=AXIS2_PLACEMENT_3D('',#1057,#1058,#1059);
#1062=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1063=PRESENTATION_STYLE_ASSIGNMENT((#1062));
#1064=STYLED_ITEM('',(#1063),#1061);
#1065=CARTESIAN_POINT('',(0.E0,1.E1,2.E1));
#1066=DIRECTION('',(0.E0,-1.E0,0.E0));
#1067=DIRECTION('',(-1.E0,0.E0,0.E0));
#1068=AXIS2_PLACEMENT_3D('',#1065,#1066,#1067);
#1070=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1071=PRESENTATION_STYLE_ASSIGNMENT((#1070));
#1072=STYLED_ITEM('',(#1071),#1069);
#1073=DIRECTION('',(0.E0,-1.E0,0.E0));
#1074=VECTOR('',#1073,1.E1);
#1075=CARTESIAN_POINT('',(-7.990381056767E0,1.E1,4.25E1));
#1076=LINE('',#1075,#1074);
#1077=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1078=PRESENTATION_STYLE_ASSIGNMENT((#1077));
#1079=STYLED_ITEM('',(#1078),#1076);
#1080=DIRECTION('',(0.E0,-1.E0,0.E0));
#1081=VECTOR('',#1080,1.E1);
#1082=CARTESIAN_POINT('',(-1.799038105677E1,1.E1,4.25E1));
#1083=LINE('',#1082,#1081);
#1084=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1085=PRESENTATION_STYLE_ASSIGNMENT((#1084));
#1086=STYLED_ITEM('',(#1085),#1083);
#1087=DIRECTION('',(1.E0,0.E0,0.E0));
#1088=VECTOR('',#1087,1.E2);
#1089=CARTESIAN_POINT('',(-5.E1,6.E1,1.E1));
#1090=LINE('',#1089,#1088);
#1091=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1092=PRESENTATION_STYLE_ASSIGNMENT((#1091));
#1093=STYLED_ITEM('',(#1092),#1090);
#1094=CARTESIAN_POINT('',(0.E0,4.E1,1.E1));
#1095=DIRECTION('',(0.E0,0.E0,-1.E0));
#1096=DIRECTION('',(-1.E0,0.E0,0.E0));
#1097=AXIS2_PLACEMENT_3D('',#1094,#1095,#1096);
#1099=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1100=PRESENTATION_STYLE_ASSIGNMENT((#1099));
#1101=STYLED_ITEM('',(#1100),#1098);
#1102=CARTESIAN_POINT('',(0.E0,4.E1,1.E1));
#1103=DIRECTION('',(0.E0,0.E0,-1.E0));
#1104=DIRECTION('',(1.E0,0.E0,0.E0));
#1105=AXIS2_PLACEMENT_3D('',#1102,#1103,#1104);
#1107=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1108=PRESENTATION_STYLE_ASSIGNMENT((#1107));
#1109=STYLED_ITEM('',(#1108),#1106);
#1110=DIRECTION('',(1.E0,0.E0,0.E0));
#1111=VECTOR('',#1110,1.E2);
#1112=CARTESIAN_POINT('',(-5.E1,6.E1,0.E0));
#1113=LINE('',#1112,#1111);
#1114=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1115=PRESENTATION_STYLE_ASSIGNMENT((#1114));
#1116=STYLED_ITEM('',(#1115),#1113);
#1117=DIRECTION('',(1.E0,0.E0,0.E0));
#1118=VECTOR('',#1117,1.E2);
#1119=CARTESIAN_POINT('',(-5.E1,0.E0,0.E0));
#1120=LINE('',#1119,#1118);
#1121=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1122=PRESENTATION_STYLE_ASSIGNMENT((#1121));
#1123=STYLED_ITEM('',(#1122),#1120);
#1124=CARTESIAN_POINT('',(0.E0,4.E1,0.E0));
#1125=DIRECTION('',(0.E0,0.E0,-1.E0));
#1126=DIRECTION('',(-1.E0,0.E0,0.E0));
#1127=AXIS2_PLACEMENT_3D('',#1124,#1125,#1126);
#1129=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1130=PRESENTATION_STYLE_ASSIGNMENT((#1129));
#1131=STYLED_ITEM('',(#1130),#1128);
#1132=CARTESIAN_POINT('',(0.E0,4.E1,0.E0));
#1133=DIRECTION('',(0.E0,0.E0,-1.E0));
#1134=DIRECTION('',(1.E0,0.E0,0.E0));
#1135=AXIS2_PLACEMENT_3D('',#1132,#1133,#1134);
#1137=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1138=PRESENTATION_STYLE_ASSIGNMENT((#1137));
#1139=STYLED_ITEM('',(#1138),#1136);
#1140=DIRECTION('',(0.E0,0.E0,-1.E0));
#1141=VECTOR('',#1140,5.E1);
#1142=CARTESIAN_POINT('',(5.E1,0.E0,5.E1));
#1143=LINE('',#1142,#1141);
#1144=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1145=PRESENTATION_STYLE_ASSIGNMENT((#1144));
#1146=STYLED_ITEM('',(#1145),#1143);
#1147=DIRECTION('',(0.E0,-1.E0,0.E0));
#1148=VECTOR('',#1147,1.E1);
#1149=CARTESIAN_POINT('',(5.E1,1.E1,5.E1));
#1150=LINE('',#1149,#1148);
#1151=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1152=PRESENTATION_STYLE_ASSIGNMENT((#1151));
#1153=STYLED_ITEM('',(#1152),#1150);
#1154=DIRECTION('',(0.E0,0.E0,1.E0));
#1155=VECTOR('',#1154,4.E1);
#1156=CARTESIAN_POINT('',(5.E1,1.E1,1.E1));
#1157=LINE('',#1156,#1155);
#1158=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1159=PRESENTATION_STYLE_ASSIGNMENT((#1158));
#1160=STYLED_ITEM('',(#1159),#1157);
#1161=DIRECTION('',(0.E0,-1.E0,0.E0));
#1162=VECTOR('',#1161,5.E1);
#1163=CARTESIAN_POINT('',(5.E1,6.E1,1.E1));
#1164=LINE('',#1163,#1162);
#1165=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1166=PRESENTATION_STYLE_ASSIGNMENT((#1165));
#1167=STYLED_ITEM('',(#1166),#1164);
#1168=DIRECTION('',(0.E0,0.E0,1.E0));
#1169=VECTOR('',#1168,1.E1);
#1170=CARTESIAN_POINT('',(5.E1,6.E1,0.E0));
#1171=LINE('',#1170,#1169);
#1172=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1173=PRESENTATION_STYLE_ASSIGNMENT((#1172));
#1174=STYLED_ITEM('',(#1173),#1171);
#1175=DIRECTION('',(0.E0,1.E0,0.E0));
#1176=VECTOR('',#1175,6.E1);
#1177=CARTESIAN_POINT('',(5.E1,0.E0,0.E0));
#1178=LINE('',#1177,#1176);
#1179=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1180=PRESENTATION_STYLE_ASSIGNMENT((#1179));
#1181=STYLED_ITEM('',(#1180),#1178);
#1182=DIRECTION('',(1.E0,0.E0,0.E0));
#1183=VECTOR('',#1182,1.E2);
#1184=CARTESIAN_POINT('',(-5.E1,1.E1,5.E1));
#1185=LINE('',#1184,#1183);
#1186=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1187=PRESENTATION_STYLE_ASSIGNMENT((#1186));
#1188=STYLED_ITEM('',(#1187),#1185);
#1189=DIRECTION('',(0.E0,0.E0,-1.E0));
#1190=VECTOR('',#1189,1.E1);
#1191=CARTESIAN_POINT('',(-5.E0,4.E1,1.E1));
#1192=LINE('',#1191,#1190);
#1193=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1194=PRESENTATION_STYLE_ASSIGNMENT((#1193));
#1195=STYLED_ITEM('',(#1194),#1192);
#1196=DIRECTION('',(0.E0,0.E0,-1.E0));
#1197=VECTOR('',#1196,1.E1);
#1198=CARTESIAN_POINT('',(5.E0,4.E1,1.E1));
#1199=LINE('',#1198,#1197);
#1200=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1201=PRESENTATION_STYLE_ASSIGNMENT((#1200));
#1202=STYLED_ITEM('',(#1201),#1199);
#1203=DIRECTION('',(0.E0,-1.E0,0.E0));
#1204=VECTOR('',#1203,1.E1);
#1205=CARTESIAN_POINT('',(5.E0,1.E1,2.E1));
#1206=LINE('',#1205,#1204);
#1207=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1208=PRESENTATION_STYLE_ASSIGNMENT((#1207));
#1209=STYLED_ITEM('',(#1208),#1206);
#1210=DIRECTION('',(0.E0,-1.E0,0.E0));
#1211=VECTOR('',#1210,1.E1);
#1212=CARTESIAN_POINT('',(-5.E0,1.E1,2.E1));
#1213=LINE('',#1212,#1211);
#1214=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1215=PRESENTATION_STYLE_ASSIGNMENT((#1214));
#1216=STYLED_ITEM('',(#1215),#1213);
#1217=CARTESIAN_POINT('',(-5.E1,0.E0,5.E1));
#1218=CARTESIAN_POINT('',(-5.E1,0.E0,0.E0));
#1219=VERTEX_POINT('',#1217);
#1220=VERTEX_POINT('',#1218);
#1221=CARTESIAN_POINT('',(-5.E1,6.E1,0.E0));
#1222=VERTEX_POINT('',#1221);
#1223=CARTESIAN_POINT('',(-5.E1,6.E1,1.E1));
#1224=VERTEX_POINT('',#1223);
#1225=CARTESIAN_POINT('',(-5.E1,1.E1,1.E1));
#1226=VERTEX_POINT('',#1225);
#1227=CARTESIAN_POINT('',(-5.E1,1.E1,5.E1));
#1228=VERTEX_POINT('',#1227);
#1229=CARTESIAN_POINT('',(5.E1,0.E0,5.E1));
#1230=CARTESIAN_POINT('',(5.E1,0.E0,0.E0));
#1231=VERTEX_POINT('',#1229);
#1232=VERTEX_POINT('',#1230);
#1233=CARTESIAN_POINT('',(5.E1,6.E1,0.E0));
#1234=VERTEX_POINT('',#1233);
#1235=CARTESIAN_POINT('',(5.E1,6.E1,1.E1));
#1236=VERTEX_POINT('',#1235);
#1237=CARTESIAN_POINT('',(5.E1,1.E1,1.E1));
#1238=VERTEX_POINT('',#1237);
#1239=CARTESIAN_POINT('',(5.E1,1.E1,5.E1));
#1240=VERTEX_POINT('',#1239);
#1241=CARTESIAN_POINT('',(5.E0,0.E0,2.E1));
#1242=CARTESIAN_POINT('',(-5.E0,0.E0,2.E1));
#1243=VERTEX_POINT('',#1241);
#1244=VERTEX_POINT('',#1242);
#1245=CARTESIAN_POINT('',(5.E0,1.E1,2.E1));
#1246=CARTESIAN_POINT('',(-5.E0,1.E1,2.E1));
#1247=VERTEX_POINT('',#1245);
#1248=VERTEX_POINT('',#1246);
#1249=CARTESIAN_POINT('',(-7.990381056767E0,0.E0,4.25E1));
#1250=CARTESIAN_POINT('',(-1.799038105677E1,0.E0,4.25E1));
#1251=VERTEX_POINT('',#1249);
#1252=VERTEX_POINT('',#1250);
#1253=CARTESIAN_POINT('',(-7.990381056767E0,1.E1,4.25E1));
#1254=CARTESIAN_POINT('',(-1.799038105677E1,1.E1,4.25E1));
#1255=VERTEX_POINT('',#1253);
#1256=VERTEX_POINT('',#1254);
#1257=CARTESIAN_POINT('',(1.799038105677E1,0.E0,4.25E1));
#1258=CARTESIAN_POINT('',(7.990381056767E0,0.E0,4.25E1));
#1259=VERTEX_POINT('',#1257);
#1260=VERTEX_POINT('',#1258);
#1261=CARTESIAN_POINT('',(1.799038105677E1,1.E1,4.25E1));
#1262=CARTESIAN_POINT('',(7.990381056767E0,1.E1,4.25E1));
#1263=VERTEX_POINT('',#1261);
#1264=VERTEX_POINT('',#1262);
#1265=CARTESIAN_POINT('',(-5.E0,4.E1,0.E0));
#1266=CARTESIAN_POINT('',(5.E0,4.E1,0.E0));
#1267=VERTEX_POINT('',#1265);
#1268=VERTEX_POINT('',#1266);
#1269=CARTESIAN_POINT('',(-5.E0,4.E1,1.E1));
#1270=CARTESIAN_POINT('',(5.E0,4.E1,1.E1));
#1271=VERTEX_POINT('',#1269);
#1272=VERTEX_POINT('',#1270);
#1273=CARTESIAN_POINT('',(-5.E1,0.E0,0.E0));
#1274=DIRECTION('',(1.E0,0.E0,0.E0));
#1275=DIRECTION('',(0.E0,0.E0,1.E0));
#1276=AXIS2_PLACEMENT_3D('',#1273,#1274,#1275);
#1277=PLANE('',#1276);
#1279=ORIENTED_EDGE('',*,*,#1278,.T.);
#1281=ORIENTED_EDGE('',*,*,#1280,.T.);
#1283=ORIENTED_EDGE('',*,*,#1282,.T.);
#1285=ORIENTED_EDGE('',*,*,#1284,.T.);
#1287=ORIENTED_EDGE('',*,*,#1286,.T.);
#1289=ORIENTED_EDGE('',*,*,#1288,.T.);
#1290=EDGE_LOOP('',(#1279,#1281,#1283,#1285,#1287,#1289));
#1291=FACE_OUTER_BOUND('',#1290,.F.);
#1293=CARTESIAN_POINT('',(-5.E1,0.E0,5.E1));
#1294=DIRECTION('',(0.E0,-1.E0,0.E0));
#1295=DIRECTION('',(0.E0,0.E0,-1.E0));
#1296=AXIS2_PLACEMENT_3D('',#1293,#1294,#1295);
#1297=PLANE('',#1296);
#1298=ORIENTED_EDGE('',*,*,#1278,.F.);
#1300=ORIENTED_EDGE('',*,*,#1299,.T.);
#1302=ORIENTED_EDGE('',*,*,#1301,.T.);
#1304=ORIENTED_EDGE('',*,*,#1303,.F.);
#1305=EDGE_LOOP('',(#1298,#1300,#1302,#1304));
#1306=FACE_OUTER_BOUND('',#1305,.F.);
#1308=ORIENTED_EDGE('',*,*,#1307,.T.);
#1310=ORIENTED_EDGE('',*,*,#1309,.T.);
#1311=EDGE_LOOP('',(#1308,#1310));
#1312=FACE_BOUND('',#1311,.F.);
#1314=ORIENTED_EDGE('',*,*,#1313,.T.);
#1316=ORIENTED_EDGE('',*,*,#1315,.T.);
#1317=EDGE_LOOP('',(#1314,#1316));
#1318=FACE_BOUND('',#1317,.F.);
#1320=ORIENTED_EDGE('',*,*,#1319,.T.);
#1322=ORIENTED_EDGE('',*,*,#1321,.T.);
#1323=EDGE_LOOP('',(#1320,#1322));
#1324=FACE_BOUND('',#1323,.F.);
#1326=CARTESIAN_POINT('',(1.299038105677E1,1.E1,4.25E1));
#1327=DIRECTION('',(0.E0,-1.E0,0.E0));
#1328=DIRECTION('',(1.E0,0.E0,0.E0));
#1329=AXIS2_PLACEMENT_3D('',#1326,#1327,#1328);
#1330=CYLINDRICAL_SURFACE('',#1329,5.E0);
#1331=ORIENTED_EDGE('',*,*,#1307,.F.);
#1333=ORIENTED_EDGE('',*,*,#1332,.F.);
#1335=ORIENTED_EDGE('',*,*,#1334,.T.);
#1337=ORIENTED_EDGE('',*,*,#1336,.T.);
#1338=EDGE_LOOP('',(#1331,#1333,#1335,#1337));
#1339=FACE_OUTER_BOUND('',#1338,.F.);
#1341=CARTESIAN_POINT('',(1.299038105677E1,1.E1,4.25E1));
#1342=DIRECTION('',(0.E0,-1.E0,0.E0));
#1343=DIRECTION('',(1.E0,0.E0,0.E0));
#1344=AXIS2_PLACEMENT_3D('',#1341,#1342,#1343);
#1345=CYLINDRICAL_SURFACE('',#1344,5.E0);
#1346=ORIENTED_EDGE('',*,*,#1309,.F.);
#1347=ORIENTED_EDGE('',*,*,#1336,.F.);
#1349=ORIENTED_EDGE('',*,*,#1348,.T.);
#1350=ORIENTED_EDGE('',*,*,#1332,.T.);
#1351=EDGE_LOOP('',(#1346,#1347,#1349,#1350));
#1352=FACE_OUTER_BOUND('',#1351,.F.);
#1354=CARTESIAN_POINT('',(-5.E1,1.E1,1.E1));
#1355=DIRECTION('',(0.E0,1.E0,0.E0));
#1356=DIRECTION('',(0.E0,0.E0,1.E0));
#1357=AXIS2_PLACEMENT_3D('',#1354,#1355,#1356);
#1358=PLANE('',#1357);
#1359=ORIENTED_EDGE('',*,*,#1286,.F.);
#1361=ORIENTED_EDGE('',*,*,#1360,.T.);
#1363=ORIENTED_EDGE('',*,*,#1362,.T.);
#1365=ORIENTED_EDGE('',*,*,#1364,.F.);
#1366=EDGE_LOOP('',(#1359,#1361,#1363,#1365));
#1367=FACE_OUTER_BOUND('',#1366,.F.);
#1368=ORIENTED_EDGE('',*,*,#1334,.F.);
#1369=ORIENTED_EDGE('',*,*,#1348,.F.);
#1370=EDGE_LOOP('',(#1368,#1369));
#1371=FACE_BOUND('',#1370,.F.);
#1373=ORIENTED_EDGE('',*,*,#1372,.F.);
#1375=ORIENTED_EDGE('',*,*,#1374,.F.);
#1376=EDGE_LOOP('',(#1373,#1375));
#1377=FACE_BOUND('',#1376,.F.);
#1379=ORIENTED_EDGE('',*,*,#1378,.F.);
#1381=ORIENTED_EDGE('',*,*,#1380,.F.);
#1382=EDGE_LOOP('',(#1379,#1381));
#1383=FACE_BOUND('',#1382,.F.);
#1385=CARTESIAN_POINT('',(-1.299038105677E1,1.E1,4.25E1));
#1386=DIRECTION('',(0.E0,-1.E0,0.E0));
#1387=DIRECTION('',(1.E0,0.E0,0.E0));
#1388=AXIS2_PLACEMENT_3D('',#1385,#1386,#1387);
#1389=CYLINDRICAL_SURFACE('',#1388,5.E0);
#1390=ORIENTED_EDGE('',*,*,#1313,.F.);
#1392=ORIENTED_EDGE('',*,*,#1391,.F.);
#1393=ORIENTED_EDGE('',*,*,#1372,.T.);
#1395=ORIENTED_EDGE('',*,*,#1394,.T.);
#1396=EDGE_LOOP('',(#1390,#1392,#1393,#1395));
#1397=FACE_OUTER_BOUND('',#1396,.F.);
#1399=CARTESIAN_POINT('',(-1.299038105677E1,1.E1,4.25E1));
#1400=DIRECTION('',(0.E0,-1.E0,0.E0));
#1401=DIRECTION('',(1.E0,0.E0,0.E0));
#1402=AXIS2_PLACEMENT_3D('',#1399,#1400,#1401);
#1403=CYLINDRICAL_SURFACE('',#1402,5.E0);
#1404=ORIENTED_EDGE('',*,*,#1315,.F.);
#1405=ORIENTED_EDGE('',*,*,#1394,.F.);
#1406=ORIENTED_EDGE('',*,*,#1374,.T.);
#1407=ORIENTED_EDGE('',*,*,#1391,.T.);
#1408=EDGE_LOOP('',(#1404,#1405,#1406,#1407));
#1409=FACE_OUTER_BOUND('',#1408,.F.);
#1411=CARTESIAN_POINT('',(-5.E1,6.E1,1.E1));
#1412=DIRECTION('',(0.E0,0.E0,1.E0));
#1413=DIRECTION('',(0.E0,-1.E0,0.E0));
#1414=AXIS2_PLACEMENT_3D('',#1411,#1412,#1413);
#1415=PLANE('',#1414);
#1416=ORIENTED_EDGE('',*,*,#1284,.F.);
#1418=ORIENTED_EDGE('',*,*,#1417,.T.);
#1420=ORIENTED_EDGE('',*,*,#1419,.T.);
#1421=ORIENTED_EDGE('',*,*,#1360,.F.);
#1422=EDGE_LOOP('',(#1416,#1418,#1420,#1421));
#1423=FACE_OUTER_BOUND('',#1422,.F.);
#1425=ORIENTED_EDGE('',*,*,#1424,.F.);
#1427=ORIENTED_EDGE('',*,*,#1426,.F.);
#1428=EDGE_LOOP('',(#1425,#1427));
#1429=FACE_BOUND('',#1428,.F.);
#1431=CARTESIAN_POINT('',(-5.E1,6.E1,0.E0));
#1432=DIRECTION('',(0.E0,1.E0,0.E0));
#1433=DIRECTION('',(0.E0,0.E0,1.E0));
#1434=AXIS2_PLACEMENT_3D('',#1431,#1432,#1433);
#1435=PLANE('',#1434);
#1436=ORIENTED_EDGE('',*,*,#1282,.F.);
#1438=ORIENTED_EDGE('',*,*,#1437,.T.);
#1440=ORIENTED_EDGE('',*,*,#1439,.T.);
#1441=ORIENTED_EDGE('',*,*,#1417,.F.);
#1442=EDGE_LOOP('',(#1436,#1438,#1440,#1441));
#1443=FACE_OUTER_BOUND('',#1442,.F.);
#1445=CARTESIAN_POINT('',(-5.E1,0.E0,0.E0));
#1446=DIRECTION('',(0.E0,0.E0,-1.E0));
#1447=DIRECTION('',(0.E0,1.E0,0.E0));
#1448=AXIS2_PLACEMENT_3D('',#1445,#1446,#1447);
#1449=PLANE('',#1448);
#1450=ORIENTED_EDGE('',*,*,#1280,.F.);
#1451=ORIENTED_EDGE('',*,*,#1303,.T.);
#1453=ORIENTED_EDGE('',*,*,#1452,.T.);
#1454=ORIENTED_EDGE('',*,*,#1437,.F.);
#1455=EDGE_LOOP('',(#1450,#1451,#1453,#1454));
#1456=FACE_OUTER_BOUND('',#1455,.F.);
#1458=ORIENTED_EDGE('',*,*,#1457,.T.);
#1460=ORIENTED_EDGE('',*,*,#1459,.T.);
#1461=EDGE_LOOP('',(#1458,#1460));
#1462=FACE_BOUND('',#1461,.F.);
#1464=CARTESIAN_POINT('',(5.E1,0.E0,0.E0));
#1465=DIRECTION('',(1.E0,0.E0,0.E0));
#1466=DIRECTION('',(0.E0,0.E0,1.E0));
#1467=AXIS2_PLACEMENT_3D('',#1464,#1465,#1466);
#1468=PLANE('',#1467);
#1469=ORIENTED_EDGE('',*,*,#1301,.F.);
#1471=ORIENTED_EDGE('',*,*,#1470,.F.);
#1472=ORIENTED_EDGE('',*,*,#1362,.F.);
#1473=ORIENTED_EDGE('',*,*,#1419,.F.);
#1474=ORIENTED_EDGE('',*,*,#1439,.F.);
#1475=ORIENTED_EDGE('',*,*,#1452,.F.);
#1476=EDGE_LOOP('',(#1469,#1471,#1472,#1473,#1474,#1475));
#1477=FACE_OUTER_BOUND('',#1476,.F.);
#1479=CARTESIAN_POINT('',(-5.E1,1.E1,5.E1));
#1480=DIRECTION('',(0.E0,0.E0,1.E0));
#1481=DIRECTION('',(0.E0,-1.E0,0.E0));
#1482=AXIS2_PLACEMENT_3D('',#1479,#1480,#1481);
#1483=PLANE('',#1482);
#1484=ORIENTED_EDGE('',*,*,#1288,.F.);
#1485=ORIENTED_EDGE('',*,*,#1364,.T.);
#1486=ORIENTED_EDGE('',*,*,#1470,.T.);
#1487=ORIENTED_EDGE('',*,*,#1299,.F.);
#1488=EDGE_LOOP('',(#1484,#1485,#1486,#1487));
#1489=FACE_OUTER_BOUND('',#1488,.F.);
#1491=CARTESIAN_POINT('',(0.E0,4.E1,1.E1));
#1492=DIRECTION('',(0.E0,0.E0,-1.E0));
#1493=DIRECTION('',(-1.E0,0.E0,0.E0));
#1494=AXIS2_PLACEMENT_3D('',#1491,#1492,#1493);
#1495=CYLINDRICAL_SURFACE('',#1494,5.E0);
#1496=ORIENTED_EDGE('',*,*,#1457,.F.);
#1498=ORIENTED_EDGE('',*,*,#1497,.F.);
#1499=ORIENTED_EDGE('',*,*,#1424,.T.);
#1501=ORIENTED_EDGE('',*,*,#1500,.T.);
#1502=EDGE_LOOP('',(#1496,#1498,#1499,#1501));
#1503=FACE_OUTER_BOUND('',#1502,.F.);
#1505=CARTESIAN_POINT('',(0.E0,4.E1,1.E1));
#1506=DIRECTION('',(0.E0,0.E0,-1.E0));
#1507=DIRECTION('',(-1.E0,0.E0,0.E0));
#1508=AXIS2_PLACEMENT_3D('',#1505,#1506,#1507);
#1509=CYLINDRICAL_SURFACE('',#1508,5.E0);
#1510=ORIENTED_EDGE('',*,*,#1459,.F.);
#1511=ORIENTED_EDGE('',*,*,#1500,.F.);
#1512=ORIENTED_EDGE('',*,*,#1426,.T.);
#1513=ORIENTED_EDGE('',*,*,#1497,.T.);
#1514=EDGE_LOOP('',(#1510,#1511,#1512,#1513));
#1515=FACE_OUTER_BOUND('',#1514,.F.);
#1517=CARTESIAN_POINT('',(0.E0,1.E1,2.E1));
#1518=DIRECTION('',(0.E0,-1.E0,0.E0));
#1519=DIRECTION('',(1.E0,0.E0,0.E0));
#1520=AXIS2_PLACEMENT_3D('',#1517,#1518,#1519);
#1521=CYLINDRICAL_SURFACE('',#1520,5.E0);
#1522=ORIENTED_EDGE('',*,*,#1319,.F.);
#1524=ORIENTED_EDGE('',*,*,#1523,.F.);
#1525=ORIENTED_EDGE('',*,*,#1378,.T.);
#1527=ORIENTED_EDGE('',*,*,#1526,.T.);
#1528=EDGE_LOOP('',(#1522,#1524,#1525,#1527));
#1529=FACE_OUTER_BOUND('',#1528,.F.);
#1531=CARTESIAN_POINT('',(0.E0,1.E1,2.E1));
#1532=DIRECTION('',(0.E0,-1.E0,0.E0));
#1533=DIRECTION('',(1.E0,0.E0,0.E0));
#1534=AXIS2_PLACEMENT_3D('',#1531,#1532,#1533);
#1535=CYLINDRICAL_SURFACE('',#1534,5.E0);
#1536=ORIENTED_EDGE('',*,*,#1321,.F.);
#1537=ORIENTED_EDGE('',*,*,#1526,.F.);
#1538=ORIENTED_EDGE('',*,*,#1380,.T.);
#1539=ORIENTED_EDGE('',*,*,#1523,.T.);
#1540=EDGE_LOOP('',(#1536,#1537,#1538,#1539));
#1541=FACE_OUTER_BOUND('',#1540,.F.);
#1543=CLOSED_SHELL('',(#1292,#1325,#1340,#1353,#1384,#1398,#1410,#1430,#1444,
#1463,#1478,#1490,#1504,#1516,#1530,#1542));
#1544=MANIFOLD_SOLID_BREP('',#1543);
#1545=FILL_AREA_STYLE_COLOUR('',#12);
#1546=FILL_AREA_STYLE('',(#1545));
#1547=SURFACE_STYLE_FILL_AREA(#1546);
#1548=SURFACE_SIDE_STYLE('',(#1547));
#1549=SURFACE_STYLE_USAGE(.BOTH.,#1548);
#1550=PRESENTATION_STYLE_ASSIGNMENT((#1549));
#906=STYLED_ITEM('',(#1550),#1544);
#1551=DIRECTION('',(0.E0,1.E0,0.E0));
#1552=VECTOR('',#1551,6.E1);
#1553=CARTESIAN_POINT('',(0.E0,0.E0,3.5E1));
#1554=LINE('',#1553,#1552);
#1556=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1557=PRESENTATION_STYLE_ASSIGNMENT((#1556));
#1558=STYLED_ITEM('',(#1557),#1555);
#1560=DIRECTION('',(0.E0,-1.E0,0.E0));
#1561=VECTOR('',#1560,1.E1);
#1562=CARTESIAN_POINT('',(0.E0,1.E1,2.E1));
#1563=LINE('',#1562,#1561);
#1565=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1566=PRESENTATION_STYLE_ASSIGNMENT((#1565));
#1567=STYLED_ITEM('',(#1566),#1564);
#1568=DIRECTION('',(0.E0,-1.E0,0.E0));
#1569=VECTOR('',#1568,1.E1);
#1570=CARTESIAN_POINT('',(-1.299038105677E1,1.E1,4.25E1));
#1571=LINE('',#1570,#1569);
#1573=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1574=PRESENTATION_STYLE_ASSIGNMENT((#1573));
#1575=STYLED_ITEM('',(#1574),#1572);
#1576=DIRECTION('',(0.E0,-1.E0,0.E0));
#1577=VECTOR('',#1576,1.E1);
#1578=CARTESIAN_POINT('',(1.299038105677E1,1.E1,4.25E1));
#1579=LINE('',#1578,#1577);
#1581=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1582=PRESENTATION_STYLE_ASSIGNMENT((#1581));
#1583=STYLED_ITEM('',(#1582),#1580);
#1584=DIRECTION('',(0.E0,0.E0,-1.E0));
#1585=VECTOR('',#1584,1.E1);
#1586=CARTESIAN_POINT('',(0.E0,4.E1,1.E1));
#1587=LINE('',#1586,#1585);
#1589=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1590=PRESENTATION_STYLE_ASSIGNMENT((#1589));
#1591=STYLED_ITEM('',(#1590),#1588);
#1592=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#1593=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#1594=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#1593);
#1595=(CONVERSION_BASED_UNIT('INCH',#1594)LENGTH_UNIT()NAMED_UNIT(#1592));
#1596=DIMENSIONAL_EXPONENTS(0.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#1597=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#1598=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#1597);
#1599=(CONVERSION_BASED_UNIT('DEGREE',#1598)NAMED_UNIT(#1596)PLANE_ANGLE_UNIT(
));
#1600=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#1601=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.268806999735E-2),#1595,
'closure',
'Maximum model space distance between geometric entities at asserted connectivities');
#1603=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1604=DIRECTION('',(0.E0,0.E0,1.E0));
#1605=DIRECTION('',(1.E0,0.E0,0.E0));
#1559=GEOMETRIC_SET('',(#1555,#1564,#1572,#1580,#1588));
#1608=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1609=DIRECTION('',(0.E0,0.E0,1.E0));
#1610=DIRECTION('',(1.E0,0.E0,0.E0));
#1613=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1614=DIRECTION('',(0.E0,0.E0,1.E0));
#1615=DIRECTION('',(1.E0,0.E0,0.E0));
#1618=SHAPE_REPRESENTATION_RELATIONSHIP('','',#1617,#1607);
#1619=SHAPE_REPRESENTATION_RELATIONSHIP('','',#1617,#1612);
#1620=MECHANICAL_CONTEXT('',#846,'mechanical');
#1621=PRODUCT('L-BRACKET','L-BRACKET','NOT SPECIFIED',(#1620));
#1622=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('2','LAST_VERSION',
#1621,.MADE.);
#1629=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#1630=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#1631=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#1630);
#1632=(CONVERSION_BASED_UNIT('INCH',#1631)LENGTH_UNIT()NAMED_UNIT(#1629));
#1633=DERIVED_UNIT_ELEMENT(#1632,2.E0);
#1634=DERIVED_UNIT((#1633));
#1635=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
2.462831907361E4),#1634);
#1639=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#1640=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#1641=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#1640);
#1642=(CONVERSION_BASED_UNIT('INCH',#1641)LENGTH_UNIT()NAMED_UNIT(#1639));
#1643=DERIVED_UNIT_ELEMENT(#1642,3.E0);
#1644=DERIVED_UNIT((#1643));
#1645=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
9.685840553253E4),#1644);
#1649=CARTESIAN_POINT('centre point',(2.832791186635E-5,2.020272290231E1,
1.459457709419E1));
#1659=CARTESIAN_POINT('centre point',(2.832791186635E-5,2.020272290231E1,
1.459457709419E1));
#1663=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1664=DIRECTION('',(0.E0,0.E0,1.E0));
#1665=DIRECTION('',(1.E0,0.E0,0.E0));
#1666=AXIS2_PLACEMENT_3D('',#1663,#1664,#1665);
#1667=ITEM_DEFINED_TRANSFORMATION('','',#1616,#1666);
#1668=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1669=DIRECTION('',(0.E0,0.E0,1.E0));
#1670=DIRECTION('',(1.E0,0.E0,0.E0));
#1672=(REPRESENTATION_RELATIONSHIP('','',#1617,#1656)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1667)SHAPE_REPRESENTATION_RELATIONSHIP());
#1673=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1672,#1658);
#1674=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1675=DIRECTION('',(0.E0,0.E0,1.E0));
#1676=DIRECTION('',(1.E0,0.E0,0.E0));
#1677=AXIS2_PLACEMENT_3D('ASM_DEF_CSYS',#1674,#1675,#1676);
#1678=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#7);
#1679=PRESENTATION_STYLE_ASSIGNMENT((#1678));
#1680=STYLED_ITEM('',(#1679),#1677);
#1683=DIRECTION('',(0.E0,-1.E0,0.E0));
#1684=VECTOR('',#1683,3.7E1);
#1685=CARTESIAN_POINT('',(-5.E0,3.7E1,0.E0));
#1686=LINE('',#1685,#1684);
#1687=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1688=PRESENTATION_STYLE_ASSIGNMENT((#1687));
#1689=STYLED_ITEM('',(#1688),#1686);
#1690=DIRECTION('',(0.E0,-1.E0,0.E0));
#1691=VECTOR('',#1690,3.7E1);
#1692=CARTESIAN_POINT('',(5.E0,3.7E1,0.E0));
#1693=LINE('',#1692,#1691);
#1694=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1695=PRESENTATION_STYLE_ASSIGNMENT((#1694));
#1696=STYLED_ITEM('',(#1695),#1693);
#1697=CARTESIAN_POINT('',(0.E0,3.7E1,0.E0));
#1698=DIRECTION('',(0.E0,-1.E0,0.E0));
#1699=DIRECTION('',(-1.E0,0.E0,0.E0));
#1700=AXIS2_PLACEMENT_3D('',#1697,#1698,#1699);
#1702=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1703=PRESENTATION_STYLE_ASSIGNMENT((#1702));
#1704=STYLED_ITEM('',(#1703),#1701);
#1705=CARTESIAN_POINT('',(0.E0,3.7E1,0.E0));
#1706=DIRECTION('',(0.E0,1.E0,0.E0));
#1707=DIRECTION('',(-1.E0,0.E0,0.E0));
#1708=AXIS2_PLACEMENT_3D('',#1705,#1706,#1707);
#1710=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1711=PRESENTATION_STYLE_ASSIGNMENT((#1710));
#1712=STYLED_ITEM('',(#1711),#1709);
#1713=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1714=DIRECTION('',(0.E0,1.E0,0.E0));
#1715=DIRECTION('',(-1.E0,0.E0,0.E0));
#1716=AXIS2_PLACEMENT_3D('',#1713,#1714,#1715);
#1718=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1719=PRESENTATION_STYLE_ASSIGNMENT((#1718));
#1720=STYLED_ITEM('',(#1719),#1717);
#1721=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1722=DIRECTION('',(0.E0,-1.E0,0.E0));
#1723=DIRECTION('',(-1.E0,0.E0,0.E0));
#1724=AXIS2_PLACEMENT_3D('',#1721,#1722,#1723);
#1726=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1727=PRESENTATION_STYLE_ASSIGNMENT((#1726));
#1728=STYLED_ITEM('',(#1727),#1725);
#1729=DIRECTION('',(0.E0,-1.E0,0.E0));
#1730=VECTOR('',#1729,3.E0);
#1731=CARTESIAN_POINT('',(-7.5E0,0.E0,0.E0));
#1732=LINE('',#1731,#1730);
#1733=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1734=PRESENTATION_STYLE_ASSIGNMENT((#1733));
#1735=STYLED_ITEM('',(#1734),#1732);
#1736=DIRECTION('',(0.E0,-1.E0,0.E0));
#1737=VECTOR('',#1736,3.E0);
#1738=CARTESIAN_POINT('',(7.5E0,0.E0,0.E0));
#1739=LINE('',#1738,#1737);
#1740=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1741=PRESENTATION_STYLE_ASSIGNMENT((#1740));
#1742=STYLED_ITEM('',(#1741),#1739);
#1743=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1744=DIRECTION('',(0.E0,-1.E0,0.E0));
#1745=DIRECTION('',(-1.E0,0.E0,0.E0));
#1746=AXIS2_PLACEMENT_3D('',#1743,#1744,#1745);
#1748=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1749=PRESENTATION_STYLE_ASSIGNMENT((#1748));
#1750=STYLED_ITEM('',(#1749),#1747);
#1751=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1752=DIRECTION('',(0.E0,1.E0,0.E0));
#1753=DIRECTION('',(-1.E0,0.E0,0.E0));
#1754=AXIS2_PLACEMENT_3D('',#1751,#1752,#1753);
#1756=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1757=PRESENTATION_STYLE_ASSIGNMENT((#1756));
#1758=STYLED_ITEM('',(#1757),#1755);
#1759=CARTESIAN_POINT('',(0.E0,-3.E0,0.E0));
#1760=DIRECTION('',(0.E0,-1.E0,0.E0));
#1761=DIRECTION('',(-1.E0,0.E0,0.E0));
#1762=AXIS2_PLACEMENT_3D('',#1759,#1760,#1761);
#1764=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1765=PRESENTATION_STYLE_ASSIGNMENT((#1764));
#1766=STYLED_ITEM('',(#1765),#1763);
#1767=CARTESIAN_POINT('',(0.E0,-3.E0,0.E0));
#1768=DIRECTION('',(0.E0,1.E0,0.E0));
#1769=DIRECTION('',(-1.E0,0.E0,0.E0));
#1770=AXIS2_PLACEMENT_3D('',#1767,#1768,#1769);
#1772=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#1681);
#1773=PRESENTATION_STYLE_ASSIGNMENT((#1772));
#1774=STYLED_ITEM('',(#1773),#1771);
#1775=CARTESIAN_POINT('',(-5.E0,3.7E1,0.E0));
#1776=CARTESIAN_POINT('',(-5.E0,0.E0,0.E0));
#1777=VERTEX_POINT('',#1775);
#1778=VERTEX_POINT('',#1776);
#1779=CARTESIAN_POINT('',(-7.5E0,0.E0,0.E0));
#1780=CARTESIAN_POINT('',(-7.5E0,-3.E0,0.E0));
#1781=VERTEX_POINT('',#1779);
#1782=VERTEX_POINT('',#1780);
#1783=CARTESIAN_POINT('',(5.E0,3.7E1,0.E0));
#1784=CARTESIAN_POINT('',(5.E0,0.E0,0.E0));
#1785=VERTEX_POINT('',#1783);
#1786=VERTEX_POINT('',#1784);
#1787=CARTESIAN_POINT('',(7.5E0,0.E0,0.E0));
#1788=CARTESIAN_POINT('',(7.5E0,-3.E0,0.E0));
#1789=VERTEX_POINT('',#1787);
#1790=VERTEX_POINT('',#1788);
#1791=CARTESIAN_POINT('',(0.E0,3.7E1,0.E0));
#1792=DIRECTION('',(0.E0,1.E0,0.E0));
#1793=DIRECTION('',(1.E0,0.E0,0.E0));
#1794=AXIS2_PLACEMENT_3D('',#1791,#1792,#1793);
#1795=PLANE('',#1794);
#1797=ORIENTED_EDGE('',*,*,#1796,.T.);
#1799=ORIENTED_EDGE('',*,*,#1798,.F.);
#1800=EDGE_LOOP('',(#1797,#1799));
#1801=FACE_OUTER_BOUND('',#1800,.F.);
#1803=CARTESIAN_POINT('',(0.E0,-5.E0,0.E0));
#1804=DIRECTION('',(0.E0,1.E0,0.E0));
#1805=DIRECTION('',(1.E0,0.E0,0.E0));
#1806=AXIS2_PLACEMENT_3D('',#1803,#1804,#1805);
#1807=CYLINDRICAL_SURFACE('',#1806,5.E0);
#1809=ORIENTED_EDGE('',*,*,#1808,.T.);
#1811=ORIENTED_EDGE('',*,*,#1810,.T.);
#1813=ORIENTED_EDGE('',*,*,#1812,.F.);
#1814=ORIENTED_EDGE('',*,*,#1796,.F.);
#1815=EDGE_LOOP('',(#1809,#1811,#1813,#1814));
#1816=FACE_OUTER_BOUND('',#1815,.F.);
#1818=CARTESIAN_POINT('',(0.E0,-5.E0,0.E0));
#1819=DIRECTION('',(0.E0,1.E0,0.E0));
#1820=DIRECTION('',(1.E0,0.E0,0.E0));
#1821=AXIS2_PLACEMENT_3D('',#1818,#1819,#1820);
#1822=CYLINDRICAL_SURFACE('',#1821,5.E0);
#1823=ORIENTED_EDGE('',*,*,#1808,.F.);
#1824=ORIENTED_EDGE('',*,*,#1798,.T.);
#1825=ORIENTED_EDGE('',*,*,#1812,.T.);
#1827=ORIENTED_EDGE('',*,*,#1826,.F.);
#1828=EDGE_LOOP('',(#1823,#1824,#1825,#1827));
#1829=FACE_OUTER_BOUND('',#1828,.F.);
#1831=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1832=DIRECTION('',(0.E0,1.E0,0.E0));
#1833=DIRECTION('',(1.E0,0.E0,0.E0));
#1834=AXIS2_PLACEMENT_3D('',#1831,#1832,#1833);
#1835=PLANE('',#1834);
#1837=ORIENTED_EDGE('',*,*,#1836,.T.);
#1839=ORIENTED_EDGE('',*,*,#1838,.F.);
#1840=EDGE_LOOP('',(#1837,#1839));
#1841=FACE_OUTER_BOUND('',#1840,.F.);
#1842=ORIENTED_EDGE('',*,*,#1826,.T.);
#1843=ORIENTED_EDGE('',*,*,#1810,.F.);
#1844=EDGE_LOOP('',(#1842,#1843));
#1845=FACE_BOUND('',#1844,.F.);
#1847=CARTESIAN_POINT('',(0.E0,-5.E0,0.E0));
#1848=DIRECTION('',(0.E0,1.E0,0.E0));
#1849=DIRECTION('',(1.E0,0.E0,0.E0));
#1850=AXIS2_PLACEMENT_3D('',#1847,#1848,#1849);
#1851=CYLINDRICAL_SURFACE('',#1850,7.5E0);
#1853=ORIENTED_EDGE('',*,*,#1852,.T.);
#1855=ORIENTED_EDGE('',*,*,#1854,.T.);
#1857=ORIENTED_EDGE('',*,*,#1856,.F.);
#1858=ORIENTED_EDGE('',*,*,#1836,.F.);
#1859=EDGE_LOOP('',(#1853,#1855,#1857,#1858));
#1860=FACE_OUTER_BOUND('',#1859,.F.);
#1862=CARTESIAN_POINT('',(0.E0,-5.E0,0.E0));
#1863=DIRECTION('',(0.E0,1.E0,0.E0));
#1864=DIRECTION('',(1.E0,0.E0,0.E0));
#1865=AXIS2_PLACEMENT_3D('',#1862,#1863,#1864);
#1866=CYLINDRICAL_SURFACE('',#1865,7.5E0);
#1867=ORIENTED_EDGE('',*,*,#1852,.F.);
#1868=ORIENTED_EDGE('',*,*,#1838,.T.);
#1869=ORIENTED_EDGE('',*,*,#1856,.T.);
#1871=ORIENTED_EDGE('',*,*,#1870,.F.);
#1872=EDGE_LOOP('',(#1867,#1868,#1869,#1871));
#1873=FACE_OUTER_BOUND('',#1872,.F.);
#1875=CARTESIAN_POINT('',(0.E0,-3.E0,0.E0));
#1876=DIRECTION('',(0.E0,1.E0,0.E0));
#1877=DIRECTION('',(1.E0,0.E0,0.E0));
#1878=AXIS2_PLACEMENT_3D('',#1875,#1876,#1877);
#1879=PLANE('',#1878);
#1880=ORIENTED_EDGE('',*,*,#1854,.F.);
#1881=ORIENTED_EDGE('',*,*,#1870,.T.);
#1882=EDGE_LOOP('',(#1880,#1881));
#1883=FACE_OUTER_BOUND('',#1882,.F.);
#1885=CLOSED_SHELL('',(#1802,#1817,#1830,#1846,#1861,#1874,#1884));
#1886=MANIFOLD_SOLID_BREP('',#1885);
#1887=FILL_AREA_STYLE_COLOUR('',#1681);
#1888=FILL_AREA_STYLE('',(#1887));
#1889=SURFACE_STYLE_FILL_AREA(#1888);
#1890=SURFACE_SIDE_STYLE('',(#1889));
#1891=SURFACE_STYLE_USAGE(.BOTH.,#1890);
#1892=PRESENTATION_STYLE_ASSIGNMENT((#1891));
#1682=STYLED_ITEM('',(#1892),#1886);
#1893=DIRECTION('',(0.E0,1.E0,0.E0));
#1894=VECTOR('',#1893,4.E1);
#1895=CARTESIAN_POINT('',(0.E0,-3.E0,0.E0));
#1896=LINE('',#1895,#1894);
#1898=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#1899=PRESENTATION_STYLE_ASSIGNMENT((#1898));
#1900=STYLED_ITEM('',(#1899),#1897);
#1902=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#1903=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#1904=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#1903);
#1905=(CONVERSION_BASED_UNIT('INCH',#1904)LENGTH_UNIT()NAMED_UNIT(#1902));
#1906=DIMENSIONAL_EXPONENTS(0.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#1907=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#1908=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#1907);
#1909=(CONVERSION_BASED_UNIT('DEGREE',#1908)NAMED_UNIT(#1906)PLANE_ANGLE_UNIT(
));
#1910=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#1911=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(4.527511461366E-3),#1905,
'closure',
'Maximum model space distance between geometric entities at asserted connectivities');
#1913=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1914=DIRECTION('',(0.E0,0.E0,1.E0));
#1915=DIRECTION('',(1.E0,0.E0,0.E0));
#1901=GEOMETRIC_SET('',(#1897));
#1918=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1919=DIRECTION('',(0.E0,0.E0,1.E0));
#1920=DIRECTION('',(1.E0,0.E0,0.E0));
#1923=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1924=DIRECTION('',(0.E0,0.E0,1.E0));
#1925=DIRECTION('',(1.E0,0.E0,0.E0));
#1928=SHAPE_REPRESENTATION_RELATIONSHIP('','',#1927,#1917);
#1929=SHAPE_REPRESENTATION_RELATIONSHIP('','',#1927,#1922);
#1930=DESIGN_CONTEXT('',#846,'design');
#1931=MECHANICAL_CONTEXT('',#846,'mechanical');
#1932=PRODUCT('BOLT','BOLT','NOT SPECIFIED',(#1931));
#1933=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('2','LAST_VERSION',
#1932,.MADE.);
#1940=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#1941=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#1942=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#1941);
#1943=(CONVERSION_BASED_UNIT('INCH',#1942)LENGTH_UNIT()NAMED_UNIT(#1940));
#1944=DERIVED_UNIT_ELEMENT(#1943,2.E0);
#1945=DERIVED_UNIT((#1944));
#1946=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
1.657190124769E3),#1945);
#1950=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#1951=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#1952=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#1951);
#1953=(CONVERSION_BASED_UNIT('INCH',#1952)LENGTH_UNIT()NAMED_UNIT(#1950));
#1954=DERIVED_UNIT_ELEMENT(#1953,3.E0);
#1955=DERIVED_UNIT((#1954));
#1956=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
3.436116963554E3),#1955);
#1960=CARTESIAN_POINT('centre point',(0.E0,1.541428571282E1,0.E0));
#1970=CARTESIAN_POINT('centre point',(0.E0,1.541428571282E1,0.E0));
#1974=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1975=DIRECTION('',(0.E0,0.E0,1.E0));
#1976=DIRECTION('',(1.E0,0.E0,0.E0));
#1977=AXIS2_PLACEMENT_3D('',#1974,#1975,#1976);
#1978=ITEM_DEFINED_TRANSFORMATION('','',#1926,#1977);
#1979=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#1980=DIRECTION('',(0.E0,0.E0,1.E0));
#1981=DIRECTION('',(1.E0,0.E0,0.E0));
#1983=(REPRESENTATION_RELATIONSHIP('','',#1927,#1967)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1978)SHAPE_REPRESENTATION_RELATIONSHIP());
#1984=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1983,#1969);
#1986=DIRECTION('',(0.E0,1.E0,0.E0));
#1987=VECTOR('',#1986,3.E0);
#1988=CARTESIAN_POINT('',(1.E1,-3.E0,-7.5E0));
#1989=LINE('',#1988,#1987);
#1990=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#1991=PRESENTATION_STYLE_ASSIGNMENT((#1990));
#1992=STYLED_ITEM('',(#1991),#1989);
#1993=DIRECTION('',(-1.E0,0.E0,0.E0));
#1994=VECTOR('',#1993,2.E1);
#1995=CARTESIAN_POINT('',(1.E1,0.E0,-7.5E0));
#1996=LINE('',#1995,#1994);
#1997=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#1998=PRESENTATION_STYLE_ASSIGNMENT((#1997));
#1999=STYLED_ITEM('',(#1998),#1996);
#2000=DIRECTION('',(0.E0,-1.E0,0.E0));
#2001=VECTOR('',#2000,3.E0);
#2002=CARTESIAN_POINT('',(-1.E1,0.E0,-7.5E0));
#2003=LINE('',#2002,#2001);
#2004=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2005=PRESENTATION_STYLE_ASSIGNMENT((#2004));
#2006=STYLED_ITEM('',(#2005),#2003);
#2007=DIRECTION('',(1.E0,0.E0,0.E0));
#2008=VECTOR('',#2007,2.E1);
#2009=CARTESIAN_POINT('',(-1.E1,-3.E0,-7.5E0));
#2010=LINE('',#2009,#2008);
#2011=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2012=PRESENTATION_STYLE_ASSIGNMENT((#2011));
#2013=STYLED_ITEM('',(#2012),#2010);
#2014=DIRECTION('',(0.E0,0.E0,1.E0));
#2015=VECTOR('',#2014,1.5E1);
#2016=CARTESIAN_POINT('',(1.E1,-3.E0,-7.5E0));
#2017=LINE('',#2016,#2015);
#2018=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2019=PRESENTATION_STYLE_ASSIGNMENT((#2018));
#2020=STYLED_ITEM('',(#2019),#2017);
#2021=DIRECTION('',(0.E0,0.E0,1.E0));
#2022=VECTOR('',#2021,1.5E1);
#2023=CARTESIAN_POINT('',(-1.E1,-3.E0,-7.5E0));
#2024=LINE('',#2023,#2022);
#2025=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2026=PRESENTATION_STYLE_ASSIGNMENT((#2025));
#2027=STYLED_ITEM('',(#2026),#2024);
#2028=CARTESIAN_POINT('',(0.E0,-3.E0,0.E0));
#2029=DIRECTION('',(0.E0,-1.E0,0.E0));
#2030=DIRECTION('',(1.E0,0.E0,0.E0));
#2031=AXIS2_PLACEMENT_3D('',#2028,#2029,#2030);
#2033=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2034=PRESENTATION_STYLE_ASSIGNMENT((#2033));
#2035=STYLED_ITEM('',(#2034),#2032);
#2036=CARTESIAN_POINT('',(0.E0,-3.E0,0.E0));
#2037=DIRECTION('',(0.E0,-1.E0,0.E0));
#2038=DIRECTION('',(-1.E0,0.E0,0.E0));
#2039=AXIS2_PLACEMENT_3D('',#2036,#2037,#2038);
#2041=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2042=PRESENTATION_STYLE_ASSIGNMENT((#2041));
#2043=STYLED_ITEM('',(#2042),#2040);
#2044=DIRECTION('',(0.E0,0.E0,1.E0));
#2045=VECTOR('',#2044,1.5E1);
#2046=CARTESIAN_POINT('',(-1.E1,0.E0,-7.5E0));
#2047=LINE('',#2046,#2045);
#2048=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2049=PRESENTATION_STYLE_ASSIGNMENT((#2048));
#2050=STYLED_ITEM('',(#2049),#2047);
#2051=DIRECTION('',(0.E0,0.E0,1.E0));
#2052=VECTOR('',#2051,1.5E1);
#2053=CARTESIAN_POINT('',(1.E1,0.E0,-7.5E0));
#2054=LINE('',#2053,#2052);
#2055=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2056=PRESENTATION_STYLE_ASSIGNMENT((#2055));
#2057=STYLED_ITEM('',(#2056),#2054);
#2058=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2059=DIRECTION('',(0.E0,-1.E0,0.E0));
#2060=DIRECTION('',(1.E0,0.E0,0.E0));
#2061=AXIS2_PLACEMENT_3D('',#2058,#2059,#2060);
#2063=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2064=PRESENTATION_STYLE_ASSIGNMENT((#2063));
#2065=STYLED_ITEM('',(#2064),#2062);
#2066=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2067=DIRECTION('',(0.E0,-1.E0,0.E0));
#2068=DIRECTION('',(-1.E0,0.E0,0.E0));
#2069=AXIS2_PLACEMENT_3D('',#2066,#2067,#2068);
#2071=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2072=PRESENTATION_STYLE_ASSIGNMENT((#2071));
#2073=STYLED_ITEM('',(#2072),#2070);
#2074=DIRECTION('',(0.E0,1.E0,0.E0));
#2075=VECTOR('',#2074,3.E0);
#2076=CARTESIAN_POINT('',(1.E1,-3.E0,7.5E0));
#2077=LINE('',#2076,#2075);
#2078=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2079=PRESENTATION_STYLE_ASSIGNMENT((#2078));
#2080=STYLED_ITEM('',(#2079),#2077);
#2081=DIRECTION('',(1.E0,0.E0,0.E0));
#2082=VECTOR('',#2081,2.E1);
#2083=CARTESIAN_POINT('',(-1.E1,-3.E0,7.5E0));
#2084=LINE('',#2083,#2082);
#2085=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2086=PRESENTATION_STYLE_ASSIGNMENT((#2085));
#2087=STYLED_ITEM('',(#2086),#2084);
#2088=DIRECTION('',(0.E0,-1.E0,0.E0));
#2089=VECTOR('',#2088,3.E0);
#2090=CARTESIAN_POINT('',(-1.E1,0.E0,7.5E0));
#2091=LINE('',#2090,#2089);
#2092=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2093=PRESENTATION_STYLE_ASSIGNMENT((#2092));
#2094=STYLED_ITEM('',(#2093),#2091);
#2095=DIRECTION('',(-1.E0,0.E0,0.E0));
#2096=VECTOR('',#2095,2.E1);
#2097=CARTESIAN_POINT('',(1.E1,0.E0,7.5E0));
#2098=LINE('',#2097,#2096);
#2099=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2100=PRESENTATION_STYLE_ASSIGNMENT((#2099));
#2101=STYLED_ITEM('',(#2100),#2098);
#2102=DIRECTION('',(0.E0,-1.E0,0.E0));
#2103=VECTOR('',#2102,3.E0);
#2104=CARTESIAN_POINT('',(5.E0,0.E0,0.E0));
#2105=LINE('',#2104,#2103);
#2106=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2107=PRESENTATION_STYLE_ASSIGNMENT((#2106));
#2108=STYLED_ITEM('',(#2107),#2105);
#2109=DIRECTION('',(0.E0,-1.E0,0.E0));
#2110=VECTOR('',#2109,3.E0);
#2111=CARTESIAN_POINT('',(-5.E0,0.E0,0.E0));
#2112=LINE('',#2111,#2110);
#2113=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#11);
#2114=PRESENTATION_STYLE_ASSIGNMENT((#2113));
#2115=STYLED_ITEM('',(#2114),#2112);
#2116=CARTESIAN_POINT('',(1.E1,-3.E0,-7.5E0));
#2117=CARTESIAN_POINT('',(1.E1,0.E0,-7.5E0));
#2118=VERTEX_POINT('',#2116);
#2119=VERTEX_POINT('',#2117);
#2120=CARTESIAN_POINT('',(-1.E1,0.E0,-7.5E0));
#2121=VERTEX_POINT('',#2120);
#2122=CARTESIAN_POINT('',(-1.E1,-3.E0,-7.5E0));
#2123=VERTEX_POINT('',#2122);
#2124=CARTESIAN_POINT('',(1.E1,-3.E0,7.5E0));
#2125=CARTESIAN_POINT('',(1.E1,0.E0,7.5E0));
#2126=VERTEX_POINT('',#2124);
#2127=VERTEX_POINT('',#2125);
#2128=CARTESIAN_POINT('',(-1.E1,0.E0,7.5E0));
#2129=VERTEX_POINT('',#2128);
#2130=CARTESIAN_POINT('',(-1.E1,-3.E0,7.5E0));
#2131=VERTEX_POINT('',#2130);
#2132=CARTESIAN_POINT('',(5.E0,0.E0,0.E0));
#2133=CARTESIAN_POINT('',(-5.E0,0.E0,0.E0));
#2134=VERTEX_POINT('',#2132);
#2135=VERTEX_POINT('',#2133);
#2136=CARTESIAN_POINT('',(5.E0,-3.E0,0.E0));
#2137=CARTESIAN_POINT('',(-5.E0,-3.E0,0.E0));
#2138=VERTEX_POINT('',#2136);
#2139=VERTEX_POINT('',#2137);
#2140=CARTESIAN_POINT('',(0.E0,0.E0,-7.5E0));
#2141=DIRECTION('',(0.E0,0.E0,1.E0));
#2142=DIRECTION('',(-1.E0,0.E0,0.E0));
#2143=AXIS2_PLACEMENT_3D('',#2140,#2141,#2142);
#2144=PLANE('',#2143);
#2146=ORIENTED_EDGE('',*,*,#2145,.T.);
#2148=ORIENTED_EDGE('',*,*,#2147,.T.);
#2150=ORIENTED_EDGE('',*,*,#2149,.T.);
#2152=ORIENTED_EDGE('',*,*,#2151,.T.);
#2153=EDGE_LOOP('',(#2146,#2148,#2150,#2152));
#2154=FACE_OUTER_BOUND('',#2153,.F.);
#2156=CARTESIAN_POINT('',(1.E1,-3.E0,-7.5E0));
#2157=DIRECTION('',(1.E0,0.E0,0.E0));
#2158=DIRECTION('',(0.E0,1.E0,0.E0));
#2159=AXIS2_PLACEMENT_3D('',#2156,#2157,#2158);
#2160=PLANE('',#2159);
#2161=ORIENTED_EDGE('',*,*,#2145,.F.);
#2163=ORIENTED_EDGE('',*,*,#2162,.T.);
#2165=ORIENTED_EDGE('',*,*,#2164,.T.);
#2167=ORIENTED_EDGE('',*,*,#2166,.F.);
#2168=EDGE_LOOP('',(#2161,#2163,#2165,#2167));
#2169=FACE_OUTER_BOUND('',#2168,.F.);
#2171=CARTESIAN_POINT('',(-1.E1,-3.E0,-7.5E0));
#2172=DIRECTION('',(0.E0,-1.E0,0.E0));
#2173=DIRECTION('',(1.E0,0.E0,0.E0));
#2174=AXIS2_PLACEMENT_3D('',#2171,#2172,#2173);
#2175=PLANE('',#2174);
#2176=ORIENTED_EDGE('',*,*,#2151,.F.);
#2178=ORIENTED_EDGE('',*,*,#2177,.T.);
#2180=ORIENTED_EDGE('',*,*,#2179,.T.);
#2181=ORIENTED_EDGE('',*,*,#2162,.F.);
#2182=EDGE_LOOP('',(#2176,#2178,#2180,#2181));
#2183=FACE_OUTER_BOUND('',#2182,.F.);
#2185=ORIENTED_EDGE('',*,*,#2184,.T.);
#2187=ORIENTED_EDGE('',*,*,#2186,.T.);
#2188=EDGE_LOOP('',(#2185,#2187));
#2189=FACE_BOUND('',#2188,.F.);
#2191=CARTESIAN_POINT('',(-1.E1,0.E0,-7.5E0));
#2192=DIRECTION('',(-1.E0,0.E0,0.E0));
#2193=DIRECTION('',(0.E0,-1.E0,0.E0));
#2194=AXIS2_PLACEMENT_3D('',#2191,#2192,#2193);
#2195=PLANE('',#2194);
#2196=ORIENTED_EDGE('',*,*,#2149,.F.);
#2198=ORIENTED_EDGE('',*,*,#2197,.T.);
#2200=ORIENTED_EDGE('',*,*,#2199,.T.);
#2201=ORIENTED_EDGE('',*,*,#2177,.F.);
#2202=EDGE_LOOP('',(#2196,#2198,#2200,#2201));
#2203=FACE_OUTER_BOUND('',#2202,.F.);
#2205=CARTESIAN_POINT('',(1.E1,0.E0,-7.5E0));
#2206=DIRECTION('',(0.E0,1.E0,0.E0));
#2207=DIRECTION('',(-1.E0,0.E0,0.E0));
#2208=AXIS2_PLACEMENT_3D('',#2205,#2206,#2207);
#2209=PLANE('',#2208);
#2210=ORIENTED_EDGE('',*,*,#2147,.F.);
#2211=ORIENTED_EDGE('',*,*,#2166,.T.);
#2213=ORIENTED_EDGE('',*,*,#2212,.T.);
#2214=ORIENTED_EDGE('',*,*,#2197,.F.);
#2215=EDGE_LOOP('',(#2210,#2211,#2213,#2214));
#2216=FACE_OUTER_BOUND('',#2215,.F.);
#2218=ORIENTED_EDGE('',*,*,#2217,.F.);
#2220=ORIENTED_EDGE('',*,*,#2219,.F.);
#2221=EDGE_LOOP('',(#2218,#2220));
#2222=FACE_BOUND('',#2221,.F.);
#2224=CARTESIAN_POINT('',(0.E0,0.E0,7.5E0));
#2225=DIRECTION('',(0.E0,0.E0,1.E0));
#2226=DIRECTION('',(-1.E0,0.E0,0.E0));
#2227=AXIS2_PLACEMENT_3D('',#2224,#2225,#2226);
#2228=PLANE('',#2227);
#2229=ORIENTED_EDGE('',*,*,#2164,.F.);
#2230=ORIENTED_EDGE('',*,*,#2179,.F.);
#2231=ORIENTED_EDGE('',*,*,#2199,.F.);
#2232=ORIENTED_EDGE('',*,*,#2212,.F.);
#2233=EDGE_LOOP('',(#2229,#2230,#2231,#2232));
#2234=FACE_OUTER_BOUND('',#2233,.F.);
#2236=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2237=DIRECTION('',(0.E0,-1.E0,0.E0));
#2238=DIRECTION('',(1.E0,0.E0,0.E0));
#2239=AXIS2_PLACEMENT_3D('',#2236,#2237,#2238);
#2240=CYLINDRICAL_SURFACE('',#2239,5.E0);
#2241=ORIENTED_EDGE('',*,*,#2217,.T.);
#2243=ORIENTED_EDGE('',*,*,#2242,.T.);
#2244=ORIENTED_EDGE('',*,*,#2184,.F.);
#2246=ORIENTED_EDGE('',*,*,#2245,.F.);
#2247=EDGE_LOOP('',(#2241,#2243,#2244,#2246));
#2248=FACE_OUTER_BOUND('',#2247,.F.);
#2250=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2251=DIRECTION('',(0.E0,-1.E0,0.E0));
#2252=DIRECTION('',(1.E0,0.E0,0.E0));
#2253=AXIS2_PLACEMENT_3D('',#2250,#2251,#2252);
#2254=CYLINDRICAL_SURFACE('',#2253,5.E0);
#2255=ORIENTED_EDGE('',*,*,#2219,.T.);
#2256=ORIENTED_EDGE('',*,*,#2245,.T.);
#2257=ORIENTED_EDGE('',*,*,#2186,.F.);
#2258=ORIENTED_EDGE('',*,*,#2242,.F.);
#2259=EDGE_LOOP('',(#2255,#2256,#2257,#2258));
#2260=FACE_OUTER_BOUND('',#2259,.F.);
#2262=CLOSED_SHELL('',(#2155,#2170,#2190,#2204,#2223,#2235,#2249,#2261));
#2263=MANIFOLD_SOLID_BREP('',#2262);
#2264=FILL_AREA_STYLE_COLOUR('',#11);
#2265=FILL_AREA_STYLE('',(#2264));
#2266=SURFACE_STYLE_FILL_AREA(#2265);
#2267=SURFACE_SIDE_STYLE('',(#2266));
#2268=SURFACE_STYLE_USAGE(.BOTH.,#2267);
#2269=PRESENTATION_STYLE_ASSIGNMENT((#2268));
#1985=STYLED_ITEM('',(#2269),#2263);
#2270=DIRECTION('',(0.E0,-1.E0,0.E0));
#2271=VECTOR('',#2270,3.E0);
#2272=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2273=LINE('',#2272,#2271);
#2275=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#2276=PRESENTATION_STYLE_ASSIGNMENT((#2275));
#2277=STYLED_ITEM('',(#2276),#2274);
#2279=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2280=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2281=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2280);
#2282=(CONVERSION_BASED_UNIT('INCH',#2281)LENGTH_UNIT()NAMED_UNIT(#2279));
#2283=DIMENSIONAL_EXPONENTS(0.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2284=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#2285=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#2284);
#2286=(CONVERSION_BASED_UNIT('DEGREE',#2285)NAMED_UNIT(#2283)PLANE_ANGLE_UNIT(
));
#2287=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#2288=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.517834944976E-3),#2282,
'closure',
'Maximum model space distance between geometric entities at asserted connectivities');
#2290=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2291=DIRECTION('',(0.E0,0.E0,1.E0));
#2292=DIRECTION('',(1.E0,0.E0,0.E0));
#2278=GEOMETRIC_SET('',(#2274));
#2295=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2296=DIRECTION('',(0.E0,0.E0,1.E0));
#2297=DIRECTION('',(1.E0,0.E0,0.E0));
#2300=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2301=DIRECTION('',(0.E0,0.E0,1.E0));
#2302=DIRECTION('',(1.E0,0.E0,0.E0));
#2305=SHAPE_REPRESENTATION_RELATIONSHIP('','',#2304,#2294);
#2306=SHAPE_REPRESENTATION_RELATIONSHIP('','',#2304,#2299);
#2307=MECHANICAL_CONTEXT('',#846,'mechanical');
#2308=PRODUCT('NUT','NUT','NOT SPECIFIED',(#2307));
#2309=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('1','LAST_VERSION',
#2308,.MADE.);
#2316=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2317=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2318=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2317);
#2319=(CONVERSION_BASED_UNIT('INCH',#2318)LENGTH_UNIT()NAMED_UNIT(#2316));
#2320=DERIVED_UNIT_ELEMENT(#2319,2.E0);
#2321=DERIVED_UNIT((#2320));
#2322=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
7.471681471406E2),#2321);
#2326=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2327=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2328=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2327);
#2329=(CONVERSION_BASED_UNIT('INCH',#2328)LENGTH_UNIT()NAMED_UNIT(#2326));
#2330=DERIVED_UNIT_ELEMENT(#2329,3.E0);
#2331=DERIVED_UNIT((#2330));
#2332=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
6.643805510870E2),#2331);
#2336=CARTESIAN_POINT('centre point',(0.E0,-1.5E0,0.E0));
#2343=CARTESIAN_POINT('centre point',(0.E0,3.15E1,0.E0));
#2347=CARTESIAN_POINT('',(0.E0,3.3E1,0.E0));
#2348=DIRECTION('',(0.E0,0.E0,1.E0));
#2349=DIRECTION('',(1.E0,0.E0,0.E0));
#2350=AXIS2_PLACEMENT_3D('',#2347,#2348,#2349);
#2351=ITEM_DEFINED_TRANSFORMATION('','',#2303,#2350);
#2352=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2353=DIRECTION('',(0.E0,0.E0,1.E0));
#2354=DIRECTION('',(1.E0,0.E0,0.E0));
#2356=(REPRESENTATION_RELATIONSHIP('','',#2304,#1967)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2351)SHAPE_REPRESENTATION_RELATIONSHIP());
#2357=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2356,#2342);
#2358=PRESENTATION_LAYER_ASSIGNMENT('05__ASM_ALL_DTM_CSYS','',(#1677));
#2359=PRESENTATION_LAYER_ASSIGNMENT('05__ASM_DEF_DTM_CSYS_2','',(#1677));
#2360=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2361=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2362=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2361);
#2363=(CONVERSION_BASED_UNIT('INCH',#2362)LENGTH_UNIT()NAMED_UNIT(#2360));
#2364=DIMENSIONAL_EXPONENTS(0.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2365=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#2366=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#2365);
#2367=(CONVERSION_BASED_UNIT('DEGREE',#2366)NAMED_UNIT(#2364)PLANE_ANGLE_UNIT(
));
#2368=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#2369=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(4.716801886406E-3),#2363,
'closure',
'Maximum model space distance between geometric entities at asserted connectivities');
#2371=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2372=DIRECTION('',(0.E0,0.E0,1.E0));
#2373=DIRECTION('',(1.E0,0.E0,0.E0));
#2375=DESIGN_CONTEXT('',#846,'design');
#2376=MECHANICAL_CONTEXT('',#846,'mechanical');
#2377=PRODUCT('NUT_BOLT_ASSEMBLY_ASM','NUT_BOLT_ASSEMBLY_ASM','NOT SPECIFIED',(
#2376));
#2378=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('7','LAST_VERSION',
#2377,.MADE.);
#2380=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2381=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2382=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2381);
#2383=(CONVERSION_BASED_UNIT('INCH',#2382)LENGTH_UNIT()NAMED_UNIT(#2380));
#2384=DERIVED_UNIT_ELEMENT(#2383,2.E0);
#2385=DERIVED_UNIT((#2384));
#2386=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
2.404358271909E3),#2385);
#2390=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2391=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2392=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2391);
#2393=(CONVERSION_BASED_UNIT('INCH',#2392)LENGTH_UNIT()NAMED_UNIT(#2390));
#2394=DERIVED_UNIT_ELEMENT(#2393,3.E0);
#2395=DERIVED_UNIT((#2394));
#2396=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
4.100497514641E3),#2395);
#2400=CARTESIAN_POINT('centre point',(0.E0,1.802056353267E1,0.E0));
#2406=CARTESIAN_POINT('centre point',(0.E0,-8.020563532669E0,2.E1));
#2410=CARTESIAN_POINT('',(0.E0,1.E1,2.E1));
#2411=DIRECTION('',(0.E0,0.E0,1.E0));
#2412=DIRECTION('',(-1.E0,0.E0,0.E0));
#2413=AXIS2_PLACEMENT_3D('',#2410,#2411,#2412);
#2414=ITEM_DEFINED_TRANSFORMATION('','',#1982,#2413);
#2415=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2416=DIRECTION('',(0.E0,0.E0,1.E0));
#2417=DIRECTION('',(1.E0,0.E0,0.E0));
#2419=(REPRESENTATION_RELATIONSHIP('','',#1967,#1656)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2414)SHAPE_REPRESENTATION_RELATIONSHIP());
#2420=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2419,#2405);
#2423=CARTESIAN_POINT('centre point',(1.299038105677E1,-8.020563532669E0,
4.25E1));
#2427=CARTESIAN_POINT('',(1.299038105677E1,1.E1,4.25E1));
#2428=DIRECTION('',(0.E0,0.E0,1.E0));
#2429=DIRECTION('',(-1.E0,0.E0,0.E0));
#2430=AXIS2_PLACEMENT_3D('',#2427,#2428,#2429);
#2431=ITEM_DEFINED_TRANSFORMATION('','',#1982,#2430);
#2432=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2433=DIRECTION('',(0.E0,0.E0,1.E0));
#2434=DIRECTION('',(1.E0,0.E0,0.E0));
#2436=(REPRESENTATION_RELATIONSHIP('','',#1967,#1656)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2431)SHAPE_REPRESENTATION_RELATIONSHIP());
#2437=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2436,#2422);
#2440=CARTESIAN_POINT('centre point',(-1.299038105677E1,-8.020563532669E0,
4.25E1));
#2444=CARTESIAN_POINT('',(-1.299038105677E1,1.E1,4.25E1));
#2445=DIRECTION('',(0.E0,0.E0,1.E0));
#2446=DIRECTION('',(-1.E0,0.E0,0.E0));
#2447=AXIS2_PLACEMENT_3D('',#2444,#2445,#2446);
#2448=ITEM_DEFINED_TRANSFORMATION('','',#1982,#2447);
#2449=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2450=DIRECTION('',(0.E0,0.E0,1.E0));
#2451=DIRECTION('',(1.E0,0.E0,0.E0));
#2453=(REPRESENTATION_RELATIONSHIP('','',#1967,#1656)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2448)SHAPE_REPRESENTATION_RELATIONSHIP());
#2454=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2453,#2439);
#2455=PRESENTATION_LAYER_ASSIGNMENT('05__ASM_ALL_DTM_CSYS_1','',(#902));
#2456=PRESENTATION_LAYER_ASSIGNMENT('05__ASM_DEF_DTM_CSYS','',(#902));
#2457=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2458=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2459=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2458);
#2460=(CONVERSION_BASED_UNIT('INCH',#2459)LENGTH_UNIT()NAMED_UNIT(#2457));
#2461=DIMENSIONAL_EXPONENTS(0.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2462=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#2463=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#2462);
#2464=(CONVERSION_BASED_UNIT('DEGREE',#2463)NAMED_UNIT(#2461)PLANE_ANGLE_UNIT(
));
#2465=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#2466=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.416594314266E-2),#2460,
'closure',
'Maximum model space distance between geometric entities at asserted connectivities');
#2468=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2469=DIRECTION('',(0.E0,0.E0,1.E0));
#2470=DIRECTION('',(1.E0,0.E0,0.E0));
#2472=MECHANICAL_CONTEXT('',#846,'mechanical');
#2473=PRODUCT('L_BRACKET_ASSEMBLY_ASM','L_BRACKET_ASSEMBLY_ASM','NOT SPECIFIED',
(#2472));
#2474=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('4','LAST_VERSION',
#2473,.MADE.);
#2476=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2477=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2478=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2477);
#2479=(CONVERSION_BASED_UNIT('INCH',#2478)LENGTH_UNIT()NAMED_UNIT(#2476));
#2480=DERIVED_UNIT_ELEMENT(#2479,2.E0);
#2481=DERIVED_UNIT((#2480));
#2482=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
3.184139388934E4),#2481);
#2486=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2487=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2488=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2487);
#2489=(CONVERSION_BASED_UNIT('INCH',#2488)LENGTH_UNIT()NAMED_UNIT(#2486));
#2490=DERIVED_UNIT_ELEMENT(#2489,3.E0);
#2491=DERIVED_UNIT((#2490));
#2492=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
1.091598980765E5),#2491);
#2496=CARTESIAN_POINT('centre point',(2.513557106415E-5,1.702217259251E1,
1.689411348214E1));
#2502=CARTESIAN_POINT('centre point',(1.810588651786E1,1.702217259251E1,
2.513557106329E-5));
#2506=CARTESIAN_POINT('',(3.5E1,5.134782488891E-14,0.E0));
#2507=DIRECTION('',(-1.E0,0.E0,0.E0));
#2508=DIRECTION('',(0.E0,0.E0,1.E0));
#2509=AXIS2_PLACEMENT_3D('',#2506,#2507,#2508);
#2510=ITEM_DEFINED_TRANSFORMATION('','',#1671,#2509);
#2511=(REPRESENTATION_RELATIONSHIP('','',#1656,#885)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2510)SHAPE_REPRESENTATION_RELATIONSHIP());
#2512=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2511,#2501);
#2515=CARTESIAN_POINT('centre point',(-1.181058865179E2,1.702217259251E1,
-2.513557106386E-5));
#2519=CARTESIAN_POINT('',(-1.35E2,0.E0,0.E0));
#2520=DIRECTION('',(1.E0,0.E0,0.E0));
#2521=DIRECTION('',(0.E0,0.E0,-1.E0));
#2522=AXIS2_PLACEMENT_3D('',#2519,#2520,#2521);
#2523=ITEM_DEFINED_TRANSFORMATION('','',#1671,#2522);
#2524=(REPRESENTATION_RELATIONSHIP('','',#1656,#885)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2523)SHAPE_REPRESENTATION_RELATIONSHIP());
#2525=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2524,#2514);
#2526=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2527=DIRECTION('',(0.E0,0.E0,1.E0));
#2528=DIRECTION('',(1.E0,0.E0,0.E0));
#2529=AXIS2_PLACEMENT_3D('ASM_DEF_CSYS',#2526,#2527,#2528);
#2530=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#7);
#2531=PRESENTATION_STYLE_ASSIGNMENT((#2530));
#2532=STYLED_ITEM('',(#2531),#2529);
#2535=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2536=DIRECTION('',(1.E0,0.E0,0.E0));
#2537=DIRECTION('',(0.E0,0.E0,1.E0));
#2538=AXIS2_PLACEMENT_3D('',#2535,#2536,#2537);
#2540=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#2533);
#2541=PRESENTATION_STYLE_ASSIGNMENT((#2540));
#2542=STYLED_ITEM('',(#2541),#2539);
#2543=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2544=DIRECTION('',(1.E0,0.E0,0.E0));
#2545=DIRECTION('',(0.E0,0.E0,-1.E0));
#2546=AXIS2_PLACEMENT_3D('',#2543,#2544,#2545);
#2548=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#2533);
#2549=PRESENTATION_STYLE_ASSIGNMENT((#2548));
#2550=STYLED_ITEM('',(#2549),#2547);
#2551=DIRECTION('',(1.E0,0.E0,0.E0));
#2552=VECTOR('',#2551,2.E2);
#2553=CARTESIAN_POINT('',(0.E0,0.E0,5.E0));
#2554=LINE('',#2553,#2552);
#2555=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#2533);
#2556=PRESENTATION_STYLE_ASSIGNMENT((#2555));
#2557=STYLED_ITEM('',(#2556),#2554);
#2558=DIRECTION('',(1.E0,0.E0,0.E0));
#2559=VECTOR('',#2558,2.E2);
#2560=CARTESIAN_POINT('',(0.E0,0.E0,-5.E0));
#2561=LINE('',#2560,#2559);
#2562=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#2533);
#2563=PRESENTATION_STYLE_ASSIGNMENT((#2562));
#2564=STYLED_ITEM('',(#2563),#2561);
#2565=CARTESIAN_POINT('',(2.E2,0.E0,0.E0));
#2566=DIRECTION('',(1.E0,0.E0,0.E0));
#2567=DIRECTION('',(0.E0,0.E0,1.E0));
#2568=AXIS2_PLACEMENT_3D('',#2565,#2566,#2567);
#2570=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#2533);
#2571=PRESENTATION_STYLE_ASSIGNMENT((#2570));
#2572=STYLED_ITEM('',(#2571),#2569);
#2573=CARTESIAN_POINT('',(2.E2,0.E0,0.E0));
#2574=DIRECTION('',(1.E0,0.E0,0.E0));
#2575=DIRECTION('',(0.E0,0.E0,-1.E0));
#2576=AXIS2_PLACEMENT_3D('',#2573,#2574,#2575);
#2578=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#2533);
#2579=PRESENTATION_STYLE_ASSIGNMENT((#2578));
#2580=STYLED_ITEM('',(#2579),#2577);
#2581=CARTESIAN_POINT('',(0.E0,0.E0,5.E0));
#2582=CARTESIAN_POINT('',(0.E0,0.E0,-5.E0));
#2583=VERTEX_POINT('',#2581);
#2584=VERTEX_POINT('',#2582);
#2585=CARTESIAN_POINT('',(2.E2,0.E0,5.E0));
#2586=CARTESIAN_POINT('',(2.E2,0.E0,-5.E0));
#2587=VERTEX_POINT('',#2585);
#2588=VERTEX_POINT('',#2586);
#2589=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2590=DIRECTION('',(1.E0,0.E0,0.E0));
#2591=DIRECTION('',(0.E0,0.E0,1.E0));
#2592=AXIS2_PLACEMENT_3D('',#2589,#2590,#2591);
#2593=PLANE('',#2592);
#2595=ORIENTED_EDGE('',*,*,#2594,.T.);
#2597=ORIENTED_EDGE('',*,*,#2596,.T.);
#2598=EDGE_LOOP('',(#2595,#2597));
#2599=FACE_OUTER_BOUND('',#2598,.F.);
#2601=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2602=DIRECTION('',(1.E0,0.E0,0.E0));
#2603=DIRECTION('',(0.E0,0.E0,1.E0));
#2604=AXIS2_PLACEMENT_3D('',#2601,#2602,#2603);
#2605=CYLINDRICAL_SURFACE('',#2604,5.E0);
#2606=ORIENTED_EDGE('',*,*,#2594,.F.);
#2608=ORIENTED_EDGE('',*,*,#2607,.T.);
#2610=ORIENTED_EDGE('',*,*,#2609,.T.);
#2612=ORIENTED_EDGE('',*,*,#2611,.F.);
#2613=EDGE_LOOP('',(#2606,#2608,#2610,#2612));
#2614=FACE_OUTER_BOUND('',#2613,.F.);
#2616=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2617=DIRECTION('',(1.E0,0.E0,0.E0));
#2618=DIRECTION('',(0.E0,0.E0,1.E0));
#2619=AXIS2_PLACEMENT_3D('',#2616,#2617,#2618);
#2620=CYLINDRICAL_SURFACE('',#2619,5.E0);
#2621=ORIENTED_EDGE('',*,*,#2596,.F.);
#2622=ORIENTED_EDGE('',*,*,#2611,.T.);
#2624=ORIENTED_EDGE('',*,*,#2623,.T.);
#2625=ORIENTED_EDGE('',*,*,#2607,.F.);
#2626=EDGE_LOOP('',(#2621,#2622,#2624,#2625));
#2627=FACE_OUTER_BOUND('',#2626,.F.);
#2629=CARTESIAN_POINT('',(2.E2,0.E0,0.E0));
#2630=DIRECTION('',(1.E0,0.E0,0.E0));
#2631=DIRECTION('',(0.E0,0.E0,1.E0));
#2632=AXIS2_PLACEMENT_3D('',#2629,#2630,#2631);
#2633=PLANE('',#2632);
#2634=ORIENTED_EDGE('',*,*,#2609,.F.);
#2635=ORIENTED_EDGE('',*,*,#2623,.F.);
#2636=EDGE_LOOP('',(#2634,#2635));
#2637=FACE_OUTER_BOUND('',#2636,.F.);
#2639=CLOSED_SHELL('',(#2600,#2615,#2628,#2638));
#2640=MANIFOLD_SOLID_BREP('',#2639);
#2641=FILL_AREA_STYLE_COLOUR('',#2533);
#2642=FILL_AREA_STYLE('',(#2641));
#2643=SURFACE_STYLE_FILL_AREA(#2642);
#2644=SURFACE_SIDE_STYLE('',(#2643));
#2645=SURFACE_STYLE_USAGE(.BOTH.,#2644);
#2646=PRESENTATION_STYLE_ASSIGNMENT((#2645));
#2534=STYLED_ITEM('',(#2646),#2640);
#2647=DIRECTION('',(1.E0,0.E0,0.E0));
#2648=VECTOR('',#2647,2.E2);
#2649=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2650=LINE('',#2649,#2648);
#2652=CURVE_STYLE('',#20,POSITIVE_LENGTH_MEASURE(2.E-2),#12);
#2653=PRESENTATION_STYLE_ASSIGNMENT((#2652));
#2654=STYLED_ITEM('',(#2653),#2651);
#2656=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2657=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2658=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2657);
#2659=(CONVERSION_BASED_UNIT('INCH',#2658)LENGTH_UNIT()NAMED_UNIT(#2656));
#2660=DIMENSIONAL_EXPONENTS(0.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2661=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#2662=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#2661);
#2663=(CONVERSION_BASED_UNIT('DEGREE',#2662)NAMED_UNIT(#2660)PLANE_ANGLE_UNIT(
));
#2664=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#2665=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.004913565826E-2),#2659,
'closure',
'Maximum model space distance between geometric entities at asserted connectivities');
#2667=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2668=DIRECTION('',(0.E0,0.E0,1.E0));
#2669=DIRECTION('',(1.E0,0.E0,0.E0));
#2655=GEOMETRIC_SET('',(#2651));
#2672=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2673=DIRECTION('',(0.E0,0.E0,1.E0));
#2674=DIRECTION('',(1.E0,0.E0,0.E0));
#2677=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2678=DIRECTION('',(0.E0,0.E0,1.E0));
#2679=DIRECTION('',(1.E0,0.E0,0.E0));
#2682=SHAPE_REPRESENTATION_RELATIONSHIP('','',#2681,#2671);
#2683=SHAPE_REPRESENTATION_RELATIONSHIP('','',#2681,#2676);
#2684=DESIGN_CONTEXT('',#846,'design');
#2685=MECHANICAL_CONTEXT('',#846,'mechanical');
#2686=PRODUCT('ROD','ROD','NOT SPECIFIED',(#2685));
#2687=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('7','LAST_VERSION',
#2686,.MADE.);
#2694=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2695=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2696=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2695);
#2697=(CONVERSION_BASED_UNIT('INCH',#2696)LENGTH_UNIT()NAMED_UNIT(#2694));
#2698=DERIVED_UNIT_ELEMENT(#2697,2.E0);
#2699=DERIVED_UNIT((#2698));
#2700=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
6.440264939647E3),#2699);
#2704=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2705=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2706=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2705);
#2707=(CONVERSION_BASED_UNIT('INCH',#2706)LENGTH_UNIT()NAMED_UNIT(#2704));
#2708=DERIVED_UNIT_ELEMENT(#2707,3.E0);
#2709=DERIVED_UNIT((#2708));
#2710=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
1.570796326087E4),#2709);
#2714=CARTESIAN_POINT('centre point',(1.E2,0.E0,0.E0));
#2724=CARTESIAN_POINT('centre point',(1.E2,0.E0,0.E0));
#2728=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2729=DIRECTION('',(0.E0,0.E0,1.E0));
#2730=DIRECTION('',(1.E0,0.E0,0.E0));
#2731=AXIS2_PLACEMENT_3D('',#2728,#2729,#2730);
#2732=ITEM_DEFINED_TRANSFORMATION('','',#2680,#2731);
#2733=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2734=DIRECTION('',(0.E0,0.E0,1.E0));
#2735=DIRECTION('',(1.E0,0.E0,0.E0));
#2737=(REPRESENTATION_RELATIONSHIP('','',#2681,#2721)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2732)SHAPE_REPRESENTATION_RELATIONSHIP());
#2738=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2737,#2723);
#2741=CARTESIAN_POINT('centre point',(1.865E2,0.E0,0.E0));
#2745=CARTESIAN_POINT('',(1.85E2,0.E0,0.E0));
#2746=DIRECTION('',(0.E0,0.E0,1.E0));
#2747=DIRECTION('',(0.E0,1.E0,0.E0));
#2748=AXIS2_PLACEMENT_3D('',#2745,#2746,#2747);
#2749=ITEM_DEFINED_TRANSFORMATION('','',#2303,#2748);
#2750=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2751=DIRECTION('',(0.E0,0.E0,1.E0));
#2752=DIRECTION('',(1.E0,0.E0,0.E0));
#2754=(REPRESENTATION_RELATIONSHIP('','',#2304,#2721)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2749)SHAPE_REPRESENTATION_RELATIONSHIP());
#2755=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2754,#2740);
#2758=CARTESIAN_POINT('centre point',(1.35E1,0.E0,0.E0));
#2762=CARTESIAN_POINT('',(1.5E1,0.E0,0.E0));
#2763=DIRECTION('',(0.E0,0.E0,1.E0));
#2764=DIRECTION('',(0.E0,-1.E0,0.E0));
#2765=AXIS2_PLACEMENT_3D('',#2762,#2763,#2764);
#2766=ITEM_DEFINED_TRANSFORMATION('','',#2303,#2765);
#2767=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2768=DIRECTION('',(0.E0,0.E0,1.E0));
#2769=DIRECTION('',(1.E0,0.E0,0.E0));
#2771=(REPRESENTATION_RELATIONSHIP('','',#2304,#2721)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2766)SHAPE_REPRESENTATION_RELATIONSHIP());
#2772=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2771,#2757);
#2773=PRESENTATION_LAYER_ASSIGNMENT('05__ASM_ALL_DTM_CSYS_2','',(#2529));
#2774=PRESENTATION_LAYER_ASSIGNMENT('05__ASM_DEF_DTM_CSYS_1','',(#2529));
#2775=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2776=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2777=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2776);
#2778=(CONVERSION_BASED_UNIT('INCH',#2777)LENGTH_UNIT()NAMED_UNIT(#2775));
#2779=DIMENSIONAL_EXPONENTS(0.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2780=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#2781=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#2780);
#2782=(CONVERSION_BASED_UNIT('DEGREE',#2781)NAMED_UNIT(#2779)PLANE_ANGLE_UNIT(
));
#2783=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#2784=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.015483814497E-2),#2778,
'closure',
'Maximum model space distance between geometric entities at asserted connectivities');
#2786=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2787=DIRECTION('',(0.E0,0.E0,1.E0));
#2788=DIRECTION('',(1.E0,0.E0,0.E0));
#2790=MECHANICAL_CONTEXT('',#846,'mechanical');
#2791=PRODUCT('ROD_ASM','ROD_ASM','NOT SPECIFIED',(#2790));
#2792=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('2','LAST_VERSION',
#2791,.MADE.);
#2794=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2795=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2796=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2795);
#2797=(CONVERSION_BASED_UNIT('INCH',#2796)LENGTH_UNIT()NAMED_UNIT(#2794));
#2798=DERIVED_UNIT_ELEMENT(#2797,2.E0);
#2799=DERIVED_UNIT((#2798));
#2800=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
7.934601233928E3),#2799);
#2804=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2805=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2806=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2805);
#2807=(CONVERSION_BASED_UNIT('INCH',#2806)LENGTH_UNIT()NAMED_UNIT(#2804));
#2808=DERIVED_UNIT_ELEMENT(#2807,3.E0);
#2809=DERIVED_UNIT((#2808));
#2810=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
1.703672436304E4),#2809);
#2814=CARTESIAN_POINT('centre point',(1.E2,0.E0,0.E0));
#2820=CARTESIAN_POINT('centre point',(-5.E1,4.E1,2.298161660974E-14));
#2824=CARTESIAN_POINT('',(5.E1,4.E1,5.628830734850E-14));
#2825=DIRECTION('',(0.E0,1.E0,0.E0));
#2826=DIRECTION('',(-1.E0,0.E0,0.E0));
#2827=AXIS2_PLACEMENT_3D('',#2824,#2825,#2826);
#2828=ITEM_DEFINED_TRANSFORMATION('','',#2736,#2827);
#2829=(REPRESENTATION_RELATIONSHIP('','',#2721,#885)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2828)SHAPE_REPRESENTATION_RELATIONSHIP());
#2830=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2829,#2819);
#2831=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2832=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2833=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2832);
#2834=(CONVERSION_BASED_UNIT('INCH',#2833)LENGTH_UNIT()NAMED_UNIT(#2831));
#2835=DIMENSIONAL_EXPONENTS(0.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2836=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#2837=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(1.745329251994E-2),
#2836);
#2838=(CONVERSION_BASED_UNIT('DEGREE',#2837)NAMED_UNIT(#2835)PLANE_ANGLE_UNIT(
));
#2839=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#2840=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.646949085119E-2),#2834,
'closure',
'Maximum model space distance between geometric entities at asserted connectivities');
#2842=CARTESIAN_POINT('',(0.E0,0.E0,0.E0));
#2843=DIRECTION('',(0.E0,0.E0,1.E0));
#2844=DIRECTION('',(1.E0,0.E0,0.E0));
#2846=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#23,#30,#37,
#44,#51,#59,#67,#75,#83,#91,#99,#107,#115,#122,#130,#138,#146,#154,#161,#169,
#177,#185,#193,#201,#209,#217,#225,#232,#240,#248,#256,#264,#271,#278,#285,#292,
#299,#306,#313,#320,#327,#334,#341,#348,#355,#362,#369,#376,#15,#768,#777,#785,
#793,#801,#809,#817,#905,#913,#920,#927,#934,#941,#948,#956,#964,#972,#980,#987,
#995,#1003,#1010,#1017,#1025,#1033,#1041,#1049,#1056,#1064,#1072,#1079,#1086,
#1093,#1101,#1109,#1116,#1123,#1131,#1139,#1146,#1153,#1160,#1167,#1174,#1181,
#1188,#1195,#1202,#1209,#1216,#906,#1558,#1567,#1575,#1583,#1591,#1680,#1689,
#1696,#1704,#1712,#1720,#1728,#1735,#1742,#1750,#1758,#1766,#1774,#1682,#1900,
#1992,#1999,#2006,#2013,#2020,#2027,#2035,#2043,#2050,#2057,#2065,#2073,#2080,
#2087,#2094,#2101,#2108,#2115,#1985,#2277,#2532,#2542,#2550,#2557,#2564,#2572,
#2580,#2534,#2654),#2841);
#2847=DESIGN_CONTEXT('',#846,'design');
#2848=MECHANICAL_CONTEXT('',#846,'mechanical');
#2849=PRODUCT('AS1_PE_ASM','AS1_PE_ASM','NOT SPECIFIED',(#2848));
#2850=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('11','LAST_VERSION',
#2849,.MADE.);
#2852=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2853=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2854=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2853);
#2855=(CONVERSION_BASED_UNIT('INCH',#2854)LENGTH_UNIT()NAMED_UNIT(#2852));
#2856=DERIVED_UNIT_ELEMENT(#2855,2.E0);
#2857=DERIVED_UNIT((#2856));
#2858=MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
1.416448210971E5),#2857);
#2862=DIMENSIONAL_EXPONENTS(1.E0,0.E0,0.E0,0.E0,0.E0,0.E0,0.E0);
#2863=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#2864=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.54E1),#2863);
#2865=(CONVERSION_BASED_UNIT('INCH',#2864)LENGTH_UNIT()NAMED_UNIT(#2862));
#2866=DERIVED_UNIT_ELEMENT(#2865,3.E0);
#2867=DERIVED_UNIT((#2866));
#2868=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
7.659317382095E5),#2867);
#2872=CARTESIAN_POINT('centre point',(-5.E1,-1.185492004887E0,
1.074689651276E-4));
#2876=PRODUCT_CATEGORY('part',$);
#2877=PRODUCT_RELATED_PRODUCT_CATEGORY('assembly',$,(#2377,#2473,#2791,#2849));
#2878=PRODUCT_CATEGORY_RELATIONSHIP('','',#2876,#2877);
#2879=PRODUCT_CATEGORY('part',$);
#2880=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',$,(#850,#1621,#1932,#2308,
#2686));
#2881=PRODUCT_CATEGORY_RELATIONSHIP('','',#2879,#2880);
#1=DRAUGHTING_PRE_DEFINED_COLOUR('green');
#2=COLOUR_RGB('',1.1E-2,1.2E-2,1.E0);
#3=COLOUR_RGB('',1.1E-1,1.1E-1,1.1E-1);
#4=COLOUR_RGB('',3.92E-1,1.2E-2,1.2E-2);
#5=COLOUR_RGB('',4.1E-1,0.E0,2.2E-1);
#6=COLOUR_RGB('',5.803921568627E-1,0.E0,1.607843137255E-1);
#7=COLOUR_RGB('',6.666666666667E-1,4.627450980392E-1,2.196078431373E-1);
#8=COLOUR_RGB('',8.784E-1,9.49E-1,1.E0);
#9=COLOUR_RGB('',9.6E-1,9.6E-1,9.6E-1);
#10=COLOUR_RGB('',9.8E-1,6.27E-1,0.E0);
#11=DRAUGHTING_PRE_DEFINED_COLOUR('red');
#12=DRAUGHTING_PRE_DEFINED_COLOUR('yellow');
#13=COLOUR_RGB('',1.E0,1.E0,9.49E-1);
#14=DRAUGHTING_PRE_DEFINED_COLOUR('white');
#56=CIRCLE('',#55,5.E0);
#64=CIRCLE('',#63,5.E0);
#72=CIRCLE('',#71,5.E0);
#80=CIRCLE('',#79,5.E0);
#88=CIRCLE('',#87,5.E0);
#96=CIRCLE('',#95,5.E0);
#104=CIRCLE('',#103,5.E0);
#112=CIRCLE('',#111,5.E0);
#127=CIRCLE('',#126,5.E0);
#135=CIRCLE('',#134,5.E0);
#143=CIRCLE('',#142,5.E0);
#151=CIRCLE('',#150,5.E0);
#166=CIRCLE('',#165,5.E0);
#174=CIRCLE('',#173,5.E0);
#182=CIRCLE('',#181,5.E0);
#190=CIRCLE('',#189,5.E0);
#198=CIRCLE('',#197,5.E0);
#206=CIRCLE('',#205,5.E0);
#214=CIRCLE('',#213,5.E0);
#222=CIRCLE('',#221,5.E0);
#237=CIRCLE('',#236,5.E0);
#245=CIRCLE('',#244,5.E0);
#253=CIRCLE('',#252,5.E0);
#261=CIRCLE('',#260,5.E0);
#446=EDGE_CURVE('',#379,#380,#19,.T.);
#448=EDGE_CURVE('',#380,#382,#27,.T.);
#450=EDGE_CURVE('',#382,#384,#34,.T.);
#452=EDGE_CURVE('',#384,#379,#41,.T.);
#456=ADVANCED_FACE('',(#455),#445,.F.);
#463=EDGE_CURVE('',#379,#387,#48,.T.);
#465=EDGE_CURVE('',#387,#388,#317,.T.);
#467=EDGE_CURVE('',#380,#388,#229,.T.);
#471=ADVANCED_FACE('',(#470),#461,.T.);
#478=EDGE_CURVE('',#384,#392,#119,.T.);
#480=EDGE_CURVE('',#392,#387,#324,.T.);
#485=EDGE_CURVE('',#439,#440,#56,.T.);
#487=EDGE_CURVE('',#440,#439,#64,.T.);
#491=EDGE_CURVE('',#431,#432,#72,.T.);
#493=EDGE_CURVE('',#432,#431,#80,.T.);
#497=EDGE_CURVE('',#415,#416,#88,.T.);
#499=EDGE_CURVE('',#416,#415,#96,.T.);
#503=EDGE_CURVE('',#407,#408,#104,.T.);
#505=EDGE_CURVE('',#408,#407,#112,.T.);
#509=EDGE_CURVE('',#399,#400,#127,.T.);
#511=EDGE_CURVE('',#400,#399,#135,.T.);
#515=EDGE_CURVE('',#423,#424,#143,.T.);
#517=EDGE_CURVE('',#424,#423,#151,.T.);
#521=ADVANCED_FACE('',(#484,#490,#496,#502,#508,#514,#520),#476,.T.);
#527=EDGE_CURVE('',#435,#436,#166,.T.);
#529=EDGE_CURVE('',#435,#439,#158,.T.);
#532=EDGE_CURVE('',#436,#440,#268,.T.);
#536=ADVANCED_FACE('',(#535),#526,.F.);
#544=EDGE_CURVE('',#388,#390,#338,.T.);
#546=EDGE_CURVE('',#382,#390,#345,.T.);
#551=EDGE_CURVE('',#436,#435,#174,.T.);
#555=EDGE_CURVE('',#427,#428,#182,.T.);
#557=EDGE_CURVE('',#428,#427,#190,.T.);
#561=EDGE_CURVE('',#411,#412,#198,.T.);
#563=EDGE_CURVE('',#412,#411,#206,.T.);
#567=EDGE_CURVE('',#403,#404,#214,.T.);
#569=EDGE_CURVE('',#404,#403,#222,.T.);
#573=EDGE_CURVE('',#395,#396,#237,.T.);
#575=EDGE_CURVE('',#396,#395,#245,.T.);
#579=EDGE_CURVE('',#419,#420,#253,.T.);
#581=EDGE_CURVE('',#420,#419,#261,.T.);
#585=ADVANCED_FACE('',(#549,#554,#560,#566,#572,#578,#584),#541,.T.);
#597=ADVANCED_FACE('',(#596),#590,.F.);
#604=EDGE_CURVE('',#427,#431,#275,.T.);
#607=EDGE_CURVE('',#428,#432,#282,.T.);
#611=ADVANCED_FACE('',(#610),#602,.F.);
#623=ADVANCED_FACE('',(#622),#616,.F.);
#630=EDGE_CURVE('',#412,#416,#296,.T.);
#633=EDGE_CURVE('',#411,#415,#289,.T.);
#637=ADVANCED_FACE('',(#636),#628,.F.);
#649=ADVANCED_FACE('',(#648),#642,.F.);
#656=EDGE_CURVE('',#404,#408,#310,.T.);
#659=EDGE_CURVE('',#403,#407,#303,.T.);
#663=ADVANCED_FACE('',(#662),#654,.F.);
#675=ADVANCED_FACE('',(#674),#668,.F.);
#683=EDGE_CURVE('',#390,#392,#331,.T.);
#688=ADVANCED_FACE('',(#687),#680,.T.);
#700=ADVANCED_FACE('',(#699),#693,.T.);
#707=EDGE_CURVE('',#396,#400,#359,.T.);
#710=EDGE_CURVE('',#395,#399,#352,.T.);
#714=ADVANCED_FACE('',(#713),#705,.F.);
#726=ADVANCED_FACE('',(#725),#719,.F.);
#733=EDGE_CURVE('',#419,#423,#366,.T.);
#736=EDGE_CURVE('',#420,#424,#373,.T.);
#740=ADVANCED_FACE('',(#739),#731,.F.);
#752=ADVANCED_FACE('',(#751),#745,.F.);
#765=TRIMMED_CURVE('A_2',#764,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#774=TRIMMED_CURVE('A_3',#773,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#782=TRIMMED_CURVE('A_4',#781,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#790=TRIMMED_CURVE('A_5',#789,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#798=TRIMMED_CURVE('A_6',#797,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#806=TRIMMED_CURVE('A_7',#805,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#814=TRIMMED_CURVE('A_8',#813,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#828=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#827))GLOBAL_UNIT_ASSIGNED_CONTEXT((#821,#825,#826))REPRESENTATION_CONTEXT
('ID1','3'));
#832=AXIS2_PLACEMENT_3D('',#829,#830,#831);
#833=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#832,#754),#828);
#837=AXIS2_PLACEMENT_3D('',#834,#835,#836);
#838=GEOMETRICALLY_BOUNDED_SURFACE_SHAPE_REPRESENTATION('',(#837,#769),#828);
#842=AXIS2_PLACEMENT_3D('',#839,#840,#841);
#843=SHAPE_REPRESENTATION('',(#842),#828);
#852=PRODUCT_DEFINITION('design','',#851,#848);
#853=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR PLATE.',#852);
#854=SHAPE_REPRESENTATION('',(#754),#828);
#855=SHAPE_ASPECT('','solid data associated with PLATE',#853,.F.);
#856=PROPERTY_DEFINITION('',
'shape for solid data with which properties are associated',#855);
#857=SHAPE_DEFINITION_REPRESENTATION(#856,#854);
#865=PROPERTY_DEFINITION('geometric_validation_property','area of PLATE',#855);
#866=REPRESENTATION('surface area',(#864),#828);
#867=PROPERTY_DEFINITION_REPRESENTATION(#865,#866);
#875=PROPERTY_DEFINITION('geometric_validation_property','volume of PLATE',
#855);
#876=REPRESENTATION('volume',(#874),#828);
#877=PROPERTY_DEFINITION_REPRESENTATION(#875,#876);
#879=PROPERTY_DEFINITION('geometric_validation_property','centroid of PLATE',
#855);
#880=REPRESENTATION('centroid',(#878),#828);
#881=PROPERTY_DEFINITION_REPRESENTATION(#879,#880);
#882=SHAPE_DEFINITION_REPRESENTATION(#853,#843);
#883=SHAPE_DEFINITION_REPRESENTATION(#884,#885);
#884=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR AS1_PE_ASM.',#2851);
#885=SHAPE_REPRESENTATION('',(#895,#2509,#2522,#2827,#2845),#2841);
#886=NEXT_ASSEMBLY_USAGE_OCCURRENCE('0','Next assembly relationship','PLATE',
#2851,#852,$);
#887=PRODUCT_DEFINITION_SHAPE('Placement #0',
'Placement of PLATE with respect to AS1_PE_ASM',#886);
#889=PROPERTY_DEFINITION('geometric_validation_property','centroid of PLATE',
#887);
#890=REPRESENTATION('centroid',(#888),#828);
#891=PROPERTY_DEFINITION_REPRESENTATION(#889,#890);
#953=CIRCLE('',#952,5.E0);
#961=CIRCLE('',#960,5.E0);
#969=CIRCLE('',#968,5.E0);
#977=CIRCLE('',#976,5.E0);
#992=CIRCLE('',#991,5.E0);
#1000=CIRCLE('',#999,5.E0);
#1022=CIRCLE('',#1021,5.E0);
#1030=CIRCLE('',#1029,5.E0);
#1038=CIRCLE('',#1037,5.E0);
#1046=CIRCLE('',#1045,5.E0);
#1061=CIRCLE('',#1060,5.E0);
#1069=CIRCLE('',#1068,5.E0);
#1098=CIRCLE('',#1097,5.E0);
#1106=CIRCLE('',#1105,5.E0);
#1128=CIRCLE('',#1127,5.E0);
#1136=CIRCLE('',#1135,5.E0);
#1278=EDGE_CURVE('',#1219,#1220,#910,.T.);
#1280=EDGE_CURVE('',#1220,#1222,#917,.T.);
#1282=EDGE_CURVE('',#1222,#1224,#924,.T.);
#1284=EDGE_CURVE('',#1224,#1226,#931,.T.);
#1286=EDGE_CURVE('',#1226,#1228,#938,.T.);
#1288=EDGE_CURVE('',#1228,#1219,#945,.T.);
#1292=ADVANCED_FACE('',(#1291),#1277,.F.);
#1299=EDGE_CURVE('',#1219,#1231,#984,.T.);
#1301=EDGE_CURVE('',#1231,#1232,#1143,.T.);
#1303=EDGE_CURVE('',#1220,#1232,#1120,.T.);
#1307=EDGE_CURVE('',#1259,#1260,#953,.T.);
#1309=EDGE_CURVE('',#1260,#1259,#961,.T.);
#1313=EDGE_CURVE('',#1251,#1252,#969,.T.);
#1315=EDGE_CURVE('',#1252,#1251,#977,.T.);
#1319=EDGE_CURVE('',#1243,#1244,#992,.T.);
#1321=EDGE_CURVE('',#1244,#1243,#1000,.T.);
#1325=ADVANCED_FACE('',(#1306,#1312,#1318,#1324),#1297,.T.);
#1332=EDGE_CURVE('',#1263,#1259,#1007,.T.);
#1334=EDGE_CURVE('',#1263,#1264,#1022,.T.);
#1336=EDGE_CURVE('',#1264,#1260,#1014,.T.);
#1340=ADVANCED_FACE('',(#1339),#1330,.F.);
#1348=EDGE_CURVE('',#1264,#1263,#1030,.T.);
#1353=ADVANCED_FACE('',(#1352),#1345,.F.);
#1360=EDGE_CURVE('',#1226,#1238,#1053,.T.);
#1362=EDGE_CURVE('',#1238,#1240,#1157,.T.);
#1364=EDGE_CURVE('',#1228,#1240,#1185,.T.);
#1372=EDGE_CURVE('',#1255,#1256,#1038,.T.);
#1374=EDGE_CURVE('',#1256,#1255,#1046,.T.);
#1378=EDGE_CURVE('',#1247,#1248,#1061,.T.);
#1380=EDGE_CURVE('',#1248,#1247,#1069,.T.);
#1384=ADVANCED_FACE('',(#1367,#1371,#1377,#1383),#1358,.T.);
#1391=EDGE_CURVE('',#1255,#1251,#1076,.T.);
#1394=EDGE_CURVE('',#1256,#1252,#1083,.T.);
#1398=ADVANCED_FACE('',(#1397),#1389,.F.);
#1410=ADVANCED_FACE('',(#1409),#1403,.F.);
#1417=EDGE_CURVE('',#1224,#1236,#1090,.T.);
#1419=EDGE_CURVE('',#1236,#1238,#1164,.T.);
#1424=EDGE_CURVE('',#1271,#1272,#1098,.T.);
#1426=EDGE_CURVE('',#1272,#1271,#1106,.T.);
#1430=ADVANCED_FACE('',(#1423,#1429),#1415,.T.);
#1437=EDGE_CURVE('',#1222,#1234,#1113,.T.);
#1439=EDGE_CURVE('',#1234,#1236,#1171,.T.);
#1444=ADVANCED_FACE('',(#1443),#1435,.T.);
#1452=EDGE_CURVE('',#1232,#1234,#1178,.T.);
#1457=EDGE_CURVE('',#1267,#1268,#1128,.T.);
#1459=EDGE_CURVE('',#1268,#1267,#1136,.T.);
#1463=ADVANCED_FACE('',(#1456,#1462),#1449,.T.);
#1470=EDGE_CURVE('',#1240,#1231,#1150,.T.);
#1478=ADVANCED_FACE('',(#1477),#1468,.T.);
#1490=ADVANCED_FACE('',(#1489),#1483,.T.);
#1497=EDGE_CURVE('',#1271,#1267,#1192,.T.);
#1500=EDGE_CURVE('',#1272,#1268,#1199,.T.);
#1504=ADVANCED_FACE('',(#1503),#1495,.F.);
#1516=ADVANCED_FACE('',(#1515),#1509,.F.);
#1523=EDGE_CURVE('',#1247,#1243,#1206,.T.);
#1526=EDGE_CURVE('',#1248,#1244,#1213,.T.);
#1530=ADVANCED_FACE('',(#1529),#1521,.F.);
#1542=ADVANCED_FACE('',(#1541),#1535,.F.);
#1555=TRIMMED_CURVE('A_1',#1554,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#1564=TRIMMED_CURVE('A_2',#1563,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#1572=TRIMMED_CURVE('A_3',#1571,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#1580=TRIMMED_CURVE('A_4',#1579,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#1588=TRIMMED_CURVE('A_5',#1587,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#1602=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#1601))GLOBAL_UNIT_ASSIGNED_CONTEXT((#1595,#1599,#1600))REPRESENTATION_CONTEXT(
'ID2','3'));
#1606=AXIS2_PLACEMENT_3D('',#1603,#1604,#1605);
#1607=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#1606,#1544),#1602);
#1611=AXIS2_PLACEMENT_3D('',#1608,#1609,#1610);
#1612=GEOMETRICALLY_BOUNDED_SURFACE_SHAPE_REPRESENTATION('',(#1611,#1559),
#1602);
#1616=AXIS2_PLACEMENT_3D('',#1613,#1614,#1615);
#1617=SHAPE_REPRESENTATION('',(#1616),#1602);
#1623=PRODUCT_DEFINITION('design','',#1622,#848);
#1624=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR L-BRACKET.',#1623);
#1625=SHAPE_REPRESENTATION('',(#1544),#1602);
#1626=SHAPE_ASPECT('','solid data associated with L-BRACKET',#1624,.F.);
#1627=PROPERTY_DEFINITION('',
'shape for solid data with which properties are associated',#1626);
#1628=SHAPE_DEFINITION_REPRESENTATION(#1627,#1625);
#1636=PROPERTY_DEFINITION('geometric_validation_property','area of L-BRACKET',
#1626);
#1637=REPRESENTATION('surface area',(#1635),#1602);
#1638=PROPERTY_DEFINITION_REPRESENTATION(#1636,#1637);
#1646=PROPERTY_DEFINITION('geometric_validation_property','volume of L-BRACKET',
#1626);
#1647=REPRESENTATION('volume',(#1645),#1602);
#1648=PROPERTY_DEFINITION_REPRESENTATION(#1646,#1647);
#1650=PROPERTY_DEFINITION('geometric_validation_property',
'centroid of L-BRACKET',#1626);
#1651=REPRESENTATION('centroid',(#1649),#1602);
#1652=PROPERTY_DEFINITION_REPRESENTATION(#1650,#1651);
#1653=SHAPE_DEFINITION_REPRESENTATION(#1624,#1617);
#1654=SHAPE_DEFINITION_REPRESENTATION(#1655,#1656);
#1655=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR L_BRACKET_ASSEMBLY_ASM.',#2475);
#1656=SHAPE_REPRESENTATION('',(#1671,#1666,#2418,#2413,#2435,#2430,#2452,#2447,
#2471,#902),#2467);
#1657=NEXT_ASSEMBLY_USAGE_OCCURRENCE('1','Next assembly relationship',
'L-BRACKET',#2475,#1623,$);
#1658=PRODUCT_DEFINITION_SHAPE('Placement #1',
'Placement of L-BRACKET with respect to L_BRACKET_ASSEMBLY_ASM',#1657);
#1660=PROPERTY_DEFINITION('geometric_validation_property',
'centroid of L-BRACKET',#1658);
#1661=REPRESENTATION('centroid',(#1659),#1602);
#1662=PROPERTY_DEFINITION_REPRESENTATION(#1660,#1661);
#1671=AXIS2_PLACEMENT_3D('',#1668,#1669,#1670);
#1681=DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#1701=CIRCLE('',#1700,5.E0);
#1709=CIRCLE('',#1708,5.E0);
#1717=CIRCLE('',#1716,5.E0);
#1725=CIRCLE('',#1724,5.E0);
#1747=CIRCLE('',#1746,7.5E0);
#1755=CIRCLE('',#1754,7.5E0);
#1763=CIRCLE('',#1762,7.5E0);
#1771=CIRCLE('',#1770,7.5E0);
#1796=EDGE_CURVE('',#1777,#1785,#1701,.T.);
#1798=EDGE_CURVE('',#1777,#1785,#1709,.T.);
#1802=ADVANCED_FACE('',(#1801),#1795,.T.);
#1808=EDGE_CURVE('',#1777,#1778,#1686,.T.);
#1810=EDGE_CURVE('',#1778,#1786,#1725,.T.);
#1812=EDGE_CURVE('',#1785,#1786,#1693,.T.);
#1817=ADVANCED_FACE('',(#1816),#1807,.T.);
#1826=EDGE_CURVE('',#1778,#1786,#1717,.T.);
#1830=ADVANCED_FACE('',(#1829),#1822,.T.);
#1836=EDGE_CURVE('',#1781,#1789,#1747,.T.);
#1838=EDGE_CURVE('',#1781,#1789,#1755,.T.);
#1846=ADVANCED_FACE('',(#1841,#1845),#1835,.T.);
#1852=EDGE_CURVE('',#1781,#1782,#1732,.T.);
#1854=EDGE_CURVE('',#1782,#1790,#1763,.T.);
#1856=EDGE_CURVE('',#1789,#1790,#1739,.T.);
#1861=ADVANCED_FACE('',(#1860),#1851,.T.);
#1870=EDGE_CURVE('',#1782,#1790,#1771,.T.);
#1874=ADVANCED_FACE('',(#1873),#1866,.T.);
#1884=ADVANCED_FACE('',(#1883),#1879,.F.);
#1897=TRIMMED_CURVE('A_1',#1896,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#1912=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#1911))GLOBAL_UNIT_ASSIGNED_CONTEXT((#1905,#1909,#1910))REPRESENTATION_CONTEXT(
'ID3','3'));
#1916=AXIS2_PLACEMENT_3D('',#1913,#1914,#1915);
#1917=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#1916,#1886),#1912);
#1921=AXIS2_PLACEMENT_3D('',#1918,#1919,#1920);
#1922=GEOMETRICALLY_BOUNDED_SURFACE_SHAPE_REPRESENTATION('',(#1921,#1901),
#1912);
#1926=AXIS2_PLACEMENT_3D('',#1923,#1924,#1925);
#1927=SHAPE_REPRESENTATION('',(#1926),#1912);
#1934=PRODUCT_DEFINITION('design','',#1933,#1930);
#1935=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR BOLT.',#1934);
#1936=SHAPE_REPRESENTATION('',(#1886),#1912);
#1937=SHAPE_ASPECT('','solid data associated with BOLT',#1935,.F.);
#1938=PROPERTY_DEFINITION('',
'shape for solid data with which properties are associated',#1937);
#1939=SHAPE_DEFINITION_REPRESENTATION(#1938,#1936);
#1947=PROPERTY_DEFINITION('geometric_validation_property','area of BOLT',#1937);
#1948=REPRESENTATION('surface area',(#1946),#1912);
#1949=PROPERTY_DEFINITION_REPRESENTATION(#1947,#1948);
#1957=PROPERTY_DEFINITION('geometric_validation_property','volume of BOLT',
#1937);
#1958=REPRESENTATION('volume',(#1956),#1912);
#1959=PROPERTY_DEFINITION_REPRESENTATION(#1957,#1958);
#1961=PROPERTY_DEFINITION('geometric_validation_property','centroid of BOLT',
#1937);
#1962=REPRESENTATION('centroid',(#1960),#1912);
#1963=PROPERTY_DEFINITION_REPRESENTATION(#1961,#1962);
#1964=SHAPE_DEFINITION_REPRESENTATION(#1935,#1927);
#1965=SHAPE_DEFINITION_REPRESENTATION(#1966,#1967);
#1966=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR NUT_BOLT_ASSEMBLY_ASM.',#2379);
#1967=SHAPE_REPRESENTATION('',(#1982,#1977,#2355,#2350,#2374,#1677),#2370);
#1968=NEXT_ASSEMBLY_USAGE_OCCURRENCE('2','Next assembly relationship','BOLT',
#2379,#1934,$);
#1969=PRODUCT_DEFINITION_SHAPE('Placement #2',
'Placement of BOLT with respect to NUT_BOLT_ASSEMBLY_ASM',#1968);
#1971=PROPERTY_DEFINITION('geometric_validation_property','centroid of BOLT',
#1969);
#1972=REPRESENTATION('centroid',(#1970),#1912);
#1973=PROPERTY_DEFINITION_REPRESENTATION(#1971,#1972);
#1982=AXIS2_PLACEMENT_3D('',#1979,#1980,#1981);
#2032=CIRCLE('',#2031,5.E0);
#2040=CIRCLE('',#2039,5.E0);
#2062=CIRCLE('',#2061,5.E0);
#2070=CIRCLE('',#2069,5.E0);
#2145=EDGE_CURVE('',#2118,#2119,#1989,.T.);
#2147=EDGE_CURVE('',#2119,#2121,#1996,.T.);
#2149=EDGE_CURVE('',#2121,#2123,#2003,.T.);
#2151=EDGE_CURVE('',#2123,#2118,#2010,.T.);
#2155=ADVANCED_FACE('',(#2154),#2144,.F.);
#2162=EDGE_CURVE('',#2118,#2126,#2017,.T.);
#2164=EDGE_CURVE('',#2126,#2127,#2077,.T.);
#2166=EDGE_CURVE('',#2119,#2127,#2054,.T.);
#2170=ADVANCED_FACE('',(#2169),#2160,.T.);
#2177=EDGE_CURVE('',#2123,#2131,#2024,.T.);
#2179=EDGE_CURVE('',#2131,#2126,#2084,.T.);
#2184=EDGE_CURVE('',#2138,#2139,#2032,.T.);
#2186=EDGE_CURVE('',#2139,#2138,#2040,.T.);
#2190=ADVANCED_FACE('',(#2183,#2189),#2175,.T.);
#2197=EDGE_CURVE('',#2121,#2129,#2047,.T.);
#2199=EDGE_CURVE('',#2129,#2131,#2091,.T.);
#2204=ADVANCED_FACE('',(#2203),#2195,.T.);
#2212=EDGE_CURVE('',#2127,#2129,#2098,.T.);
#2217=EDGE_CURVE('',#2134,#2135,#2062,.T.);
#2219=EDGE_CURVE('',#2135,#2134,#2070,.T.);
#2223=ADVANCED_FACE('',(#2216,#2222),#2209,.T.);
#2235=ADVANCED_FACE('',(#2234),#2228,.T.);
#2242=EDGE_CURVE('',#2135,#2139,#2112,.T.);
#2245=EDGE_CURVE('',#2134,#2138,#2105,.T.);
#2249=ADVANCED_FACE('',(#2248),#2240,.F.);
#2261=ADVANCED_FACE('',(#2260),#2254,.F.);
#2274=TRIMMED_CURVE('A_1',#2273,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#2289=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#2288))GLOBAL_UNIT_ASSIGNED_CONTEXT((#2282,#2286,#2287))REPRESENTATION_CONTEXT(
'ID4','3'));
#2293=AXIS2_PLACEMENT_3D('',#2290,#2291,#2292);
#2294=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#2293,#2263),#2289);
#2298=AXIS2_PLACEMENT_3D('',#2295,#2296,#2297);
#2299=GEOMETRICALLY_BOUNDED_SURFACE_SHAPE_REPRESENTATION('',(#2298,#2278),
#2289);
#2303=AXIS2_PLACEMENT_3D('',#2300,#2301,#2302);
#2304=SHAPE_REPRESENTATION('',(#2303),#2289);
#2310=PRODUCT_DEFINITION('design','',#2309,#1930);
#2311=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR NUT.',#2310);
#2312=SHAPE_REPRESENTATION('',(#2263),#2289);
#2313=SHAPE_ASPECT('','solid data associated with NUT',#2311,.F.);
#2314=PROPERTY_DEFINITION('',
'shape for solid data with which properties are associated',#2313);
#2315=SHAPE_DEFINITION_REPRESENTATION(#2314,#2312);
#2323=PROPERTY_DEFINITION('geometric_validation_property','area of NUT',#2313);
#2324=REPRESENTATION('surface area',(#2322),#2289);
#2325=PROPERTY_DEFINITION_REPRESENTATION(#2323,#2324);
#2333=PROPERTY_DEFINITION('geometric_validation_property','volume of NUT',
#2313);
#2334=REPRESENTATION('volume',(#2332),#2289);
#2335=PROPERTY_DEFINITION_REPRESENTATION(#2333,#2334);
#2337=PROPERTY_DEFINITION('geometric_validation_property','centroid of NUT',
#2313);
#2338=REPRESENTATION('centroid',(#2336),#2289);
#2339=PROPERTY_DEFINITION_REPRESENTATION(#2337,#2338);
#2340=SHAPE_DEFINITION_REPRESENTATION(#2311,#2304);
#2341=NEXT_ASSEMBLY_USAGE_OCCURRENCE('3','Next assembly relationship','NUT',
#2379,#2310,$);
#2342=PRODUCT_DEFINITION_SHAPE('Placement #3',
'Placement of NUT with respect to NUT_BOLT_ASSEMBLY_ASM',#2341);
#2344=PROPERTY_DEFINITION('geometric_validation_property','centroid of NUT',
#2342);
#2345=REPRESENTATION('centroid',(#2343),#2289);
#2346=PROPERTY_DEFINITION_REPRESENTATION(#2344,#2345);
#2355=AXIS2_PLACEMENT_3D('',#2352,#2353,#2354);
#2370=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#2369))GLOBAL_UNIT_ASSIGNED_CONTEXT((#2363,#2367,#2368))REPRESENTATION_CONTEXT(
'ID5','3'));
#2374=AXIS2_PLACEMENT_3D('',#2371,#2372,#2373);
#2379=PRODUCT_DEFINITION('design','',#2378,#2375);
#2387=PROPERTY_DEFINITION('geometric_validation_property',
'area of NUT_BOLT_ASSEMBLY_ASM',#1966);
#2388=REPRESENTATION('surface area',(#2386),#2370);
#2389=PROPERTY_DEFINITION_REPRESENTATION(#2387,#2388);
#2397=PROPERTY_DEFINITION('geometric_validation_property',
'volume of NUT_BOLT_ASSEMBLY_ASM',#1966);
#2398=REPRESENTATION('volume',(#2396),#2370);
#2399=PROPERTY_DEFINITION_REPRESENTATION(#2397,#2398);
#2401=PROPERTY_DEFINITION('geometric_validation_property',
'centroid of NUT_BOLT_ASSEMBLY_ASM',#1966);
#2402=REPRESENTATION('centroid',(#2400),#2370);
#2403=PROPERTY_DEFINITION_REPRESENTATION(#2401,#2402);
#2404=NEXT_ASSEMBLY_USAGE_OCCURRENCE('4','Next assembly relationship',
'NUT_BOLT_ASSEMBLY',#2475,#2379,$);
#2405=PRODUCT_DEFINITION_SHAPE('Placement #4',
'Placement of NUT_BOLT_ASSEMBLY_ASM with respect to L_BRACKET_ASSEMBLY_ASM',
#2404);
#2407=PROPERTY_DEFINITION('geometric_validation_property',
'centroid of NUT_BOLT_ASSEMBLY',#2405);
#2408=REPRESENTATION('centroid',(#2406),#2370);
#2409=PROPERTY_DEFINITION_REPRESENTATION(#2407,#2408);
#2418=AXIS2_PLACEMENT_3D('',#2415,#2416,#2417);
#2421=NEXT_ASSEMBLY_USAGE_OCCURRENCE('5','Next assembly relationship',
'NUT_BOLT_ASSEMBLY',#2475,#2379,$);
#2422=PRODUCT_DEFINITION_SHAPE('Placement #5',
'Placement of NUT_BOLT_ASSEMBLY_ASM with respect to L_BRACKET_ASSEMBLY_ASM',
#2421);
#2424=PROPERTY_DEFINITION('geometric_validation_property',
'centroid of NUT_BOLT_ASSEMBLY',#2422);
#2425=REPRESENTATION('centroid',(#2423),#2370);
#2426=PROPERTY_DEFINITION_REPRESENTATION(#2424,#2425);
#2435=AXIS2_PLACEMENT_3D('',#2432,#2433,#2434);
#2438=NEXT_ASSEMBLY_USAGE_OCCURRENCE('6','Next assembly relationship',
'NUT_BOLT_ASSEMBLY',#2475,#2379,$);
#2439=PRODUCT_DEFINITION_SHAPE('Placement #6',
'Placement of NUT_BOLT_ASSEMBLY_ASM with respect to L_BRACKET_ASSEMBLY_ASM',
#2438);
#2441=PROPERTY_DEFINITION('geometric_validation_property',
'centroid of NUT_BOLT_ASSEMBLY',#2439);
#2442=REPRESENTATION('centroid',(#2440),#2370);
#2443=PROPERTY_DEFINITION_REPRESENTATION(#2441,#2442);
#2452=AXIS2_PLACEMENT_3D('',#2449,#2450,#2451);
#2467=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#2466))GLOBAL_UNIT_ASSIGNED_CONTEXT((#2460,#2464,#2465))REPRESENTATION_CONTEXT(
'ID6','3'));
#2471=AXIS2_PLACEMENT_3D('',#2468,#2469,#2470);
#2475=PRODUCT_DEFINITION('design','',#2474,#2375);
#2483=PROPERTY_DEFINITION('geometric_validation_property',
'area of L_BRACKET_ASSEMBLY_ASM',#1655);
#2484=REPRESENTATION('surface area',(#2482),#2467);
#2485=PROPERTY_DEFINITION_REPRESENTATION(#2483,#2484);
#2493=PROPERTY_DEFINITION('geometric_validation_property',
'volume of L_BRACKET_ASSEMBLY_ASM',#1655);
#2494=REPRESENTATION('volume',(#2492),#2467);
#2495=PROPERTY_DEFINITION_REPRESENTATION(#2493,#2494);
#2497=PROPERTY_DEFINITION('geometric_validation_property',
'centroid of L_BRACKET_ASSEMBLY_ASM',#1655);
#2498=REPRESENTATION('centroid',(#2496),#2467);
#2499=PROPERTY_DEFINITION_REPRESENTATION(#2497,#2498);
#2500=NEXT_ASSEMBLY_USAGE_OCCURRENCE('7','Next assembly relationship',
'L_BRACKET_ASSEMBLY',#2851,#2475,$);
#2501=PRODUCT_DEFINITION_SHAPE('Placement #7',
'Placement of L_BRACKET_ASSEMBLY_ASM with respect to AS1_PE_ASM',#2500);
#2503=PROPERTY_DEFINITION('geometric_validation_property',
'centroid of L_BRACKET_ASSEMBLY',#2501);
#2504=REPRESENTATION('centroid',(#2502),#2467);
#2505=PROPERTY_DEFINITION_REPRESENTATION(#2503,#2504);
#2513=NEXT_ASSEMBLY_USAGE_OCCURRENCE('8','Next assembly relationship',
'L_BRACKET_ASSEMBLY',#2851,#2475,$);
#2514=PRODUCT_DEFINITION_SHAPE('Placement #8',
'Placement of L_BRACKET_ASSEMBLY_ASM with respect to AS1_PE_ASM',#2513);
#2516=PROPERTY_DEFINITION('geometric_validation_property',
'centroid of L_BRACKET_ASSEMBLY',#2514);
#2517=REPRESENTATION('centroid',(#2515),#2467);
#2518=PROPERTY_DEFINITION_REPRESENTATION(#2516,#2517);
#2533=DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#2539=CIRCLE('',#2538,5.E0);
#2547=CIRCLE('',#2546,5.E0);
#2569=CIRCLE('',#2568,5.E0);
#2577=CIRCLE('',#2576,5.E0);
#2594=EDGE_CURVE('',#2583,#2584,#2539,.T.);
#2596=EDGE_CURVE('',#2584,#2583,#2547,.T.);
#2600=ADVANCED_FACE('',(#2599),#2593,.F.);
#2607=EDGE_CURVE('',#2583,#2587,#2554,.T.);
#2609=EDGE_CURVE('',#2587,#2588,#2569,.T.);
#2611=EDGE_CURVE('',#2584,#2588,#2561,.T.);
#2615=ADVANCED_FACE('',(#2614),#2605,.T.);
#2623=EDGE_CURVE('',#2588,#2587,#2577,.T.);
#2628=ADVANCED_FACE('',(#2627),#2620,.T.);
#2638=ADVANCED_FACE('',(#2637),#2633,.T.);
#2651=TRIMMED_CURVE('A_2',#2650,(PARAMETER_VALUE(0.E0)),(PARAMETER_VALUE(1.E0)),
.T.,.UNSPECIFIED.);
#2666=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#2665))GLOBAL_UNIT_ASSIGNED_CONTEXT((#2659,#2663,#2664))REPRESENTATION_CONTEXT(
'ID7','3'));
#2670=AXIS2_PLACEMENT_3D('',#2667,#2668,#2669);
#2671=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#2670,#2640),#2666);
#2675=AXIS2_PLACEMENT_3D('',#2672,#2673,#2674);
#2676=GEOMETRICALLY_BOUNDED_SURFACE_SHAPE_REPRESENTATION('',(#2675,#2655),
#2666);
#2680=AXIS2_PLACEMENT_3D('',#2677,#2678,#2679);
#2681=SHAPE_REPRESENTATION('',(#2680),#2666);
#2688=PRODUCT_DEFINITION('design','',#2687,#2684);
#2689=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR ROD.',#2688);
#2690=SHAPE_REPRESENTATION('',(#2640),#2666);
#2691=SHAPE_ASPECT('','solid data associated with ROD',#2689,.F.);
#2692=PROPERTY_DEFINITION('',
'shape for solid data with which properties are associated',#2691);
#2693=SHAPE_DEFINITION_REPRESENTATION(#2692,#2690);
#2701=PROPERTY_DEFINITION('geometric_validation_property','area of ROD',#2691);
#2702=REPRESENTATION('surface area',(#2700),#2666);
#2703=PROPERTY_DEFINITION_REPRESENTATION(#2701,#2702);
#2711=PROPERTY_DEFINITION('geometric_validation_property','volume of ROD',
#2691);
#2712=REPRESENTATION('volume',(#2710),#2666);
#2713=PROPERTY_DEFINITION_REPRESENTATION(#2711,#2712);
#2715=PROPERTY_DEFINITION('geometric_validation_property','centroid of ROD',
#2691);
#2716=REPRESENTATION('centroid',(#2714),#2666);
#2717=PROPERTY_DEFINITION_REPRESENTATION(#2715,#2716);
#2718=SHAPE_DEFINITION_REPRESENTATION(#2689,#2681);
#2719=SHAPE_DEFINITION_REPRESENTATION(#2720,#2721);
#2720=PRODUCT_DEFINITION_SHAPE('','SHAPE FOR ROD_ASM.',#2793);
#2721=SHAPE_REPRESENTATION('',(#2736,#2731,#2753,#2748,#2770,#2765,#2789,#2529),
#2785);
#2722=NEXT_ASSEMBLY_USAGE_OCCURRENCE('9','Next assembly relationship','ROD',
#2793,#2688,$);
#2723=PRODUCT_DEFINITION_SHAPE('Placement #9',
'Placement of ROD with respect to ROD_ASM',#2722);
#2725=PROPERTY_DEFINITION('geometric_validation_property','centroid of ROD',
#2723);
#2726=REPRESENTATION('centroid',(#2724),#2666);
#2727=PROPERTY_DEFINITION_REPRESENTATION(#2725,#2726);
#2736=AXIS2_PLACEMENT_3D('',#2733,#2734,#2735);
#2739=NEXT_ASSEMBLY_USAGE_OCCURRENCE('10','Next assembly relationship','NUT',
#2793,#2310,$);
#2740=PRODUCT_DEFINITION_SHAPE('Placement #10',
'Placement of NUT with respect to ROD_ASM',#2739);
#2742=PROPERTY_DEFINITION('geometric_validation_property','centroid of NUT',
#2740);
#2743=REPRESENTATION('centroid',(#2741),#2666);
#2744=PROPERTY_DEFINITION_REPRESENTATION(#2742,#2743);
#2753=AXIS2_PLACEMENT_3D('',#2750,#2751,#2752);
#2756=NEXT_ASSEMBLY_USAGE_OCCURRENCE('11','Next assembly relationship','NUT',
#2793,#2310,$);
#2757=PRODUCT_DEFINITION_SHAPE('Placement #11',
'Placement of NUT with respect to ROD_ASM',#2756);
#2759=PROPERTY_DEFINITION('geometric_validation_property','centroid of NUT',
#2757);
#2760=REPRESENTATION('centroid',(#2758),#2666);
#2761=PROPERTY_DEFINITION_REPRESENTATION(#2759,#2760);
#2770=AXIS2_PLACEMENT_3D('',#2767,#2768,#2769);
#2785=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#2784))GLOBAL_UNIT_ASSIGNED_CONTEXT((#2778,#2782,#2783))REPRESENTATION_CONTEXT(
'ID8','3'));
#2789=AXIS2_PLACEMENT_3D('',#2786,#2787,#2788);
#2793=PRODUCT_DEFINITION('design','',#2792,#2684);
#2801=PROPERTY_DEFINITION('geometric_validation_property','area of ROD_ASM',
#2720);
#2802=REPRESENTATION('surface area',(#2800),#2785);
#2803=PROPERTY_DEFINITION_REPRESENTATION(#2801,#2802);
#2811=PROPERTY_DEFINITION('geometric_validation_property','volume of ROD_ASM',
#2720);
#2812=REPRESENTATION('volume',(#2810),#2785);
#2813=PROPERTY_DEFINITION_REPRESENTATION(#2811,#2812);
#2815=PROPERTY_DEFINITION('geometric_validation_property','centroid of ROD_ASM',
#2720);
#2816=REPRESENTATION('centroid',(#2814),#2785);
#2817=PROPERTY_DEFINITION_REPRESENTATION(#2815,#2816);
#2818=NEXT_ASSEMBLY_USAGE_OCCURRENCE('12','Next assembly relationship','ROD',
#2851,#2793,$);
#2819=PRODUCT_DEFINITION_SHAPE('Placement #12',
'Placement of ROD_ASM with respect to AS1_PE_ASM',#2818);
#2821=PROPERTY_DEFINITION('geometric_validation_property','centroid of ROD',
#2819);
#2822=REPRESENTATION('centroid',(#2820),#2785);
#2823=PROPERTY_DEFINITION_REPRESENTATION(#2821,#2822);
#2841=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#2840))GLOBAL_UNIT_ASSIGNED_CONTEXT((#2834,#2838,#2839))REPRESENTATION_CONTEXT(
'ID9','3'));
#2845=AXIS2_PLACEMENT_3D('',#2842,#2843,#2844);
#2851=PRODUCT_DEFINITION('design','',#2850,#2847);
#2859=PROPERTY_DEFINITION('geometric_validation_property','area of AS1_PE_ASM',
#884);
#2860=REPRESENTATION('surface area',(#2858),#2841);
#2861=PROPERTY_DEFINITION_REPRESENTATION(#2859,#2860);
#2869=PROPERTY_DEFINITION('geometric_validation_property',
'volume of AS1_PE_ASM',#884);
#2870=REPRESENTATION('volume',(#2868),#2841);
#2871=PROPERTY_DEFINITION_REPRESENTATION(#2869,#2870);
#2873=PROPERTY_DEFINITION('geometric_validation_property',
'centroid of AS1_PE_ASM',#884);
#2874=REPRESENTATION('centroid',(#2872),#2841);
#2875=PROPERTY_DEFINITION_REPRESENTATION(#2873,#2874);
ENDSEC;
END-ISO-10303-21;
