<template>
  <el-card class="box-card">
    <el-tabs v-model="queryParams.resource_type" class="demo-tabs" @tab-click="handleClickTab">
      <el-tab-pane label="素材" :name="0" />
      <el-tab-pane label="预置形象" :name="1" />
      <el-tab-pane label="背景音乐" :name="2" />
    </el-tabs>
    <div class="flex">
      <el-form :inline="true" :model="queryParams" class="flex flex-wrap">
        <el-form-item label="所属CP:">
          <el-select clearable v-model="queryParams.user_id" placeholder="请选择CP">
            <el-option key="" label="全部" value="" />
            <el-option
              v-for="item in userOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              clearable
            />
          </el-select>
        </el-form-item>
        <el-form-item label="风格类型：" v-if="[0, 1].includes(queryParams.resource_type)">
          <StyleCascader
            :key="queryParams.resource_type"
            style="width: 200px"
            v-model="queryParams.style_id"
          />
        </el-form-item>
        <el-form-item label="素材类型：" v-if="queryParams.resource_type == 0">
          <el-tree-select
            clearable
            v-model="queryParams.type"
            :data="typeTreeData"
            :check-strictly="false"
            :render-after-expand="false"
            :props="{ label: 'chinese', value: 'name' }"
          />
        </el-form-item>
        <el-form-item label="统计时间:" class="w-80">
          <InnerMonthPicker
            v-model="queryParams.dateRange"
          />
        </el-form-item>
        <el-form-item label="使用次数范围:">
          <NInputNumber
            :min="0"
            :max="999999"
            :step="1"
            class="w-24"
            :show-button="false"
            clearable
            :precision="0"
            :value="queryParams.min_times"
            :on-update:value="(v) => {
              handleMinTimesChange(v)
              queryParams.min_times = v
            }"
          />
          <span class="inline mx-1">~</span>
          <NInputNumber
            :min="0"
            :max="999999"
            :step="1"
            class="w-24"
            :show-button="false"
            clearable
            :precision="0"
            :value="queryParams.max_times"
            :on-update:value="(v) => {
              handleMaxTimesChange(v)
              queryParams.max_times = v
            }"
          />
        </el-form-item>

        <el-button class="ml-auto" type="primary" @click="handleExport">导出</el-button>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%" :header-row-style="{ background: '#e5e7eb' }">
      <el-table-column label="所属CP" prop="user.name" />
      <el-table-column label="所属技术规范" prop="provider.title" />
      <el-table-column
        v-if="queryParams.resource_type == 0"
        label="素材类型"
        prop="type"
        :formatter="formatterType"
      />
      <!-- <el-table-column
        v-if="[0, 1].includes(queryParams.resource_type)"
        label="风格类型"
        prop="provider_style.title"
      /> -->
      <el-table-column label="资源总数" prop="total" />
      <el-table-column label="被使用次数" prop="usage" />
      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-button type="primary" :text="true" @click="handleView(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="flex my-4 justify-end"
      background
      v-model:page-size="queryParams.limit"
      v-model:current-page="queryParams.page"
      layout="total, prev, pager, next, sizes, jumper"
      :total="totalRef"
    />
  </el-card>
  <ModalTable @register="register" class="basicModalLight" ref="modalRef" />
</template>

<script lang="ts" setup>
  import { getAssetTypeList } from '@/api/material/material';
  import { getAuditStatistic } from '@/api/statistics';
  import { getAllUserNames } from '@/api/system/user';
  import { ModalTable, useModalTable } from '@/components/ModalTable';
  import { onMounted, reactive, ref, watch } from 'vue';
  import { useExport } from '@/views/hooks';
  import { useDebounceFn } from '@vueuse/core';
  import { useHandleMaxTimeChange } from './useHandMaxTimesChange';
  import { format } from 'date-fns';
  import { InnerMonthPicker } from '@/components/InnerMonthPicker';
  import { StyleCascader } from '@/components/StyleCascader';
  import { NInputNumber } from 'naive-ui';

  const currentRow = ref({});
  const modalRef = ref(null);

  const queryParams = reactive<any>({
    resource_type: 0,
    provider_id: '',
    style_id: '',
    user_id: '',
    type: '',
    dateRange: [
      format(new Date(new Date().setDate(1)), 'yyyy-MM-dd'),
      format(new Date(), 'yyyy-MM-dd'),
    ] as any,
    min_times: undefined,
    max_times: undefined,
    page: 1,
    limit: 10,
  });

  const [handleMaxTimesChange, handleMinTimesChange] = useHandleMaxTimeChange(queryParams);

  const { exportFile } = useExport();

  const [register, { openModal }] = useModalTable({
    title: '查看详情',
    data: currentRow,
  });

  const totalRef = ref(0);

  const tableData = ref([]);
  const allTypeOptions = ref<any>([]);
  const userOptions = ref<{ name: string; id: number }[]>([]);

  const handleClickTab = (tab) => {
    tableData.value = [];
    totalRef.value = 0;
    queryParams.resource_type = tab.props.name;
    queryParams.provider_id = '';
    queryParams.style_id = '';
    queryParams.user_id = '';
    queryParams.dateRange = [
      format(new Date(new Date().setDate(1)), 'yyyy-MM-dd'),
      format(new Date(), 'yyyy-MM-dd'),
    ];
    queryParams.min_times = undefined;
    queryParams.max_times = undefined;
    queryParams.type = '';
    queryParams.page = 1;
    queryParams.limit = 10;
  };

  const formatterType = (_row, _column, cellValue) => {
    return allTypeOptions.value?.find(({ name }) => name == cellValue)?.chinese;
  };

  const typeTreeData = ref<any>([]);

  const flatTree = (data: any = []) => {
    return data.reduce((prev, item) => {
      return [...prev, ...item.children];
    }, []);
  };

  const fetchTypeTree = async () => {
    const { code, data } = await getAssetTypeList();
    if (code == 0) {
      typeTreeData.value = [
        {
          name: '',
          chinese: '全部',
          order: -1,
          level: 1,
          parent: null,
          gender: 0,
        },
        ...data,
      ];
      allTypeOptions.value = flatTree(data);
    }
  };

  const fetchAllUserNames = async () => {
    const { code, data } = await getAllUserNames();
    if (code == 0) {
      userOptions.value = data;
    }
  };

  watch(
    () => queryParams,
    () => {
      getList();
    },
    { deep: true }
  );

  onMounted(async () => {
    await fetchTypeTree();
    await fetchAllUserNames();
    await getList();
  });

  const generateParamas = () => {
    const {
      provider_id,
      style_id,
      type,
      resource_type,
      dateRange,
      min_times,
      max_times,
      ...other
    } = queryParams;
    const params: any = { ...other, resource_type };
    if (dateRange && Array.isArray(dateRange)) {
      const [start, end] = dateRange;
      params.create_start = start;
      params.create_end = end;
    }
    if (min_times !== undefined) {
      params.min_times = min_times;
    }
    if (max_times !== undefined) {
      params.max_times = max_times;
    }
    if ([0, 1].includes(resource_type)) {
      params.style_id = style_id;
    }
    if (resource_type === 0) {
      params.type = type;
    }
    return params;
  };

  //获取列表数据
  const getList = useDebounceFn(async () => {
    const params = generateParamas();
    const {
      data: { total, rows: list },
    } = await getAuditStatistic(params);
    tableData.value = list;
    totalRef.value = total;
  }, 1000);

  //导出
  const handleExport = () => {
    const params = generateParamas();
    exportFile('/business/export-query/statistic/export/refs4audit', params);
  };
  //查看详情
  const handleView = (row) => {
    currentRow.value = row;
    openModal({
      resource_type: queryParams.resource_type,
      ...row,
    });
  };
</script>
<style lang="less" scoped>
  .box-card {
    min-height: 800px;

    .img {
      width: 60px;
      height: 60px;
    }

    .title {
      flex: 1;
      margin-left: 10px;
    }
  }
</style>
