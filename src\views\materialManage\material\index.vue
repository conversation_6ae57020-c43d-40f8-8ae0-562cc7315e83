<template>
  <el-card :bordered="false" class="proCard" :style="{ marginBottom: '15px' }">
    <el-form :rules="rules" ref="formRef" label-width="80px">
      <el-form-item label="素材风格">
        <el-radio-group v-model="formParams.style_id" name="style" v-if="isCpUser">
          <el-radio key="all" label=""> 全部 </el-radio>
          <el-radio v-for="item in styles" :key="item.id" :label="item.id">
            {{ item.title }}
          </el-radio>
        </el-radio-group>
        <StyleCascader v-else v-model="formParams.style_id" />
      </el-form-item>
      <el-form-item label="素材分类">
        <el-radio-group v-model="category" name="category">
          <el-radio-button label="all"> 全部 </el-radio-button>
          <el-radio-button v-for="item in parentBundleOption" :key="item.name" :label="item.name">
            {{ item.chinese }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="素材类型" name="bundleType">
        <div style="width: 100%">
          <el-checkbox
            @change="handleCheckAllChange"
            v-model="isCheckAll"
          >
            全部
          </el-checkbox>
        </div>
        <el-checkbox-group v-model="formParams.bundleType">
          <el-checkbox v-for="item in filterOptions" :key="item.name" :label="item.name">
            {{ item.chinese }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="适用范围">
        <el-radio-group v-model="formParams.gender">
          <el-radio label="">全部</el-radio>
          <el-radio label="male">男性</el-radio>
          <el-radio label="female">女性</el-radio>
          <el-radio label="all">通用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </el-card>
  <el-card>
    <el-form :rules="rules" ref="formRef" label-width="80px">
      <el-row>
        <el-col :span="4" v-if="!isCpUser">
          <el-form-item label="制作CP">
            <el-select
              v-model="formParams.user_id"
              placeholder="请选择制作CP"
            >
              <el-option label="全部" value="" />
              <template v-for="item in allCpUsers" :key="item.id">
                <el-option :label="item.name" :value="item.id" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="素材状态">
            <el-select
              v-model="formParams.status"
              placeholder="请选择素材状态"
            >
              <el-option label="全部" value="" />
              <template v-for="item in filteredAssetsSatusOptions" :key="item.value">
                <el-option :label="item.label" :value="item.value" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="创建日期">
            <el-date-picker
              v-model="formParams.dates"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              date-format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              clearable
              :disabled-date="
                (date) => {
                  return date.getTime() > new Date().getTime();
                }
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="" label-width="10">
            <el-input
              placeholder="请输入素材名称"
              clearable
              v-model="formParams.keyword"
              :prefix-icon="Search"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :style="{ textAlign: 'right' }">
          <el-button
            type="primary"
            @click="addTable"
            :icon="PlusOutlined"
            v-if="hasPermission([PERMISSION_KEYS.cpuser.asset_create])"
          >
            新建
          </el-button>
          <el-button
            type="primary"
            @click="handleExport"
            v-if="hasPermission([PERMISSION_KEYS.cpuser.asset_read])"
          >
            导出
          </el-button>
          <el-button type="danger" @click="handleBatchRemove" v-if="isCpUser">批量删除</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div class="material-table">
      <BasicTable
        v-if="optionInited"
        :columns="materialColumns"
        :request="loadDataTable"
        :row-key="(row: ListData) => row.id"
        ref="actionRef"
        :actionColumn="actionColumn"
        :scroll-x="1920"
      />
    </div>
  </el-card>
  <el-dialog v-model="dialogVisible" title="预览" width="900px" destroy-on-close>
    <VFrame :flowSrc="flowSrc" />
  </el-dialog>
</template>

<script lang="ts" setup>
  import {
    applyAssetPublish,
    cancelAssetPublish,
    deleteAsset,
    getAssetList,
    getAssetTypeList,
    getAuditAssetList,
    revisingAssetPublish,
    batchDeleteAsset,
  } from '@/api/material/material';
  import { StyleCascader } from '@/components/StyleCascader';
  import { BasicTable, TableAction } from '@/components/Table';
  import { usePermission } from '@/hooks/web/usePermission';
  import { PERMISSION_KEYS, useUserStore } from '@/store/modules/user';
  import { DocumentCopy, Search } from '@element-plus/icons-vue';
  import { PlusOutlined } from '@vicons/antd';
  import moment from 'moment';
  import { ElIcon, ElImage, ElMessage, ElMessageBox, ElTooltip } from 'element-plus';
  import { type FormRules } from 'naive-ui';
  import { storeToRefs } from 'pinia';
  import { computed, h, onBeforeMount, reactive, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { AssetStatus, AssetStatusColor, ListData, assetsSatusOptions } from './columns';
  import { EAssetStatus } from '../avatar/columns';
  import { useAllCpUsers, useExport } from '@/views/hooks';
  import { useClipboard, useThrottleFn } from '@vueuse/core';

  const optionInited = ref(false);
  const inited = ref(false);
  const userStore = useUserStore();
  const { styles, currentProviderId, isCpUser } = storeToRefs(userStore);
  const { hasPermission } = usePermission();
  const { exportFile } = useExport();
  const allCpUsers = useAllCpUsers(isCpUser.value);
  const filteredAssetsSatusOptions = computed(() => {
    return assetsSatusOptions.filter((item) => {
      if (isCpUser.value) return true;
      return item.value !== 1;
    });
  });
  const isCheckAll = computed(() => formParams.bundleType.length === filterOptions.value.length);
  // 素材类型
  const category = ref('all');
  const parentBundleOption = ref<any[]>([]);
  async function queryMaterialType() {
    const res = await getAssetTypeList();
    parentBundleOption.value = res.data;
  }

  const filterOptions = computed(() => {
    let options: any[] = [];
    if (category.value === 'all') {
      options = parentBundleOption.value.reduce((prev, item) => {
        return [...prev, ...item.children];
      }, []);
    } else {
      options = parentBundleOption.value.find((i) => i.name === category.value).children;
    }
    formParams.bundleType = options.map((i) => i.name);
    return options;
  });

  const handleCheckAllChange = (v) => {
    if (v) {
      formParams.bundleType = filterOptions.value.map((i) => i.name);
    } else {
      formParams.bundleType = [];
    }
  };

  const materialColumns = [
    isCpUser.value
      ? {
          type: 'selection',
          disabled(row) {
            return row.status === EAssetStatus.Published;
          },
        }
      : null,
    {
      title: '资源名称',
      key: 'originalname',
      width: 120,
      render(row) {
        let namePart;
        try {
          namePart = h(
            ElTooltip,
            {
              trigger: 'hover',
              placement: 'top',
            },
            {
              content: () => [h('div', { style: 'text-align: center' }, row.name)],
              default: () => [h('div', row.name)],
            }
          );
        } catch (e) {
          namePart = row.name;
          console.error(e);
        }
        return h('div', [
          h(ElImage, {
            style: { width: '48px', height: '48px' },
            zoomRate: 1.2,
            maxScale: 6,
            minScale: 0.2,
            previewSrcList: [row.url_icon],
            fit: 'cover',
            src: row.url_icon,
            title: '点击放大',
            hideOnClickModal: true,
          }),
          namePart,
        ]);
      },
    },
    {
      title: '素材路径',
      key: 'file_path',
      width: 200,
      render(row) {
        return h('div', { style: 'display: flex' }, [
          h(
            ElTooltip,
            {
              trigger: 'hover',
              placement: 'top',
            },
            {
              content: () => [h('div', { style: 'text-align: center' }, row.file_path)],
              default: () => [
                h(
                  'div',
                  {
                    style:
                      'width: 150px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap',
                  },
                  row.file_path
                ),
              ],
            }
          ),
          h(
            ElIcon,
            {
              onClick: () => {
                handleCopy(row.file_path);
              },
            },
            { default: () => h(DocumentCopy, { class: 'cursor-copy', style: 'margin-top: 9px' }) }
          ),
        ]);
      },
    },
    isCpUser.value
      ? null
      : {
          title: '制作CP',
          key: 'userId',
          width: 150,
          render(row) {
            return row.user?.name;
          },
        },
    {
      title: '所属技术规范',
      key: 'address',
      width: 150,
      render(row) {
        return row.provider?.title;
      },
    },
    {
      title: '风格类型',
      key: 'style',
      width: 140,
      render(row) {
        return row.style?.title;
      },
    },
    {
      title: '素材类型',
      key: 'type',
      width: 100,
      render(row) {
        return filterOptions.value.find((i) => i.name === row.type)?.chinese;
      },
    },
    {
      title: '适用范围',
      key: 'gender',
      width: 120,
      render(row) {
        return row.gender === 'male' ? '男' : row.gender === 'female' ? '女' : '通用';
      },
    },
    {
      title: '同步状态',
      key: 'sync_status',
      width: 100,
      render(row) {
        return row.sync_status === 0 ? '未同步' : '已同步';
      },
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      render(row) {
        return h(
          'span',
          {
            style: { color: AssetStatusColor[row.status] },
          },
          AssetStatus[row.status]
        );
      },
    },
    {
      title: '创建时间',
      key: 'created_at',
      width: 160,
      render(row) {
        return moment(row['created_at']).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '更新时间',
      key: 'updated_at',
      width: 160,
      render(row) {
        return moment(row['updated_at']).format('YYYY-MM-DD HH:mm:ss');
      },
    },
  ].filter((i) => i);

  onBeforeMount(async () => {
    await queryMaterialType();
    optionInited.value = true;
  });

  const rules: FormRules = {
    name: {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入名称',
    },
    address: {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入地址',
    },
    date: {
      type: 'number',
      required: true,
      trigger: ['blur', 'change'],
      message: '请选择日期',
    },
  };

  const router = useRouter();

  const formRef: any = ref(null);
  const actionRef = ref();

  const formParams = reactive<{
    [k in string]: any;
  }>({
    style_id: '',
    keyword: '',
    status: '',
    type: null,
    gender: '',
    dates: [null, null],
    bundleType: [],
    page: 1,
    limit: 10,
    user_id: '',
  });

  watch(
    formParams,
    () => {
      if (inited.value) {
        reloadTable();
      }
    },
    { deep: true }
  );

  const actionColumn = reactive({
    title: '操作',
    key: 'action',
    width: '300px',
    render(record) {
      return h(TableAction as any, {
        style: 'button',
        actions: [
/*         {
          label: '详情',
          onClick: handleDetail.bind(null, record),
        }, */
          {
            label: '审核',
            confirmed: true,
            onClick: handleAudit.bind(null, record),
            ifShow: () => {
              return record.status === 2;
            },
            auth: [PERMISSION_KEYS.auditor.audit_asset],
          },
          {
            label: '撤回',
            confirmed: true,
            // 根据业务控制是否显示 isShow 和 auth 是并且关系
            ifShow: () => {
              return record.status === 2;
            },
            type: 'warning',
            popConfirm: {
              title: '确定要撤回吗？',
              confirm: handleCancelPublish.bind(null, record),
            },
            // 根据权限控制是否显示: 有权限，会显示，支持多个
            auth: [PERMISSION_KEYS.cpuser.asset_unpublish],
          },
          {
            label: '预览',
            onClick: handlePreview.bind(null, record),
            auth: [PERMISSION_KEYS.cpuser.asset_read, PERMISSION_KEYS.auditor.audit_asset],
          },
          {
            label: '删除',
            confirmed: true,
            // 根据业务控制是否显示 isShow 和 auth 是并且关系
            ifShow: () => {
              return record.status !== EAssetStatus.Published;
            },
            popConfirm: {
              title: '确定要删除吗？',
              confirm: handleDelete.bind(null, record),
            },
            type: 'error',
            auth: [PERMISSION_KEYS.cpuser.asset_remove],
          },
          {
            label: '编辑',
            onClick: handleEdit.bind(null, record),
            ifShow: () => {
              return [
                EAssetStatus.Draft,
                EAssetStatus.Rejected,
                EAssetStatus.Unshelve
              ].includes(record.status)
            },
            auth: [PERMISSION_KEYS.cpuser.asset_edit],
          },
          {
            label: '申请发布',
            popConfirm: {
              title: '确定要申请发布吗？',
              confirm: handleApplyPublish.bind(null, record),
            },
            // 根据业务控制是否显示 isShow 和 auth 是并且关系
            ifShow: () => {
              return [
                EAssetStatus.Draft,
                EAssetStatus.Rejected,
                EAssetStatus.Unshelve
              ].includes(record.status)
            },
            auth: [PERMISSION_KEYS.cpuser.asset_publish],
          },
          {
            label: '下架',
            // 根据业务控制是否显示 isShow 和 auth 是并且关系
            ifShow: () => {
              return record.status === EAssetStatus.Published;
            },
            type: 'error',
            popConfirm: {
              title: '确定要下架吗？',
              confirm: handleUnpublish.bind(null, record),
            },
            auth: [PERMISSION_KEYS.auditor.audit_asset],
          },
        ],
      });
    },
  });

  let paramsDraft: any = null;
  const loadDataTable = async (res) => {
    const params = {
      ...formParams,
      ...res,
      provider_id: currentProviderId.value,
      create_start: formParams.dates?.[0],
      create_end: formParams.dates?.[1],
      type: formParams.bundleType.join(','),
    };
    delete params.bundleType;
    paramsDraft = params;
    let data;
    if (hasPermission([PERMISSION_KEYS.auditor.audit_asset])) {
      data = await getAuditAssetList(params);
    } else {
      data = await getAssetList(params);
    }
    inited.value = true;
    if (data.code === 0) {
      return data.data;
    }
    return;
  };

  function reloadTable() {
    actionRef.value.updatePage(1);
  }

  const dialogVisible = ref(false);
  const flowSrc = ref('');
  function handlePreview(record: ListData) {
    // 使用Vue Router的replace方法直接跳转，这样不会添加历史记录
    const modelUrl = record.url || '';
    
    if (!modelUrl) {
      ElMessage.warning('该素材没有关联的模型文件');
      return;
    }
    
    // 在控制台输出详细的URL信息，帮助调试
    console.log('原始模型URL:', modelUrl);
    console.log('完整模型URL:', new URL(modelUrl, window.location.origin).href);
    
    // 添加一个时间戳参数，避免浏览器缓存
    const urlWithTimestamp = modelUrl.includes('?') 
      ? `${modelUrl}&_t=${new Date().getTime()}` 
      : `${modelUrl}?_t=${new Date().getTime()}`;
    
    // 获取实际文件扩展名，如果是bundle，假设它是FBX
    let format = modelUrl ? (modelUrl.split('.').pop() || '未知') : '未知';
    if (format.toLowerCase() === 'bundle') {
      format = 'fbx (bundle)';
    }
    
    router.replace({
      name: 'material-model3d-preview', // 使用素材管理下的3D模型预览路由
      query: {
        modelUrl: urlWithTimestamp,
        modelInfo: JSON.stringify({
          name: record.name || '未命名',
          format: format,
          size: record.size || 0,
          uploadTime: record.created_at || new Date().toISOString()
        }),
        source: 'material' // 添加来源标识，用于区分不同模块
      }
    });
  }

  async function handleAudit(record: ListData) {
    const { id, type, url, gender } = record;
    router.push({
      name: 'material-preview',
      query: {
        bundlePath: url,
        bundleId: id,
        type,
        mode: 'audit',
        gender,
        name: filterOptions.value.find((i) => i.name === type)?.chinese,
      },
    });
  }

  function handleDetail(record: ListData) {
    const { id, type, url, gender } = record;
    router.push({
      name: 'material-preview1',
      query: {
        bundlePath: url,
        bundleId: id,
        type,
        mode: 'detail',
        gender,
        name: filterOptions.value.find((i) => i.name === type)?.chinese,
      }
    })
  }

  async function handleUnpublish(record) {
    const { id } = record;
    await revisingAssetPublish(id);
    ElMessage({
      message: '下架成功',
      type: 'success',
    });
    reloadTable();
  }

  function addTable() {
    router.push({ path: '/material/material-add' });
  }

  // 导出
  const handleExport = () => {
    const ids = actionRef.value?.getSelectionRowKeys();
    if (hasPermission([PERMISSION_KEYS.auditor.audit_asset])) {
      exportFile(
        '/business/export-query/asset/audit/list/export', 
        paramsDraft,
        {ids}
      );
    } else {
      exportFile(
        '/business/export-query/asset/list/export',
        paramsDraft,
        {ids}
      );
    }
  };

  function handleEdit(record: Recordable) {
    router.push({ name: 'material-edit', query: { id: record.id } });
  }

  const source = ref('');
  const { copy, isSupported } = useClipboard({ source });

  const handleCopy = useThrottleFn((text) => {
    source.value = text;
    if (!isSupported) {
      ElMessage.warning('您的浏览器不支持复制!');
      return;
    }
    copy(source.value);
    ElMessage.success('复制成功!');
    source.value = '';
  }, 3000);

  async function handleCancelPublish(record) {
    await cancelAssetPublish(record.id);
    ElMessage({
      message: '撤回成功',
      type: 'success',
    });
    reloadTable();
  }

  async function handleDelete(record: Recordable) {
    await deleteAsset(record.id);
    ElMessage({
      message: '删除成功',
      type: 'success',
    });
    reloadTable();
  }

  async function handleApplyPublish(record) {
    await applyAssetPublish(record.id);
    ElMessage({
      message: '申请已提交',
      type: 'success',
    });
    reloadTable();
  }

  const handleBatchRemove = async () => {
    try {
      const ids = actionRef.value?.getSelectionRowKeys();
      if (ids.length <= 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要批量删除的素材',
        });
        return;
      }
      const action = await ElMessageBox.confirm(`确认要批量删除所选素材吗?`, '批量删除素材');
      if (action == 'confirm') {
        const { code } = await batchDeleteAsset({ ids });
        if (code === 0) {
          ElMessage({
            type: 'success',
            message: '批量删除成功！',
          });
          reloadTable();
        }
      }
    } catch (e) {console.log(e)}
  };
</script>

<style lang="less" scoped>
  .material-table {
    :deep(.n-data-table-base-table-body) {
      max-height: unset !important;
    }
  }
</style>
