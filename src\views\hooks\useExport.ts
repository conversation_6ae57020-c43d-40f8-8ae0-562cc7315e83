import { http } from '@/utils/http/axios';

export default function useExport() {
  const exportFile = async (apiUrl, params, body?) => {
    const link = document.createElement('a');
    const response = await http.request(
      {
        url: apiUrl,
        method: body ? 'POST' : 'GET',
        params,
        data: body,
        responseType: 'blob',
      },
      { isReturnNativeResponse: true }
    );
    const { data, headers } = response;
    const fileName = headers['content-disposition'].replace(/\w+; filename=(.*)/, '$1');
    // const type = headers['content-type'];
    const blob = new Blob([data]);
    const url = window.URL.createObjectURL(blob);
    link.href = url;
    link.download = decodeURIComponent(fileName);
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  return {
    exportFile,
  };
}
