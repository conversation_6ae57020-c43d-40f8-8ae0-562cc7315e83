{"asset": {"generator": "Khronos glTF Blender I/O v3.4.49", "version": "2.0"}, "scene": 0, "scenes": [{"extras": {"Scene Property String": "scene example", "Scene Property Integer": 1, "Scene Property Float": 0.5}, "name": "Scene", "nodes": [0, 1, 2]}], "nodes": [{"extras": {"Node Property Float": 0.5, "Node Property String": "node example"}, "mesh": 0, "name": "Cube 1 Node"}, {"mesh": 1, "name": "Cube 2 Node", "translation": [0, 0, -3.0047976970672607]}, {"mesh": 2, "name": "Cube 3 Node", "translation": [-2.802919626235962, 0, 0]}], "materials": [{"doubleSided": true, "extras": {"Material Property Float": 1.0, "Material Property String": "material example"}, "name": "Material 1", "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Material 2", "pbrMetallicRoughness": {"baseColorFactor": [0, 0.800000011920929, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Material 3", "pbrMetallicRoughness": {"baseColorFactor": [0, 0, 0.800000011920929, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}], "meshes": [{"extras": {"Mesh Property Float": 0.5, "Mesh Property String": "mesh example"}, "name": "Cube 1", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2}, "indices": 3, "material": 0}]}, {"name": "Cube 2", "primitives": [{"attributes": {"POSITION": 4, "TEXCOORD_0": 5, "NORMAL": 6}, "indices": 3, "material": 1}]}, {"name": "Cube 3", "primitives": [{"attributes": {"POSITION": 7, "TEXCOORD_0": 8, "NORMAL": 9}, "indices": 3, "material": 2}]}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 24, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 24, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 24, "type": "VEC3"}, {"bufferView": 3, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 4, "componentType": 5126, "count": 24, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 5, "componentType": 5126, "count": 24, "type": "VEC2"}, {"bufferView": 6, "componentType": 5126, "count": 24, "type": "VEC3"}, {"bufferView": 7, "componentType": 5126, "count": 24, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 8, "componentType": 5126, "count": 24, "type": "VEC2"}, {"bufferView": 9, "componentType": 5126, "count": 24, "type": "VEC3"}], "bufferViews": [{"buffer": 0, "byteLength": 288, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 288, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 480, "target": 34962}, {"buffer": 0, "byteLength": 72, "byteOffset": 768, "target": 34963}, {"buffer": 0, "byteLength": 288, "byteOffset": 840, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 1128, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 1320, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 1608, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 1896, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 2088, "target": 34962}], "buffers": [{"byteLength": 2376, "uri": "data:application/octet-stream;base64,AACAPwAAgD8AAIC/AACAPwAAgD8AAIC/AACAPwAAgD8AAIC/AACAPwAAgL8AAIC/AACAPwAAgL8AAIC/AACAPwAAgL8AAIC/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgL8AAIA/AACAPwAAgL8AAIA/AACAPwAAgL8AAIA/AACAvwAAgD8AAIC/AACAvwAAgD8AAIC/AACAvwAAgD8AAIC/AACAvwAAgL8AAIC/AACAvwAAgL8AAIC/AACAvwAAgL8AAIC/AACAvwAAgD8AAIA/AACAvwAAgD8AAIA/AACAvwAAgD8AAIA/AACAvwAAgL8AAIA/AACAvwAAgL8AAIA/AACAvwAAgL8AAIA/AAAgPwAAAD8AACA/AAAAPwAAID8AAAA/AADAPgAAAD8AAMA+AAAAPwAAwD4AAAA/AAAgPwAAgD4AACA/AACAPgAAID8AAIA+AADAPgAAgD4AAMA+AACAPgAAwD4AAIA+AAAgPwAAQD8AACA/AABAPwAAYD8AAAA/AAAAPgAAAD8AAMA+AABAPwAAwD4AAEA/AAAgPwAAAAAAACA/AACAPwAAYD8AAIA+AAAAPgAAgD4AAMA+AAAAAAAAwD4AAIA/AAAAAAAAAAAAAIC/AAAAAAAAgD8AAACAAACAPwAAAAAAAACAAAAAAAAAgL8AAACAAAAAAAAAAAAAAIC/AACAPwAAAAAAAACAAAAAAAAAAAAAAIA/AAAAAAAAgD8AAACAAACAPwAAAAAAAACAAAAAAAAAgL8AAACAAAAAAAAAAAAAAIA/AACAPwAAAAAAAACAAACAvwAAAAAAAACAAAAAAAAAAAAAAIC/AAAAAAAAgD8AAACAAAAAAAAAgL8AAACAAACAvwAAAAAAAACAAAAAAAAAAAAAAIC/AAAAAAAAAAAAAIA/AACAvwAAAAAAAACAAAAAAAAAgD8AAACAAAAAAAAAgL8AAACAAAAAAAAAAAAAAIA/AACAvwAAAAAAAACAAQAOABQAAQAUAAcACgAGABIACgASABYAFwATAAwAFwAMABAADwADAAkADwAJABUABQACAAgABQAIAAsAEQANAAAAEQAAAAQAAACAPwAAgD8AAIC/AACAPwAAgD8AAIC/AACAPwAAgD8AAIC/AACAPwAAgL8AAIC/AACAPwAAgL8AAIC/AACAPwAAgL8AAIC/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgL8AAIA/AACAPwAAgL8AAIA/AACAPwAAgL8AAIA/AACAvwAAgD8AAIC/AACAvwAAgD8AAIC/AACAvwAAgD8AAIC/AACAvwAAgL8AAIC/AACAvwAAgL8AAIC/AACAvwAAgL8AAIC/AACAvwAAgD8AAIA/AACAvwAAgD8AAIA/AACAvwAAgD8AAIA/AACAvwAAgL8AAIA/AACAvwAAgL8AAIA/AACAvwAAgL8AAIA/AAAgPwAAAD8AACA/AAAAPwAAID8AAAA/AADAPgAAAD8AAMA+AAAAPwAAwD4AAAA/AAAgPwAAgD4AACA/AACAPgAAID8AAIA+AADAPgAAgD4AAMA+AACAPgAAwD4AAIA+AAAgPwAAQD8AACA/AABAPwAAYD8AAAA/AAAAPgAAAD8AAMA+AABAPwAAwD4AAEA/AAAgPwAAAAAAACA/AACAPwAAYD8AAIA+AAAAPgAAgD4AAMA+AAAAAAAAwD4AAIA/AAAAAAAAAAAAAIC/AAAAAAAAgD8AAACAAACAPwAAAAAAAACAAAAAAAAAgL8AAACAAAAAAAAAAAAAAIC/AACAPwAAAAAAAACAAAAAAAAAAAAAAIA/AAAAAAAAgD8AAACAAACAPwAAAAAAAACAAAAAAAAAgL8AAACAAAAAAAAAAAAAAIA/AACAPwAAAAAAAACAAACAvwAAAAAAAACAAAAAAAAAAAAAAIC/AAAAAAAAgD8AAACAAAAAAAAAgL8AAACAAACAvwAAAAAAAACAAAAAAAAAAAAAAIC/AAAAAAAAAAAAAIA/AACAvwAAAAAAAACAAAAAAAAAgD8AAACAAAAAAAAAgL8AAACAAAAAAAAAAAAAAIA/AACAvwAAAAAAAACAAACAPwAAgD8AAIC/AACAPwAAgD8AAIC/AACAPwAAgD8AAIC/AACAPwAAgL8AAIC/AACAPwAAgL8AAIC/AACAPwAAgL8AAIC/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgL8AAIA/AACAPwAAgL8AAIA/AACAPwAAgL8AAIA/AACAvwAAgD8AAIC/AACAvwAAgD8AAIC/AACAvwAAgD8AAIC/AACAvwAAgL8AAIC/AACAvwAAgL8AAIC/AACAvwAAgL8AAIC/AACAvwAAgD8AAIA/AACAvwAAgD8AAIA/AACAvwAAgD8AAIA/AACAvwAAgL8AAIA/AACAvwAAgL8AAIA/AACAvwAAgL8AAIA/AAAgPwAAAD8AACA/AAAAPwAAID8AAAA/AADAPgAAAD8AAMA+AAAAPwAAwD4AAAA/AAAgPwAAgD4AACA/AACAPgAAID8AAIA+AADAPgAAgD4AAMA+AACAPgAAwD4AAIA+AAAgPwAAQD8AACA/AABAPwAAYD8AAAA/AAAAPgAAAD8AAMA+AABAPwAAwD4AAEA/AAAgPwAAAAAAACA/AACAPwAAYD8AAIA+AAAAPgAAgD4AAMA+AAAAAAAAwD4AAIA/AAAAAAAAAAAAAIC/AAAAAAAAgD8AAACAAACAPwAAAAAAAACAAAAAAAAAgL8AAACAAAAAAAAAAAAAAIC/AACAPwAAAAAAAACAAAAAAAAAAAAAAIA/AAAAAAAAgD8AAACAAACAPwAAAAAAAACAAAAAAAAAgL8AAACAAAAAAAAAAAAAAIA/AACAPwAAAAAAAACAAACAvwAAAAAAAACAAAAAAAAAAAAAAIC/AAAAAAAAgD8AAACAAAAAAAAAgL8AAACAAACAvwAAAAAAAACAAAAAAAAAAAAAAIC/AAAAAAAAAAAAAIA/AACAvwAAAAAAAACAAAAAAAAAgD8AAACAAAAAAAAAgL8AAACAAAAAAAAAAAAAAIA/AACAvwAAAAAAAACA"}]}