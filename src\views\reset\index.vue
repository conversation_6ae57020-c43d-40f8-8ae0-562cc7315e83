<template>
  <div class="login-body bg-cover bg-left-top flex flex-col">
    <logo-bar />
    <div class="flex-1 flex items-center">
      <!-- <reset-form class="m-auto" 20250611/> -->
      <reset-form />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import LogoBar from '@/components/LogoBar';
  import ResetForm from './ResetForm.vue'; //20250611 如果是setup 那么就不需要显示导入
</script>

<style lang="less" scoped>
  .login-body {
    min-height: 100vh;
    // background-image: url('@/assets/images/login/login-bg.png');
  }
</style>
