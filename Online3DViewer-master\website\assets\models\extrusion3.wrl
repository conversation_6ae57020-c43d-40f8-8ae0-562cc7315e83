#VRML V2.0 utf8
# X3D-to-VRML-97 XSL translation autogenerated by X3dToVrml97.xslt
# http://www.web3d.org/x3d/content/X3dToVrml97.xslt
# Generated using XSLT processor: SAXON 9.1.0.2 from Saxonica

# [X3D] VRML V3.0 utf8
# PROFILE Immersive
# [X3D] version=3.0
# [X3D] noNamespaceSchemaLocation=http://www.web3d.org/specifications/x3d-3.0.xsd
# [head]

# META "title" "Figure15.15TwistedBar.x3d"
# META "creator" "Figure 15.15, The VRML 2.0 Sourcebook, Copyright [1997] By <PERSON>, <PERSON>, and <PERSON>"
# META "reference" "http://www.wiley.com/legacy/compbooks/vrml2sbk/ch15/15fig15.htm"
# META "translator" "<PERSON>"
# META "created" "20 August 2000"
# META "modified" "14 January 2010"
# META "description" "A bar twisted using Y-axis, cross-section rotation at each spine coordinate."
# META "identifier" "http://www.web3d.org/x3d/content/examples/Vrml2Sourcebook/Chapter15-Extrusion/Figure15.15TwistedBar.x3d"
# META "generator" "X3D-Edit 3.3, https://savage.nps.edu/X3D-Edit"
# META "license" "../../license.html"

# [Scene] ========== ========== ==========

NavigationInfo { type [ "EXAMINE" "ANY" ] } ###  Default X3D NavigationInfo

# Computational cost of Extrusion compared to IndexedFaceSet: quicker to download, takes time to calculate polygonal faces and normals during initial loading, equally fast at run time.
# Authoring capabilities of Extrusion compared to IndexedFaceSet: can be more complicated to express, but also can provide great detail with much less effort.
# First position and rotate viewpoint into positive-X-Y-Z octant using a Transform
Transform {
  rotation 0 1 0 0.758
  translation 10 5 10
  children [
      Viewpoint {
        description "Twisted bar using extrusion orientations"
        orientation 1 0 0 -0.3
        position 0 0 0
      }
  ]
}
Shape {
  appearance Appearance {
    material Material {
      diffuseColor 1 0.5 0
    }
  }
  geometry Extrusion {
    creaseAngle 0.785
    crossSection [ -1 1 1 1 1 -1 -1 -1 -1 1 ]
    orientation [ 0 1 0 0 0 1 0 0.175 0 1 0 0.349 0 1 0 0.524 0 1 0 0.698 0 1 0 0.873 0 1 0 1.047 0 1 0 1.222 0 1 0 1.396 ]
    spine [ 0 0.0 0 0 0.5 0 0 1.0 0 0 1.5 0 0 2.0 0 0 2.5 0 0 3.0 0 0 3.5 0 0 4.0 0 ]
  }
}
