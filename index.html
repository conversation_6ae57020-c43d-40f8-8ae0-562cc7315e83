<!DOCTYPE html>
<html lang="zh-cmn-Hans" id="htmlRoot" data-theme="light">
<head>
  <meta charset="UTF-8">
  <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible"/>
  <meta content="webkit" name="renderer"/>
  <meta http-equiv="Expires" content="0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Cache-control" content="no-store,no-cache,must-revalidate">
  <meta http-equiv="Cache" content="no-cache">
  <meta
    content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0"
    name="viewport"
  />
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="manifest" href="/site.webmanifest">
  <link href="/favicon.ico" rel="icon"/>
  <title><%= title %></title>
</head>
<body>
<div id="appProvider" style="display: none"></div>
<div id="app">
  <style>
    .first-loading-wrap {
      display: flex;
      width: 100%;
      height: 100vh;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .first-loading-wrap > h1 {
      font-size: 128px;
    }

    .first-loading-wrap .loading-wrap {
      padding: 98px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .dot {
      animation: antRotate 1.2s infinite linear;
      transform: rotate(45deg);
      position: relative;
      display: inline-block;
      font-size: 32px;
      width: 32px;
      height: 32px;
      box-sizing: border-box;
    }

    .dot i {
      width: 14px;
      height: 14px;
      position: absolute;
      display: block;
      background-color: #1890ff;
      border-radius: 100%;
      transform: scale(.75);
      transform-origin: 50% 50%;
      opacity: .3;
      animation: antSpinMove 1s infinite linear alternate
    }

    .dot i:nth-child(1) {
      top: 0;
      left: 0;
    }

    .dot i:nth-child(2) {
      top: 0;
      right: 0;
      -webkit-animation-delay: .4s;
      animation-delay: .4s;
    }

    .dot i:nth-child(3) {
      right: 0;
      bottom: 0;
      -webkit-animation-delay: .8s;
      animation-delay: .8s;
    }

    .dot i:nth-child(4) {
      bottom: 0;
      left: 0;
      -webkit-animation-delay: 1.2s;
      animation-delay: 1.2s;
    }

    @keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg);
      }
    }

    @-webkit-keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg);
      }
    }

    @keyframes antSpinMove {
      to {
        opacity: 1;
      }
    }

    @-webkit-keyframes antSpinMove {
      to {
        opacity: 1;
      }
    }</style>
  <div class="first-loading-wrap">
    <div class="loading-wrap">
      <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
    </div>
  </div>
</div>
<script src="/src/main.ts" type="module"></script>
</body>
</html>
