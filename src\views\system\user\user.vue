<template>
  <div>
    <n-card :bordered="false" class="mt-4 proCard">
      <BasicTable
        :columns="columns"
        :request="loadDataTable"
        :row-key="(row) => row.id"
        ref="actionRef"
        :actionColumn="actionColumn"
      >
        <template #tableTitle>
          <el-form :inline="true">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="params.createAt"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
                type="daterange"
                :disabled-date="
                  (date) => {
                    return date.getTime() > new Date().getTime();
                  }
                "
              />
            </el-form-item>
            <el-form-item label="">
              <el-input
                clearable
                v-model="params.keyword"
                type="daterange"
                placeholder="请输入账户名称或账号"
                :prefix-icon="Search"
              />
            </el-form-item>
          </el-form>
        </template>
        <template #toolbar>
          <n-button type="primary" @click="addTable()">
            <template #icon>
              <n-icon>
                <PlusOutlined />
              </n-icon>
            </template>
            添加账号
          </n-button>
        </template>
        <template #action>
          <TableAction />
        </template>
      </BasicTable>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, unref, h, onMounted, watch } from 'vue';
  import { BasicTable, TableAction } from '@/components/Table';
  import { getUserList, editUser, deleteUser } from '@/api/system/user';
  import { PlusOutlined } from '@vicons/antd';
  import { Search } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';
  import moment from 'moment';
  import { PERMISSION_KEYS } from '@/store/modules/user';
  import { ElMessageBox, ElSwitch, ElMessage } from 'element-plus';
  import { useEncrypt } from '@/views/hooks';

  const encrypt = useEncrypt();
  const router = useRouter();
  const actionRef = ref();

  const params = reactive({
    keyword: '',
    createAt: [null, null],
  });

  const columns = [
    {
      title: '账户名称',
      key: 'username',
    },
    {
      title: '登录账号',
      key: 'phone',
    },
    {
      title: '姓名',
      key: 'name',
    },
    {
      title: '角色',
      key: 'role',
      render(row) {
        return row.role.name;
      },
    },
    {
      title: '描述',
      key: 'introduction',
    },
    {
      title: '状态',
      key: 'status',
      render(row) {
        return h(ElSwitch, {
          modelValue: row.status === 1,
          beforeChange() {
            if (row.status === 2) {
              changeStatus(row);
              return true;
            } else {
              return new Promise(async (_) => {
                const action = await ElMessageBox.confirm(
                  '禁用生效后，该账号将不能登录系统，是否要禁用此账号？',
                  '操作确认'
                );
                if (action == 'confirm') {
                  changeStatus(row);
                } else {
                  return false;
                }
              });
            }
          },
        });
      },
    },
    {
      title: '创建时间',
      key: 'created_at',
      width: 180,
      render(row) {
        return moment(row.created_at).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '更新时间',
      key: 'updated_at',
      width: 180,
      render(row) {
        return moment(row.updated_at).format('YYYY-MM-DD HH:mm:ss');
      },
    },
  ];
  const actionColumn = reactive({
    width: 150,
    title: '操作',
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction, {
        style: 'button',
        actions: [
          {
            label: '编辑',
            onClick: handleEdit.bind(null, record),
            ifShow: () => {
              return true;
            },
            auth: [PERMISSION_KEYS.super.user_edit],
          },
          {
            label: '删除',
            type: 'error',
            popConfirm: {
              title: '确定要删除吗？',
              confirm: handleDelete.bind(null, record),
            },
            // 根据业务控制是否显示 isShow 和 auth 是并且关系
            ifShow: () => {
              return true;
            },
            // 根据权限控制是否显示: 有权限，会显示，支持多个
            auth: [PERMISSION_KEYS.super.user_remove],
          },
        ],
      });
    },
  });

  function addTable() {
    router.push({ path: '/user/add' });
  }

  watch(
    params,
    () => {
      reloadTable();
    },
    { deep: true }
  );

  const loadDataTable = async (res: any) => {
    let _params = {
      ...unref(params),
      create_start: params.createAt?.[0],
      create_end: params.createAt?.[1],
      ...res,
    };
    const data = await getUserList(_params);
    if (data.code === 0) {
      return data.data;
    }
    return;
  };

  function reloadTable() {
    actionRef.value.updatePage(1);
  }

  function handleEdit(record) {
    router.push({ name: 'user-edit', query: { id: record.id } });
  }

  async function handleDelete(record) {
    await deleteUser(record.id);
    ElMessage({
      message: '删除成功!',
      type: 'success',
    });
    reloadTable();
  }

  async function changeStatus(record) {
    try {
      await editUser(
        record.id,
        encrypt({
          status: record.status === 1 ? 2 : 1,
        })
      );
      ElMessage({
        message: record.status === 1 ? '禁用成功' : '启用成功',
        type: 'success',
      });
      reloadTable();
    } catch (e) {
      console.error(e);
    }
  }

  onMounted(async () => {});
</script>

<style lang="less" scoped></style>
