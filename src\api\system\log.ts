import { http } from '@/utils/http/axios';

// 获取 token, 其他请求均依赖于 token
export async function getKey() {
  return http.request({
    url: `/business/user/token/key`,
    method: 'GET',
  });
}

/**
 * @description: 安全日志
 */
export function getSecurityLog(params) {
  return http.request({
    url: '/business/system-log/list',
    method: 'GET',
    params,
  });
}

/**
 * @description: 用户行为
 */
export function getUserBehaviorList(params) {
  return http.request({
    url: '/business/system-log/extend/list-by-user',
    method: 'GET',
    params,
  });
}

/**
 * @description: 操作日志
 */
export function getOperationLog(params) {
  return http.request({
    url: '/business/operation-log/list',
    method: 'GET',
    params,
  });
}

/**
 * @description: 操作行为统计
 */
export function getOperationBehaviorList(params) {
  return http.request({
    url: '/business/operation-log/extend/list-by-user',
    method: 'GET',
    params,
  });
}

/**
 * @description: 访问页面前5
 */
export function getPageRanking(params = {}) {
  return http.request({
    url: '/business/pv-log/access-page-ranking',
    method: 'GET',
    params,
  });
}

/**
 * @description: 访问页面前5
 */
export function getModelNames(params = {}) {
  return http.request({
    url: '/business/operation-log/extend/module-names',
    method: 'GET',
    params,
  });
}

/**
 * @description: 访问页面列表
 */
export function getPages(params = {}) {
  return http.request({
    url: '/business/system-log/extend/pages',
    method: 'GET',
    params,
  });
}

/**
 * @description: 权限映射
 */
export function getPermission(params = { flatten: 1 }) {
  return http.request({
    url: '/business/permission',
    method: 'GET',
    params,
  });
}
