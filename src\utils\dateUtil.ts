import { format } from 'date-fns';

const DATE_TIME_FORMAT = 'yyyy-MM-dd HH:mm:ss';
const DATE_FORMAT = 'yyyy-MM-dd ';

export function formatToDateTime(date: Date | number, formatStr = DATE_TIME_FORMAT): string {
  if (!date) return '';
  const d = new Date(date);
  return format(d, formatStr);
}

export function formatToDate(date: Date | number, formatStr = DATE_FORMAT): string {
  if (!date) return '';
  const d = new Date(date);
  return format(d, formatStr);
}

export const disabledDate = (time: Date) => {
  return time.getTime() > Date.now() - 8.64e6;
};

export const disabledTodayDate = (time: Date) => {
  return time.getTime() > Date.now() - 8.64e7;
};
