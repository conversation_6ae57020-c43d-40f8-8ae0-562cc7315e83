<template>
  <div>
    <n-card :bordered="false" class="mt-4 proCard">
      <BasicTable
        :columns="columns"
        :request="loadDataTable"
        :row-key="(row) => row.id"
        ref="actionRef"
        :actionColumn="actionColumn"
      >
        <template #tableTitle>
          <el-form-item label="">
            <el-radio-group v-model="params.category">
              <el-radio :label="undefined">全部</el-radio>
              <el-radio :label="0">CP用户</el-radio>
              <el-radio :label="1">管理员</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="" style="margin-left: 50px">
            <el-input
              style="width: 300px"
              clearable
              v-model="params.name"
              type="daterange"
              placeholder="请输入权限名称"
              :prefix-icon="Search"
            />
          </el-form-item>
        </template>
        <template #toolbar>
          <n-button type="primary" @click="addTable()">
            <template #icon>
              <n-icon>
                <PlusOutlined />
              </n-icon>
            </template>
            添加权限
          </n-button>
        </template>
        <template #action>
          <TableAction />
        </template>
      </BasicTable>
    </n-card>

    <n-modal
      v-model:show="showModal"
      :show-icon="false"
      preset="dialog"
      :title="editPermissionTitle"
    >
      <div class="py-3 menu-list">
        <n-tree
          block-line
          cascade
          checkable
          :virtual-scroll="true"
          :data="treeData"
          :expandedKeys="expandedKeys"
          :checked-keys="checkedKeys"
          style="max-height: 950px; overflow: hidden"
          @update:checked-keys="checkedTree"
          @update:expanded-keys="onExpandedKeys"
        />
      </div>
      <template #action>
        <n-space>
          <n-button type="info" ghost icon-placement="left" @click="packHandle">
            全部{{ expandedKeys.length ? '收起' : '展开' }}
          </n-button>
          <n-button type="primary" :loading="formBtnLoading" @click="confirmForm">提交</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, unref, h } from 'vue';
  import { BasicTable, TableAction } from '../../components/Table';
  import {
    getPermissionList,
    changePermissionStatus,
    deletePermission,
  } from '../../api/system/permission';
  import { PlusOutlined } from '@vicons/antd';
  import { Search } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';
  import { watch } from 'vue';
  import { ElMessageBox, ElSwitch, ElMessage } from 'element-plus';

  const router = useRouter();
  const actionRef = ref();

  const showModal = ref(false);
  const formBtnLoading = ref(false);
  const editPermissionTitle = ref('');
  const treeData = ref([]);
  const expandedKeys = ref([]);
  const checkedKeys = ref(['console', 'step-form']);

  const params = reactive({
    name: '',
    keyword: '',
    category: undefined,
    createAt: [null, null],
  });

  const columns = [
    {
      title: '权限名称',
      key: 'name',
    },
    {
      title: 'key',
      key: 'key',
    },
    {
      title: '所属类别',
      key: 'category',
      render(row) {
        return row.category === 0 ? 'CP用户' : '管理员';
      },
    },
    {
      title: '状态',
      key: 'status',
      render(row) {
        return h(ElSwitch, {
          disabled: row.key === 'super',
          modelValue: row.status === 1,
          beforeChange() {
            if (row.status === 2) {
              changeStatus(row);
              return true;
            } else {
              return new Promise(async (_) => {
                const action = await ElMessageBox.confirm('是否要禁用此权限？', '操作确认');
                console.log('action', action);
                if (action == 'confirm') {
                  changeStatus(row);
                } else {
                  return false;
                }
              });
            }
          },
        });
      },
    },
  ];
  const actionColumn = reactive({
    width: 250,
    title: '操作',
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction, {
        style: 'button',
        actions: [
          {
            label: '编辑',
            disabled: record.key === 'super',
            onClick: handleEdit.bind(null, record),
            ifShow: () => {
              return true;
            },
          },
          {
            label: '删除',
            disabled: record.key === 'super' || record.status === 1,
            type: 'error',
            popConfirm: {
              title: '确定要删除吗？',
              confirm: handleDelete.bind(null, record),
            },
          },
        ],
      });
    },
  });

  function addTable() {
    router.push({ path: '/permission/add' });
  }

  watch(
    params,
    () => {
      reloadTable();
    },
    { deep: true }
  );

  const loadDataTable = async (res: any) => {
    let _params = {
      ...unref(params),
      ...res,
    };
    const data = await getPermissionList(_params);
    if (data.code === 0) {
      return data.data;
    }
    return;
  };

  function reloadTable() {
    actionRef.value.updatePage(1);
  }

  function confirmForm(e: any) {
    e.preventDefault();
    formBtnLoading.value = true;
    setTimeout(() => {
      showModal.value = false;
      ElMessage({
        message: '提交成功!',
        type: 'success',
      });
      reloadTable();
      formBtnLoading.value = false;
    }, 200);
  }

  function handleEdit(record) {
    router.push({ name: 'permission-edit', query: { id: record.id } });
  }

  async function handleDelete(record) {
    await deletePermission(record.id);
    ElMessage({
      message: '删除成功!',
      type: 'success',
    });
    reloadTable();
  }

  function checkedTree(keys) {
    checkedKeys.value = [checkedKeys.value, ...keys];
  }

  function onExpandedKeys(keys) {
    expandedKeys.value = keys;
  }

  function packHandle() {
    if (expandedKeys.value.length) {
      expandedKeys.value = [];
    } else {
      expandedKeys.value = treeData.value.map((item: any) => item.key) as [];
    }
  }

  async function changeStatus(record) {
    try {
      await changePermissionStatus(record.id, {
        status: record.status === 1 ? 2 : 1,
      });
      ElMessage({
        message: record.status === 1 ? '禁用成功' : '启用成功',
        type: 'success',
      });
      reloadTable();
    } catch (e) {
      console.error(e);
    }
  }
</script>
