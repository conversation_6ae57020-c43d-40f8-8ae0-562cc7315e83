import { http } from '@/utils/http/axios';

/**
 * @description: 获取运营配置Tab选项列表
 */
export function getConfigTapOptionList() {
  return http.request({
    url: '/business/operation-config/config-tab',
    method: 'GET',
  });
}

export function getAssetsList(params) {
  return http.request({
    url: '/business/operation-config/asset-list',
    method: 'GET',
    params,
  });
}

// 运营配置同步
export function syncOperationConfig(params) {
  return http.request({
    url: '/business/operation-config/sync',
    method: 'POST',
    params,
  });
}

// 获取同步状态
export function getSyncStatus(params) {
  return http.request({
    url: '/business/operation-config/sync-status',
    method: 'GET',
    params,
  });
}

// 获取同步历史记录
export function getSyncActionList(params) {
  return http.request({
    url: '/business/operation-config/sync-action-list',
    method: 'GET',
    params,
  });
}

// 获取模板同步历史记录
export function getTemplateSyncActionList(params) {
  return http.request({
    url: '/business/template/sync/history',
    method: 'GET',
    params,
  });
}
