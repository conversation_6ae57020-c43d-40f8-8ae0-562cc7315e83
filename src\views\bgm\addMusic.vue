<template>
  <el-card class="box-card content">
    <h1 class="text-center">基础信息</h1>
    <div class="flex justify-center mt-10">
      <el-form
        class="w-2/5"
        ref="musicFormRef"
        :model="musicForm"
        :rules="rules"
        label-width="120px"
        status-icon
      >
        <el-form-item label="音乐名称" prop="name">
          <el-input @input="(value) => handleInput('name', value)" v-model="musicForm.name" placeholder="请输入音乐名称" maxlength="100"/>
        </el-form-item>
        <el-form-item label="音乐icon" prop="icon">
          <CustomUpload
            class="bgm-upload"
            v-model="musicForm.icon"
            scope="bgm_icon"
            @change="() => handleUplodStatusChange('icon')"
            @upload="handleUplodStatus"
            :show-file-list="false"
            list-type="picture-card"
            accept=".png,.jpg,.jpeg"
            :maxSize="10"
          >
            <template v-if="musicForm?.icon?.id">
              <div class="card-bgm-uploaded">
                <div class="w-16 h-16 flex justify-center items-center rounded bg-card-bg-color">
                  <img alt="图片" :width="36" :height="36" :src="musicForm?.icon?.url" />
                </div>
                <div class="right-section ml-4 flex flex-col justify-between">
                  <div class="text-sm file-name" v-text="musicForm?.icon?.name"></div>
                  <el-button class="bg-transparent" type="primary" :text="true">重新上传</el-button>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="bgm-card">
                <img alt="图片" :src="uploadIcon" />
                <div class="el-upload__text">点击或拖拽文件上传 </div>
              </div>
            </template>
            <template #tip>
              <div class="el-upload__tip">支持jpg、jpeg、png格式;单个文件最大不超过10M</div>
            </template>
          </CustomUpload>
        </el-form-item>
        <el-form-item label="音频文件" prop="bgm">
          <CustomUpload
            class="bgm-upload"
            v-model="musicForm.bgm"
            scope="bgm"
            @upload="handleUplodStatus"
            :show-file-list="false"
            list-type="picture-card"
            accept=".mp3,.m4a"
            :maxSize="50"
          >
            <template v-if="musicForm?.bgm?.name">
              <div class="card-bgm-uploaded">
                <div class="w-16 h-16 flex justify-center items-center rounded bg-card-bg-color">
                  <img alt="图片" :width="36" :height="36" :src="music2Icon" />
                </div>
                <div class="right-section ml-4 flex flex-col justify-between">
                  <div class="text-sm file-name" v-text="musicForm?.bgm?.name"></div>
                  <el-button class="bg-transparent" type="primary" :text="true">重新上传</el-button>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="bgm-card">
                <img alt="图片" :src="music1Icon" />
                <div class="el-upload__text">点击或拖拽文件上传 </div>
              </div>
            </template>
            <template #tip>
              <div class="el-upload__tip">支持Mp3、M4a格式；单个文件最大不超过50M</div>
            </template>
          </CustomUpload>
        </el-form-item>
        <el-form-item label="音频备注" prop="remark">
          <el-input v-model="musicForm.remark" rows="6" type="textarea" maxlength="100" placeholder="请输入音频备注" @input="(value) => handleInput('remark', value)"/>
        </el-form-item>
      </el-form>
    </div>
  </el-card>
  <div class="content-bottom">
    <el-button @click="goBack">返回</el-button>
    <el-button type="primary" :disabled="submitStatus" @click="handleSubmit(musicFormRef)"
      >确认提交</el-button
    >
  </div>
</template>

<script lang="ts" setup>
  import { addAssetBgm, getAssetBgm, updateAssetBgm } from '@/api/bgm';
  import { useUserStore } from '@/store/modules/user';
  import { FormInstance, FormRules } from 'element-plus';
  import { storeToRefs } from 'pinia';
  import { onMounted, computed, reactive, ref, toRaw } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import CustomUpload from '@/views/operator/components/CustomUpload.vue';
  import music1Icon from '@/assets/icons/music1.svg';
  import music2Icon from '@/assets/icons/music2.svg';
  import uploadIcon from '@/assets/icons/upload.svg';
import { useFilterInputHander } from '../hooks';

  const userStore = useUserStore();
  const { currentProviderId } = storeToRefs(userStore);
  const route = useRoute();
  const router = useRouter();
  const uploadStatus = ref(false); // 文件是否处于上传态

  const musicFormRef = ref<FormInstance>();
  const musicForm = reactive<any>({
    name: '',
    provider_id: '',
    bgm: {},
    icon: {},
    remark: '',
  });
  const handleInput = useFilterInputHander(musicForm);

  const submitStatus = computed(() => {
    return !musicForm.bgm?.id || uploadStatus.value;
  });

  const validateInnerId = (rule, value, callback) => {
    if (rule.required && !value.id) {
      return callback(new Error(rule.message));
    }
    callback();
  };

  const rules = reactive<FormRules>({
    name: [
      {
        required: true,
        message: '请输入音乐名称',
        trigger: ['blur', 'change'],
      },
    ],
    icon: [
      {
        required: true,
        message: '请上传音乐Icon',
        validator: validateInnerId,
        trigger: ['blur', 'change'],
      },
    ],
    bgm: [
      {
        required: true,
        message: '请上传音频文件',
        validator: validateInnerId,
        trigger: ['blur', 'change'],
      },
    ],
    remark: [
      {
        max: 100,
        message: '音频备注不能超过100个字符',
        trigger: 'blur',
      },
    ],
  });

  const handleUplodStatusChange = (filed) => {
    musicFormRef?.value?.validateField?.([filed]);
  };

  const handleUplodStatus = (flag) => {
    uploadStatus.value = flag;
  };

  const fetchBgmMusic = async (id) => {
    const { code, data } = await getAssetBgm(id);
    if (code === 0) {
      const { name, url_bgm, url_bgm_icon, file, file_id, provider_id, icon, icon_id, remark } =
        data;
      const originalname = file?.originalname;
      const originalIconname = icon?.originalname;
      musicForm.name = name;
      musicForm.bgm = { url: url_bgm, id: file_id, name: originalname };
      musicForm.icon = { url: url_bgm_icon, id: icon_id, name: originalIconname };
      musicForm.provider_id = provider_id;
      musicForm.remark = remark;
    }
  };

  onMounted(async () => {
    const id = route.query?.id;
    if (id) {
      await fetchBgmMusic(id);
    }
  });

  const goBack = () => {
    router.push({
      path: '/bgm/list',
    });
  };

  const generateParams = () => {
    const { name, bgm, remark, icon } = toRaw(musicForm);
    const provider_id = toRaw(currentProviderId.value);
    const params = { name, provider_id, file_id: bgm?.id, icon_id: icon?.id, remark };
    return params;
  };
  //新增
  const handleAddMusic = async () => {
    const params = generateParams();
    const { code } = await addAssetBgm(params);
    if (code === 0) {
      router.replace('/bgm/list');
    }
  };
  //编辑
  const handleEditMusic = async () => {
    const id = route.query.id;
    const params = generateParams();
    const { code } = await updateAssetBgm(id, params);
    if (code === 0) {
      router.replace('/bgm/list');
    }
  };
  const handleSubmit = async (formEl: FormInstance | undefined) => {
    try {
      if (!formEl) return;
      await formEl.validate();
      if (route.query.id) {
        //编辑
        handleEditMusic();
      } else {
        //新增
        handleAddMusic();
      }
    } catch (e) {console.log(e)}
  };
</script>
