import * as THREE from 'three';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';
import { ColladaLoader } from 'three/examples/jsm/loaders/ColladaLoader';
import { PLYLoader } from 'three/examples/jsm/loaders/PLYLoader';

// 文件扩展名到加载器的映射
const loaderMap = {
    // 3D模型格式
    'stl': STLLoader,
    'obj': OBJLoader,
    'gltf': GLTFLoader,
    'glb': GLTFLoader,
    'fbx': FBXLoader,
    'dae': ColladaLoader,
    'ply': PLYLoader,
    // bundle格式尝试使用FBXLoader加载
    'bundle': FBXLoader
};

// 文件格式和MIME类型的映射
export const fileFormats = {
    'stl': ['model/stl', 'application/octet-stream'],
    'obj': ['model/obj', 'text/plain'],
    'gltf': ['model/gltf+json'],
    'glb': ['model/gltf-binary', 'application/octet-stream'],
    'fbx': ['application/octet-stream'],
    'dae': ['model/vnd.collada+xml', 'text/xml'],
    'ply': ['application/octet-stream'],
    'bundle': ['application/octet-stream'],
};

/**
 * 从URL获取文件扩展名
 * @param {string} url - 文件URL
 * @returns {string} 文件扩展名（小写）
 */
export function getExtension(url) {
    // 先移除URL中的查询参数
    const baseUrl = url.split('?')[0];
    const parts = baseUrl.split('.');
    if (parts.length < 2) return '';
    return parts.pop().toLowerCase();
}

/**
 * 创建一个简单的立方体模型作为默认显示
 * @returns {THREE.Mesh} 创建的默认模型
 */
function createDefaultModel(extension) {
    // 创建一个立方体几何体
    const geometry = new THREE.BoxGeometry(1, 1, 1);

    // 创建材质
    const material = new THREE.MeshStandardMaterial({
        color: 0x3366ff,
        metalness: 0.3,
        roughness: 0.5,
    });

    // 创建网格
    const cube = new THREE.Mesh(geometry, material);

    // 添加一个显示文本的平面
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 256;
    const context = canvas.getContext('2d');
    context.fillStyle = '#ffffff';
    context.fillRect(0, 0, 256, 256);
    context.font = 'bold 20px Arial';
    context.fillStyle = '#000000';
    context.textAlign = 'center';
    context.fillText(`不支持 .${extension} 格式`, 128, 128);

    const texture = new THREE.CanvasTexture(canvas);
    const textMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        side: THREE.DoubleSide
    });

    const plane = new THREE.Mesh(
        new THREE.PlaneGeometry(2, 0.5),
        textMaterial
    );
    plane.position.set(0, 1.5, 0);

    // 创建组合对象
    const group = new THREE.Group();
    group.add(cube);
    group.add(plane);

    return group;
}

/**
 * 加载模型并返回THREE.js对象
 * @param {string} url - 模型URL
 * @param {function} onProgress - 进度回调函数
 * @returns {Promise<THREE.Object3D>} 加载的模型对象
 */
export function loadModelFromUrl(url, onProgress) {
    return new Promise((resolve, reject) => {
        const extension = getExtension(url);

        // 检查是否支持该扩展名
        if (!(extension in loaderMap)) {
            console.warn(`未知的文件格式: ${extension}，将使用默认模型展示`);
            resolve(createDefaultModel(extension));
            return;
        }

        // 如果该格式使用默认处理
        if (loaderMap[extension] === null) {
            console.log(`使用默认显示模式处理 .${extension} 格式`);
            resolve(createDefaultModel(extension));
            return;
        }

        const LoaderClass = loaderMap[extension];
        const loader = new LoaderClass();

        // 添加进度回调
        if (onProgress && typeof onProgress === 'function') {
            loader.setRequestHeader = function (request) {
                request.addEventListener('progress', (event) => {
                    if (event.lengthComputable) {
                        const progress = event.loaded / event.total;
                        onProgress(progress);
                    }
                });
            };
        }

        // 加载模型
        loader.load(
            url,
            (object) => {
                // 处理不同加载器返回的不同对象类型
                let model;

                if (extension === 'stl' || extension === 'ply') {
                    // STL和PLY加载器返回几何体
                    const material = new THREE.MeshStandardMaterial({
                        color: 0xaaaaaa,
                        metalness: 0.25,
                        roughness: 0.6,
                        side: THREE.DoubleSide
                    });
                    model = new THREE.Mesh(object, material);
                } else if (extension === 'gltf' || extension === 'glb') {
                    // GLTF加载器返回一个包含场景的对象
                    model = object.scene;
                } else {
                    // 其他加载器直接返回对象
                    model = object;
                }

                // 居中模型
                centerModel(model);

                resolve(model);
            },
            onProgress,
            (error) => {
                console.error(`无法加载 ${extension} 格式:`, error);
                console.log(`使用默认模型替代...`);
                resolve(createDefaultModel(extension));
            }
        );
    });
}

/**
 * 从ArrayBuffer加载模型
 * @param {ArrayBuffer} buffer - 模型数据
 * @param {string} format - 文件格式
 * @returns {Promise<THREE.Object3D>} 加载的模型对象
 */
export function loadModelFromBuffer(buffer, format) {
    return new Promise((resolve, reject) => {
        const extension = format.toLowerCase();

        // 检查是否支持该扩展名
        if (!(extension in loaderMap)) {
            console.warn(`未知的文件格式: ${extension}，将使用默认模型展示`);
            resolve(createDefaultModel(extension));
            return;
        }

        // 如果该格式使用默认处理
        if (loaderMap[extension] === null) {
            console.log(`使用默认显示模式处理 .${extension} 格式`);
            resolve(createDefaultModel(extension));
            return;
        }

        const LoaderClass = loaderMap[extension];
        const loader = new LoaderClass();

        try {
            // 不同的加载器有不同的解析方法
            let object;
            if (extension === 'stl' || extension === 'ply') {
                object = loader.parse(buffer);
                const material = new THREE.MeshStandardMaterial({
                    color: 0xaaaaaa,
                    metalness: 0.25,
                    roughness: 0.6,
                    side: THREE.DoubleSide
                });
                object = new THREE.Mesh(object, material);
            } else if (extension === 'gltf' || extension === 'glb') {
                // GLTF需要特殊处理
                loader.parse(buffer, '', (gltf) => {
                    object = gltf.scene;
                    centerModel(object);
                    resolve(object);
                }, (error) => {
                    console.error(`无法解析 ${extension} 格式:`, error);
                    resolve(createDefaultModel(extension));
                });
                return;
            } else {
                // 其他格式可能需要不同的处理方法
                object = loader.parse(buffer);
            }

            centerModel(object);
            resolve(object);

        } catch (error) {
            console.error(`无法解析 ${extension} 格式:`, error);
            resolve(createDefaultModel(extension));
        }
    });
}

/**
 * 将模型居中
 * @param {THREE.Object3D} model - 要居中的模型
 */
function centerModel(model) {
    // 计算模型的边界框
    const box = new THREE.Box3().setFromObject(model);
    const center = box.getCenter(new THREE.Vector3());

    // 将模型移动到中心点
    model.position.sub(center);

    // 对于大小异常的模型，可以进行缩放
    const size = box.getSize(new THREE.Vector3());
    const maxDimension = Math.max(size.x, size.y, size.z);

    if (maxDimension > 10) {
        const scale = 10 / maxDimension;
        model.scale.set(scale, scale, scale);
    }
} 