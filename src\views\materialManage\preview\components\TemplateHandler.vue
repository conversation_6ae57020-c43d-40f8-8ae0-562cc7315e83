<template>
  <div class="template-handler-wrapper" v-loading="previewLoading" element-loading-text="预览中，请不要离开当前页面...">
    <el-tabs tab-position="right" class="template-handler-tabs" @tab-change="pause()">
      <el-tab-pane label="形象">
        <div style="padding: 16px" class="template-handler-tab-avatar">
          <p class="title">选择形象</p>
          <div>
            <img :class="props.gender === 'male' ? 'selected' : ''" src="./default_male.png" alt=""
              @click="() => props.resetAvatar('male')">
            <img :class="props.gender === 'female' ? 'selected' : ''" src="./default_female.png" alt=""
              @click="() => props.resetAvatar('female')">
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="动作">
        <AnimationList ref="animationListRef" :gender="props.gender" :avatar="props.avatar"
          v-model="tempData.animation" />
      </el-tab-pane>
      <el-tab-pane label="素材">
        <Material :gender="props.gender" :avatar="props.avatar" :background="tempData.background"
          :foreground="tempData.foreground" :music="tempData.music" :musicId="tempData.musicId"
          @updateBackground="(v) => tempData.background = v" @updateForeground="(v) => tempData.foreground = v"
          @updateMusic="(v) => tempData.music = v" @updateMusicId="(v) => tempData.musicId = v" />
      </el-tab-pane>
      <el-tab-pane label="描述">
        <TTSModule ref="ttsModuleRef" :avatar="props.avatar" :tempData="tempData" :name="tempData.name"
          :text="tempData.text" @updateName="v => tempData.name = v" @updateText="v => tempData.text = v" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts">
export const HOST = window.location.origin
</script>
<script lang="ts" setup>
import { ref } from 'vue';
import {
  AnimationList,
  Material,
  TTSModule
} from './index'
import { addTemplate, editTemplate, uploadTts } from '@/api/material/template';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/store/modules/user';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { v4 as uuidv4 } from "uuid";
import TempData from './data';
import { inject } from 'vue';
import { Ref } from 'vue';
import { getSyncAssetDetail } from '@/api/material/material';

const BONUS_TIME = 2000

const uuid = uuidv4();
const props = defineProps(['resetAvatar', 'gender', 'avatar', 'tempData', 'previewLoading']);
const tempData = props.tempData
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const { currentProviderId } = storeToRefs(userStore);
const animationListRef = ref<any>(null)
const ttsModuleRef = ref<any>(null)
const previewLoading = inject('previewLoading') as Ref<boolean>
const pause = inject('pause') as any

const save = async () => {
  console.log('tempData', tempData)
  if (tempData.name.trim().length === 0) {
    ElMessage.error('请填写模板名称')
    return
  }
  if (
    tempData.text.trim().length === 0
    && tempData.animation.filter(i => i).length !== 2) {
    ElMessage.error('请输入文本或者选择动作(男、女)！')
    return
  }
  const res = await fetch('https://avatarx-jxyd.oss-cn-hangzhou.aliyuncs.com/console/cloud-render/template/default/type-video-data.json')
  const data: typeof TempData = await res.json()
  // let totalTime = 0
  if (tempData.text) {
    const [maleTtsRes, femaleTtsRes] = await Promise.all([
      uploadTts({
        "provider_id": currentProviderId.value,
        "word": tempData.text,
        "voice": "Aida",
        "ratioSpeed": 1,
        "template_uuid": uuid
      }),
      uploadTts({
        "provider_id": currentProviderId.value,
        "word": tempData.text,
        "voice": "Aiqi",
        "ratioSpeed": 1,
        "template_uuid": uuid
      })
    ])
    console.log('maleTtsRes', maleTtsRes)
    console.log('femaleTtsRes', femaleTtsRes)
    const maleTTSTrack = data.male.TrackBlocks.find(i => i.Type === 'TTS')
    const femaleTTSTrack = data.female.TrackBlocks.find(i => i.Type === 'TTS')
    const maleTrackTTSInfo = maleTTSTrack?.Metas.TTSInfo
    const femaleTrackTTSInfo = femaleTTSTrack?.Metas.TTSInfo
    if (maleTrackTTSInfo && maleTtsRes.code === 0) {
      const {
        audio,
        relativeTimestamp,
        relativeSentence,
        relativeSentence2,
        relativeTxt,
        time,
      } = maleTtsRes.data
      maleTTSTrack.EndTime = time * 1000 + BONUS_TIME
      maleTrackTTSInfo.TTSAudioPath = audio
      maleTrackTTSInfo.TTSTimeStamp = relativeTimestamp
      maleTrackTTSInfo.TTSPath = relativeTimestamp
      maleTrackTTSInfo.TTSSentenceTimeStampPath = relativeSentence2
      maleTrackTTSInfo.TTSWordTimeStampPath = relativeSentence
      maleTrackTTSInfo.TTSTxtPath = relativeTxt
      data.male.GlobalInfo.TotalTime = time * 1000 + BONUS_TIME
    }
    if (femaleTrackTTSInfo && femaleTtsRes.code === 0) {
      const {
        audio,
        relativeTimestamp,
        relativeSentence,
        relativeSentence2,
        relativeTxt,
        time,
      } = femaleTtsRes.data
      femaleTTSTrack.EndTime = time * 1000 + BONUS_TIME
      femaleTrackTTSInfo.TTSAudioPath = audio
      femaleTrackTTSInfo.TTSTimeStamp = relativeTimestamp
      femaleTrackTTSInfo.TTSPath = relativeTimestamp
      femaleTrackTTSInfo.TTSSentenceTimeStampPath = relativeSentence2
      femaleTrackTTSInfo.TTSWordTimeStampPath = relativeSentence
      femaleTrackTTSInfo.TTSTxtPath = relativeTxt
      data.female.GlobalInfo.TotalTime = time * 1000 + BONUS_TIME
    }
  } else {
    data.female.GlobalInfo.TotalTime = 5000
    data.male.GlobalInfo.TotalTime = 5000
    data.male.TrackBlocks = data.male.TrackBlocks.filter(i => i.Type !== 'TTS')
    data.female.TrackBlocks = data.female.TrackBlocks.filter(i => i.Type !== 'TTS')
  }

  if (tempData.animation[0]) {
    const animationTrack = data.male.TrackBlocks.find(i => i.Type === 'Animation')
    const maleTrackAnimationInfo = animationTrack?.Metas.AnimationInfo
    if (maleTrackAnimationInfo) {
      const target = animationListRef.value.animationList.find(i => i.file_path === tempData.animation[0])
      if (target && animationTrack && currentProviderId.value) {
        const res = await getSyncAssetDetail({
          provider_id: `${currentProviderId.value}`,
          filesystemposition: target.filesystemposition
        })
        animationTrack.EndTime = res.data?.duration * 1000 || 1000 + BONUS_TIME
        data.male.GlobalInfo.TotalTime = Math.max(data.male.GlobalInfo.TotalTime, animationTrack.EndTime)
      }
      maleTrackAnimationInfo.Path = tempData.animation[0]
    }
  } else {
    const animationTrack = data.male.TrackBlocks.find(i => i.Type === 'Animation')
    if (animationTrack) {
      animationTrack.EndTime = data.male.GlobalInfo.TotalTime
    }
  }
  if (tempData.animation[1]) {
    const animationTrack = data.female.TrackBlocks.find(i => i.Type === 'Animation')
    const femaleTrackAnimationInfo = data.female.TrackBlocks.find(i => i.Type === 'Animation')?.Metas.AnimationInfo
    if (femaleTrackAnimationInfo) {
      const target = animationListRef.value.animationList.find(i => i.file_path === tempData.animation[1])
      if (target && animationTrack && currentProviderId.value) {
        const res = await getSyncAssetDetail({
          provider_id: `${currentProviderId.value}`,
          filesystemposition: target.filesystemposition
        })
        animationTrack.EndTime = res.data?.duration * 1000 || 1000 + BONUS_TIME
        data.female.GlobalInfo.TotalTime = Math.max(data.female.GlobalInfo.TotalTime, animationTrack.EndTime)
      }
      femaleTrackAnimationInfo.Path = tempData.animation[1]
    }
  } else {
    const animationTrack = data.female.TrackBlocks.find(i => i.Type === 'Animation')
    if (animationTrack) {
      animationTrack.EndTime = data.female.GlobalInfo.TotalTime
    }
  }
  if (tempData.background) {
    const maleTrackBackgroundInfo = data.male.TrackBlocks.find(i => i.UUID === 'background-track')?.Metas.EffectInfo
    if (maleTrackBackgroundInfo) {
      maleTrackBackgroundInfo.EffectPath = tempData.background

    }
    const femaleTrackAnimationInfo = data.female.TrackBlocks.find(i => i.UUID === 'background-track')?.Metas.EffectInfo
    if (femaleTrackAnimationInfo) {
      femaleTrackAnimationInfo.EffectPath = tempData.background
    }
  } else {
    data.male.TrackBlocks = data.male.TrackBlocks.filter(i => i.UUID !== 'background-track')
    data.female.TrackBlocks = data.female.TrackBlocks.filter(i => i.UUID !== 'background-track')
  }

  if (tempData.foreground) {
    const maleTrackForegroundInfo = data.male.TrackBlocks.find(i => i.UUID === 'foreground-track')?.Metas.EffectInfo
    if (maleTrackForegroundInfo) {
      maleTrackForegroundInfo.EffectPath = tempData.foreground
    }
    const femaleTrackAnimationInfo = data.female.TrackBlocks.find(i => i.UUID === 'foreground-track')?.Metas.EffectInfo
    if (femaleTrackAnimationInfo) {
      femaleTrackAnimationInfo.EffectPath = tempData.foreground
    }
  } else {
    data.male.TrackBlocks = data.male.TrackBlocks.filter(i => i.UUID !== 'foreground-track')
    data.female.TrackBlocks = data.female.TrackBlocks.filter(i => i.UUID !== 'foreground-track')
  }

  if (tempData.music) {
    const maleTrackBgm = data.male.TrackBlocks.find(i => i.Type === 'Audio')
    if (maleTrackBgm) {
      maleTrackBgm.EndTime = data.male.GlobalInfo.TotalTime
    }

    const femaleTrackBgm = data.female.TrackBlocks.find(i => i.Type === 'Audio')
    if (femaleTrackBgm) {
      femaleTrackBgm.EndTime = data.female.GlobalInfo.TotalTime
    }

    const maleTrackMusicInfo = data.male.TrackBlocks.find(i => i.Type === 'Audio')?.Metas.AudioInfo
    if (maleTrackMusicInfo) {
      maleTrackMusicInfo.Path = HOST + tempData.music
    }
    const femaleTrackMusicInfo = data.female.TrackBlocks.find(i => i.Type === 'Audio')?.Metas.AudioInfo
    if (femaleTrackMusicInfo) {
      femaleTrackMusicInfo.Path = HOST + tempData.music
    }
  } else {
    data.male.TrackBlocks = data.male.TrackBlocks.filter(i => i.Type !== 'Audio')
    data.female.TrackBlocks = data.female.TrackBlocks.filter(i => i.Type !== 'Audio')
  }
  console.log('data', data)
  data.male.Tracks = data.male.Tracks.filter(i => data.male.TrackBlocks.find(j => j.TrackID === i.UUID))
  data.female.Tracks = data.female.Tracks.filter(i => data.female.TrackBlocks.find(j => j.TrackID === i.UUID))

  data.female.TrackBlocks.forEach((trackBlock) => {
    if (trackBlock.Type !== 'Animation' && trackBlock.Type !== 'TTS') {
      trackBlock.EndTime = data.female.GlobalInfo.TotalTime
    }
  })
  data.male.TrackBlocks.forEach((trackBlock) => {
    if (trackBlock.Type !== 'Animation' && trackBlock.Type !== 'TTS') {
      trackBlock.EndTime = data.male.GlobalInfo.TotalTime
    }
  })
  const formData = new FormData();
  if (tempData.imgBlob) {
    formData.append('icon', tempData.imgBlob);
  }
  formData.append('name', tempData.name);
  formData.append('data', JSON.stringify({
    ...data,
    extra: {
      ...tempData
    }
  }));
  formData.append('resources', JSON.stringify([{
    type: 2,
    track_id: "f70f497b-28e1-43b6-856c-8a4b6213b7a1",
    id: tempData.musicId
  }]));
  if (route.query.templateId) {
    await editTemplate(route.query.templateId, formData)
    ElMessage({
      message: '保存成功！',
      type: 'success',
    });
  } else {
    formData.append('provider_id', `${currentProviderId.value}`);
    formData.append('style_id', (route.query.styleId || '1') as any);
    formData.append('uuid', uuid);
    await addTemplate(formData)
    ElMessage({
      message: '创建成功！',
      type: 'success',
    });
  }

  router.push({
    name: 'template-list',
  });
}

const getTtsModule = async () => {
  await ttsModuleRef.value.emitByText()
}

defineExpose({
  save,
  tempData,
  getTtsModule
})
</script>
<style lang="less">
.template-handler-wrapper {
  width: 710px;
  height: 100%;
  background: #fff;

  .template-handler-tabs {
    height: 100%;

    p {
      margin: 0;

      &.title {
        margin-bottom: 16px;
        color: rgba(34, 35, 38, 1);
        font-weight: bold;
      }
    }

    .template-handler-tab-avatar {
      img {
        width: 106px;
        height: 184px;
        margin-right: 16px;
        border: 2px solid rgba(64, 126, 255, 0);
        border-radius: 5px;

        &.selected {
          border: 2px solid rgba(64, 126, 255, 1);
        }
      }
    }
  }
}
</style>
