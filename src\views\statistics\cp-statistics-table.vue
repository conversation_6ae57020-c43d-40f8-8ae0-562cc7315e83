<template>
  <el-form :inline="true" :model="queryParams" class="flex flex-wrap items-start">
    <el-form-item label="风格类型：" v-if="[0, 1].includes(queryParams.resource_type)">
      <StyleCascader
        :key="queryParams.resource_type"
        v-model="queryParams.style_id"
        style="width: 200px"
      />
    </el-form-item>
    <el-form-item label="素材类型：" v-if="queryParams.resource_type == 0">
      <el-tree-select
        clearable
        v-model="queryParams.type"
        :data="typeTreeData"
        :check-strictly="false"
        :render-after-expand="false"
        :props="{ label: 'chinese', value: 'name' }"
      />
    </el-form-item>
    <el-form-item label="统计时间:" class="w-80">
      <InnerMonthPicker
        v-model="queryParams.dateRange"
      />
    </el-form-item>
    <el-form-item label="使用次数范围:">
      <NInputNumber
        :min="0"
        :max="999999"
        :step="1"
        class="w-24"
        :show-button="false"
        clearable
        :precision="0"
        :value="queryParams.min_times"
        :on-update:value="(v) => {
          handleMinTimesChange(v)
          queryParams.min_times = v
        }"
      />
      <span class="inline mx-1">~</span>
      <NInputNumber
        :min="0"
        :max="999999"
        :step="1"
        class="w-24"
        :show-button="false"
        clearable
        :precision="0"
        :value="queryParams.max_times"
        :on-update:value="(v) => {
          handleMaxTimesChange(v)
          queryParams.max_times = v
        }"
      />
    </el-form-item>

    <el-form-item>
      <el-input
        class="mr-3"
        :prefix-icon="Search"
        v-model.trim="queryParams.keyword"
        :placeholder="searchPlacehoder"
        clearable
      />
    </el-form-item>
    <el-button class="ml-auto" type="primary" @click="handleExport">导出</el-button>
  </el-form>
  <el-table :data="tableData" style="width: 100%" :header-row-style="{ background: '#e5e7eb' }">
    <el-table-column prop="resource_name" label="音乐名称" v-if="queryParams.resource_type == 2" />
    <el-table-column
      v-if="[0, 1].includes(queryParams.resource_type)"
      :label="labelName"
      :show-overflow-tooltip="true"
    >
      <template #default="scope">
        <div class="flex">
          <img alt="图片" :src="scope.row.icon_path" class="img" />
          <div class="title">
            <div>{{ scope.row.resource_name }}</div>
            <div>{{ scope.row.file_path }}</div>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column
      v-if="[0, 1].includes(queryParams.resource_type)"
      prop="provider.title"
      label="所属合作伙伴"
    />
    <el-table-column
      v-if="queryParams.resource_type == 0"
      prop="type"
      label="素材类型"
      :formatter="materialFormatter"
    />
    <el-table-column
      v-if="[0, 1].includes(queryParams.resource_type)"
      prop="provider_style.title"
      label="风格类型"
    />
    <el-table-column prop="usage" label="使用次数" />
    <el-table-column label="操作" width="100">
      <template #default="scope">
        <el-button type="primary" :text="true" @click="handleView(scope.row)">查看详情</el-button>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    class="flex my-4 justify-end"
    background
    v-model:page-size="queryParams.limit"
    v-model:current-page="queryParams.page"
    layout="total, prev, pager, next, sizes, jumper"
    :total="totalRef"
  />
  <ModalTable @register="register" class="basicModalLight" ref="modalRef" />
</template>

<script setup lang="ts">
  import { getAssetTypeList } from '@/api/material/material';
  import { getStatisticsData } from '@/api/statistics';
  import { ModalTable, useModalTable } from '@/components/ModalTable';
  import { StyleCascader } from '@/components/StyleCascader';
  import { useUserStore } from '@/store/modules/user';
  import { Search } from '@element-plus/icons-vue';
  import { onMounted, reactive, ref, watch, computed } from 'vue';
  import { useExport } from '@/views/hooks';
  import { format } from 'date-fns';
  import { useDebounceFn } from '@vueuse/core';
  import { useHandleMaxTimeChange } from './useHandMaxTimesChange';
  import { InnerMonthPicker } from '@/components/InnerMonthPicker';
  import { NInputNumber } from 'naive-ui';

  const props = defineProps({
    resourceType: {
      type: Number,
      default: 0,
    },
  });

  const currentRow = ref({});
  const allTypeOptions = ref<any>([]);
  const { exportFile } = useExport();

  const userStore = useUserStore();

  const totalRef = ref(0);
  const tableData = ref([]);

  const labelName = computed(() => {
    return props.resourceType == 0 ? '素材名称' : '形象名称';
  });
  const queryParams = reactive<any>({
    provider_id: '',
    style_id: '',
    type: '',
    dateRange: [
      format(new Date(new Date().setDate(1)), 'yyyy-MM-dd'),
      format(new Date(), 'yyyy-MM-dd'),
    ],
    min_times: undefined,
    max_times: undefined,
    resource_type: 0,
    keyword: '',
    page: 1,
    limit: 10,
  });

  const [handleMaxTimesChange, handleMinTimesChange] = useHandleMaxTimeChange(queryParams);

  watch(
    () => props.resourceType,
    () => {
      totalRef.value = 0;
      tableData.value = [];
      queryParams.resource_type = props.resourceType;
      queryParams.style_id = '';
      queryParams.type = '';
      queryParams.keyword = '';
      queryParams.dateRange = [
        format(new Date(new Date().setDate(1)), 'yyyy-MM-dd'),
        format(new Date(), 'yyyy-MM-dd'),
      ];
      queryParams.min_times = undefined;
      queryParams.max_times = undefined;
      queryParams.page = 1;
      queryParams.limit = 10;
    }
  );

  const [register, { openModal }] = useModalTable({
    title: '查看详情',
    data: currentRow,
  });
  const PLACEHOLDER_TIP = ['请输入素材名称或bundle路径', '请输入形象名称', '请输入音乐名称'];
  const searchPlacehoder = computed(() => {
    return PLACEHOLDER_TIP[props.resourceType];
  });

  const materialFormatter = (_row, _column, cellValue) => {
    return allTypeOptions.value?.find(({ name }) => name == cellValue)?.chinese;
  };

  const typeTreeData = ref<any>([]);

  const fetchTypeTree = async () => {
    const { code, data } = await getAssetTypeList();
    if (code == 0) {
      typeTreeData.value = [
        {
          name: '',
          chinese: '全部',
          order: -1,
          level: 1,
          parent: null,
          gender: 0,
        },
        ...data,
      ];
      allTypeOptions.value = flatTree(data);
    }
  };

  const flatTree = (data: any[] = []) => {
    return data.reduce((prev, item) => {
      return [...prev, ...item.children];
    }, []);
  };

  watch(
    () => queryParams,
    () => {
      getList();
    },
    { deep: true }
  );

  onMounted(async () => {
    await fetchTypeTree();
    await getList();
  });

  const generateParamas = () => {
    const provider_id = userStore.currentProviderId;
    const { resource_type, style_id, type, dateRange, min_times, max_times, ...other } =
      queryParams;
    const params: any = { ...other, resource_type, provider_id };

    if (dateRange && Array.isArray(dateRange)) {
      const [start, end] = dateRange;
      params.create_start = start;
      params.create_end = end;
    }
    if (min_times !== undefined) {
      params.min_times = min_times;
    }
    if (max_times !== undefined) {
      params.max_times = max_times;
    }

    if ([0, 1].includes(resource_type)) {
      params.style_id = style_id;
    }
    if (resource_type === 0) {
      params.type = type;
    }

    return params;
  };

  //获取列表数据
  const getList = useDebounceFn(async () => {
    const params = generateParamas();
    const {
      data: { total, rows: list },
    } = await getStatisticsData(params);
    tableData.value = list;
    totalRef.value = total;
  }, 1000);

  //导出
  const handleExport = () => {
    const params = generateParamas();
    exportFile('/business/export-query/statistic/export/refs4cp', params);
  };
  //查看详情
  const handleView = (row) => {
    currentRow.value = row;
    openModal({
      role: 'cp',
      resource_type: queryParams.resource_type,
      ...row,
    });
  };
</script>
<style lang="less" scoped>
  .box-card {
    min-height: 800px;

    .img {
      width: 60px;
      height: 60px;
    }

    .title {
      flex: 1;
      margin-left: 10px;
    }
  }
</style>
