import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

/**
 * 表示视图方向的枚举
 * @enum {number}
 */
export const Direction = {
    X: 0,
    Y: 1,
    Z: 2
};

/**
 * 获取默认相机位置
 * @param {Direction} direction - 视图方向
 * @returns {THREE.PerspectiveCamera} - 透视相机
 */
export function GetDefaultCamera(direction) {
    const camera = new THREE.PerspectiveCamera(45.0, 1.0, 0.1, 1000.0);
    if (direction === Direction.X) {
        camera.position.set(1.0, 0.0, 0.0);
        camera.up.set(0.0, 1.0, 0.0);
    } else if (direction === Direction.Y) {
        camera.position.set(0.0, 1.0, 0.0);
        camera.up.set(0.0, 0.0, 1.0);
    } else if (direction === Direction.Z) {
        camera.position.set(0.0, 0.0, 1.0);
        camera.up.set(0.0, 1.0, 0.0);
    }
    camera.lookAt(new THREE.Vector3(0.0, 0.0, 0.0));
    return camera;
}

/**
 * 3D查看器类
 */
export class Viewer {
    /**
     * 构造函数
     */
    constructor() {
        this.canvas = null;
        this.renderer = null;
        this.scene = null;
        this.camera = null;
        this.controls = null;
        this.isInitialized = false;
    }

    /**
     * 初始化查看器
     * @param {HTMLCanvasElement} canvas - 画布元素
     */
    Init(canvas) {
        if (this.isInitialized) {
            return;
        }

        this.canvas = canvas;
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: true
        });

        // 启用阴影
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // 创建场景
        this.scene = new THREE.Scene();

        // 创建相机
        this.camera = new THREE.PerspectiveCamera(45.0, 1.0, 0.1, 1000.0);
        this.camera.position.set(0.0, 0.0, 3.0);
        this.camera.up.set(0.0, 1.0, 0.0);
        this.camera.lookAt(new THREE.Vector3(0.0, 0.0, 0.0));

        // 创建轨道控制器
        this.controls = new OrbitControls(this.camera, this.canvas);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = true;

        // 添加光源
        this.SetupLights();

        // 添加坐标轴辅助工具（调试用）
        //const axesHelper = new THREE.AxesHelper(5);
        //this.scene.add(axesHelper);

        this.isInitialized = true;
    }

    /**
     * 设置相机
     * @param {THREE.Camera} camera - 相机
     */
    SetCamera(camera) {
        if (!this.isInitialized) {
            return;
        }

        this.camera = camera;
        this.controls = new OrbitControls(this.camera, this.canvas);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = true;
    }

    /**
     * 设置光源
     */
    SetupLights() {
        // 添加环境光
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        this.scene.add(ambientLight);

        // 添加方向光（模拟太阳光）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 1).normalize();
        directionalLight.castShadow = true;

        // 设置阴影参数
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.1;
        directionalLight.shadow.camera.far = 100;

        // 设置阴影相机视锥体
        directionalLight.shadow.camera.left = -5;
        directionalLight.shadow.camera.right = 5;
        directionalLight.shadow.camera.top = 5;
        directionalLight.shadow.camera.bottom = -5;

        this.scene.add(directionalLight);

        // 添加半球光（模拟环境光）
        const hemisphereLight = new THREE.HemisphereLight(0xffffbb, 0x080820, 0.3);
        this.scene.add(hemisphereLight);
    }

    /**
     * 调整大小
     * @param {number} width - 宽度
     * @param {number} height - 高度
     */
    Resize(width, height) {
        if (!this.isInitialized) {
            return;
        }

        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height, false);
    }

    /**
     * 渲染场景
     */
    Render() {
        if (!this.isInitialized) {
            return;
        }

        try {
            // 更新控制器
            if (this.controls) {
                this.controls.update();
            }

            // 渲染场景
            this.renderer.render(this.scene, this.camera);
        } catch (error) {
            console.error('渲染时发生错误:', error);
        }
    }

    /**
     * 销毁查看器，释放资源
     */
    Destroy() {
        if (!this.isInitialized) {
            return;
        }

        // 移除场景中的所有对象
        while (this.scene.children.length > 0) {
            const child = this.scene.children[0];
            // 如果有几何体或材质，释放它们
            if (child.geometry) {
                child.geometry.dispose();
            }
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                } else {
                    child.material.dispose();
                }
            }
            this.scene.remove(child);
        }

        // 释放渲染器资源
        if (this.renderer) {
            this.renderer.dispose();
            this.renderer = null;
        }

        // 释放控制器
        if (this.controls) {
            this.controls.dispose();
            this.controls = null;
        }

        this.canvas = null;
        this.scene = null;
        this.camera = null;
        this.isInitialized = false;
    }
} 