<template>
  <div class="login-body bg-cover bg-left-top flex flex-col">
    <logo-bar />
    <div class="flex-1 flex items-center">
      <div class="flex-1"></div>
      <div class="flex-1 flex">
        <login-form class="m-auto ml-0 w-8/12" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import LogoBar from '@/components/LogoBar';
  import LoginForm from './LoginForm.vue';
</script>

<style lang="less" scoped>
  .login-body {
    min-height: 100vh;
    background-image: url('@/assets/images/login/login-bg.png');
  }
</style>
