<template>
  <el-date-picker
    :popper-class="props.class"
    :clearable="false"
    v-model="dateValue"
    :disabled-date="disabledDate"
    @calendar-change="calendarChange"
    @visible-change="visibleDate"
    format="YYYY-MM-DD"
    value-format="YYYY-MM-DD"
    type="daterange"
    range-separator="至"
    start-placeholder="开始时间"
    end-placeholder="结束时间"
  />
</template>

<script lang="ts" setup>
  import { computed, ref, defineProps, defineEmits } from 'vue';
  import { useDisabledOutCurrentMomthDate } from '@/views/hooks/useDisabledDate';

  const props = defineProps(['modelValue', 'class']);
  const emit = defineEmits(['update:modelValue']);
  const dateValue = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    },
  });

  const chooseDay = ref<any>(null);
  const secondDay = ref<any>(null);

  const disabledDate = useDisabledOutCurrentMomthDate(chooseDay);
  const calendarChange = (e) => {
    chooseDay.value = e[0]
    secondDay.value = e[1]
  }
  const visibleDate = (visible) => {
    if (secondDay.value && !visible) {
      chooseDay.value = null
      secondDay.value = null
    }
  }

  // onUnmounted(() => {
  //   emit('update:modelValue', []);
  // });
</script>
