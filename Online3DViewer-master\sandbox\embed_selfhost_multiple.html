<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no">

	<title>Online 3D Viewer</title>

	<script type="text/javascript" src="../build/engine_dev/o3dv.min.js"></script>

    <script type='text/javascript'>
        window.addEventListener ('load', () => {
            let viewers = OV.Init3DViewerElements ();
			let firstViewer = viewers[0].viewer;
			firstViewer.SetBackgroundColor (new OV.RGBAColor (34, 34, 34, 255));
        });
    </script>

    <style>
        div.online_3d_viewer
        {
            float: left;
            border: 1px solid #eeeeee;
            margin: 0px 4px 4px 0px;
        }
    </style>
</head>

<body>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../test/testfiles/3ds/cube_four_instances.3ds,../../test/testfiles/3ds/texture.png">
    </div>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../test/testfiles/obj/hundred_cubes.obj,../../test/testfiles/obj/hundred_cubes.mtl">
    </div>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../test/testfiles/obj/hundred_cubes.obj,../../test/testfiles/obj/not_exising.mtl,../../test/testfiles/obj/hundred_cubes.mtl">
    </div>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../test/testfiles/3ds/cube_four_instances.3ds,../../test/testfiles/3ds/texture.png">
    </div>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../test/testfiles/obj/hundred_cubes.obj,../../test/testfiles/obj/hundred_cubes.mtl">
    </div>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../test/testfiles/obj/hundred_cubes.obj">
    </div>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../test/testfiles/obj/hundred_cubes.obj"
        defaultcolor="200,0,0"
		backgroundcolor="200,200,200">
    </div>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="wrong.3ds">
    </div>
</body>

</html>
