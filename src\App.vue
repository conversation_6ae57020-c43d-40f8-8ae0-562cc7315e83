<template>
  <el-config-provider :locale="elZhCN">
    <NConfigProvider :locale="zhCN" :theme-overrides="getThemeOverrides" :date-locale="dateZhCN">
      <AppProvider>
        <AvatarRender>
          <RouterView :key="route.path" />
        </AvatarRender>
      </AppProvider>
    </NConfigProvider>
  </el-config-provider>
</template>

<script lang="ts" setup>
import elZhCN from 'element-plus/dist/locale/zh-cn.mjs';
import { dateZhCN, zhCN } from 'naive-ui';
import { computed } from 'vue';
import { AppProvider } from '@/components/Application';
import { useDesignSettingStore } from '@/store/modules/designSetting';
import { lighten } from '@/utils/index';
import { useRoute } from 'vue-router';
import { AvatarRender } from '@/components/AvatarRender';

const route = useRoute();
const designStore = useDesignSettingStore();

/**
 * @type import('naive-ui').GlobalThemeOverrides
 */
const getThemeOverrides = computed(() => {
  const appTheme = designStore.appTheme;
  const lightenStr = lighten(designStore.appTheme, 6);
  return {
    common: {
      primaryColor: appTheme,
      primaryColorHover: lightenStr,
      primaryColorPressed: lightenStr,
      primaryColorSuppl: appTheme,
    },
    LoadingBar: {
      colorLoading: appTheme,
    },
  };
});
</script>

<style lang="less">
  @import 'styles/index.less';
  @import 'styles/reset-element-plus.less';
</style>
