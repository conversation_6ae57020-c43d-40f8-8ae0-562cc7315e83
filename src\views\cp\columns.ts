import { BasicColumn } from '@/components/Table';
export interface ListData {
  id: string;
  name: string;
  avatar: string;
  address: string;
  beginTime: string;
  endTime: string;
  date: string;
}
export const columns: BasicColumn<ListData>[] = [
  /* {
    title: 'id',
    key: 'id',
    width: 100,
  }, */
  {
    title: '公司名称',
    key: 'name',
    width: 100,
  },
  {
    title: '联系人',
    key: 'name',
    width: 100,
  }, {
    title: '联系电话',
    key: 'name',
    width: 100,
  },
   {
    title: '账号状态',
    key: 'name',
    width: 100,
  },
  /* {
    title: '头像',
    key: 'avatar',
    width: 100,
    render(row) {
      return h(NAvatar, {
        size: 48,
        src: row.avatar,
      });
    },
  }, */
 /*  {
    title: '地址',
    key: 'address',
    auth: ['basic_list'], // 同时根据权限控制是否显示
    ifShow: (_column) => {
      return true; // 根据业务控制是否显示
    },
    width: 150,
  }, */
  /* {
    title: '开始日期',
    key: 'beginTime',
    width: 160,
  }, */
  /* {
    title: '结束日期',
    key: 'endTime',
    width: 160,
  }, */
  {
    title: '注册时间',
    key: 'date',
    width: 100,
  },
];
