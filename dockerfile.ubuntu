FROM ubuntu:21.04 AS base
WORKDIR /app
RUN sed -i 's/archive.ubuntu.com/mirrors.ustc.edu.cn\/ubuntu-old-releases/g' /etc/apt/sources.list && \
    sed -i 's/security.ubuntu.com/mirrors.ustc.edu.cn\/ubuntu-old-releases/g' /etc/apt/sources.list && \
    apt update -y && \
    DEBIAN_FRONTEND=noninteractive apt install -y tzdata && \
    echo "Asia/Shanghai" > /etc/timezone && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
ENV PATH=$PATH:/usr/local/node/bin
RUN mkdir -p /usr/local && cd /usr/local && \
    apt install -y curl && \
    NODE_VERSION="v18.17.1" && \
    NODE_OS_ARCH="linux-x64" && \
    curl -o node.tar.gz https://mirrors.tuna.tsinghua.edu.cn/nodejs-release/${NODE_VERSION}/node-${NODE_VERSION}-${NODE_OS_ARCH}.tar.gz && \
    tar -zxf node.tar.gz && \
    rm -rf node.tar.gz && \
    mv node-${NODE_VERSION}-${NODE_OS_ARCH} node

# package
FROM base AS stage.package
COPY ./package.json /app/package.json
COPY ./pnpm-lock.yaml /app/pnpm-lock.yaml
RUN sed -i 3d /app/package.json

# build
FROM base AS stage.build
# NPM镜像源设置：淘宝镜像源
RUN npm config set registry https://registry.npmmirror.com
RUN npm install -g pnpm@8.9.2
RUN pnpm install -g fs-extra
COPY --from=stage.package /app/package.json /app/package.json
COPY --from=stage.package /app/pnpm-lock.yaml /app/pnpm-lock.yaml
# RUN pnpm install --frozen-lockfile
RUN pnpm install
COPY . /app
RUN pnpm run build

# nginx or openresty
FROM docker.io/openresty/openresty:1.19.9.1-alpine-amd64
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories && \
    apk add tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone
WORKDIR /app
COPY ./openresty/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf
COPY --from=stage.build /app/dist /app/public
