<template>
  <el-card class="n-layout-page-header">
    <el-form :model="form" ref="formRef" label-width="80px" :rules="rules">
      <el-form-item label="所属类别" required>
        <el-radio-group v-model="form.category" :disabled="id">
          <el-radio :label="0">CP用户</el-radio>
          <el-radio :label="1">管理员</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="所属模块" required prop="module_name">
        <el-input v-model="form.module_name" maxlength="100" @input="value => handleInput('module_name', value)" />
      </el-form-item>
      <el-form-item label="权限名称" required prop="name">
        <el-input v-model="form.name" maxlength="100" @input="value => handleInput('name', value)" />
      </el-form-item>
      <el-form-item label="key" required prop="key">
        <el-input v-model="form.key" maxlength="100" @input="value => handleInput('key', value)"/>
      </el-form-item>
    </el-form>
    <el-button @click="submit">提交</el-button>
    <el-button @click="goback">返回</el-button>
  </el-card>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage, FormInstance } from 'element-plus';
  import { addPermission, editPermission, getPermissionDetail } from '@/api/system/permission';
  import { useFilterInputHander } from '@/views/hooks';

  const router = useRouter();
  const route = useRoute();
  const { id } = route.query;

  const formRef = ref<FormInstance>();
  const form = reactive({
    category: 0,
    module_name: '',
    name: '',
    key: '',
  });
  const handleInput = useFilterInputHander(form);

  const rules = {
    module_name: [{ required: true, message: '请输入所属模块', trigger: 'blur' }],
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    key: [{ required: true, message: '请输入key', trigger: 'blur' }],
  };

  onMounted(async () => {
    if (id) {
      const res = await getPermissionDetail(id);
      for (let k in form) {
        form[k] = res.data[k];
      }
    }
  });

  async function submit() {
    if (!formRef.value) return;
    formRef.value.validate(async (valid) => {
      if (valid) {
        if (id) {
          await editPermission(id, {
            ...form,
          });
          ElMessage({
            message: '修改成功！',
            type: 'success',
          });
        } else {
          await addPermission({
            ...form,
          });
          ElMessage({
            message: '新增成功！',
            type: 'success',
          });
        }
        goback();
      }
    });
  }

  function goback() {
    router.push({ path: '/permission' });
  }
</script>

<style lang="less" scoped></style>
