<template>
  <el-card class="box-card">
    <el-form :inline="true" :model="queryParams" class="flex-1 flex flex-wrap">
      <el-form-item label="联系人:">
        <el-input v-model.trim="queryParams.name" placeholder="联系人" clearable />
      </el-form-item>
      <el-form-item label="联系电话:">
        <el-input
          v-model.trim="queryParams.phone"
          @input="(value) => handleInput('phone', value, 'number')"
          placeholder="联系电话"
          clearable
        />
      </el-form-item>
      <el-form-item label="公司名称:">
        <el-input v-model.trim="queryParams.company_name" placeholder="公司名称" clearable />
      </el-form-item>
      <el-form-item label="账号状态:">
        <el-select v-model="queryParams.status" clearable placeholder="请选择账号状态">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            clearable
          />
        </el-select>
      </el-form-item>
      <el-form-item label="注册时间:" class="w-80">
        <el-date-picker
          clearable
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          v-model="queryParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :disabled-date="disabledDate"
        />
      </el-form-item>
    </el-form>
    <el-table :data="tableData" style="width: 100%">
      <template v-for="(item, index) in tableColumns">
        <el-table-column
          v-if="item.show"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :formatter="item.formatter"
          show-overflow-tooltip
        />
      </template>
      <el-table-column label="操作" width="320">
        <template #default="{ row }">
          <el-button type="primary" :text="true" @click="edit(row)"
            >编辑</el-button
          >
          <el-button
            v-if="row.status === 1"
            type="primary"
            :text="true"
            @click="resetPassword(row)"
            >重置密码</el-button
          >
          <el-button
            type="primary"
            v-if="row.status >= 1"
            :text="true"
            @click="handleFreezeOrRecover(row)"
            >{{ freezeOrRecoverBtnText(row.status) }}</el-button
          >
          <el-button type="primary" v-if="row.status == 0" :text="true" @click="handleExamine(row)"
            >审核</el-button
          >
          <el-button type="danger" :text="true" v-if="row.status !== 0" @click="handleDelete(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="flex my-4 justify-end"
      background
      layout="total, prev, pager, next, sizes, jumper"
      v-model:page-size="queryParams.limit"
      v-model:current-page="queryParams.page"
      :total="totalRef"
    />
  </el-card>
  
  <el-dialog v-model="dialogVisible" title="编辑CP" width="900px" destroy-on-close>
    <RegisterForm @cancel="dialogVisible = false" @ok="() => {
      dialogVisible = false
      debunceGetList()
    }" :id="editId"/>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { freezeOrRecoverCp, getCpList, removeCp, postResetPassword } from '@/api/system/user';
  import { useTimeoutFn } from '@/hooks/core/useTimeout';
  import { useDateFormat, useDebounceFn } from '@vueuse/core';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { h, onMounted, reactive, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { disabledDate } from '@/utils/dateUtil';
  import { useFilterInputHander } from '@/views/hooks';
  import RegisterForm from './RegisterForm.vue';

  const router = useRouter();

  const queryParams = reactive({
    name: '',
    status: '',
    phone: '',
    company_name: '',
    dateRange: [] as any,
    page: 1,
    limit: 10,
  });

  const handleInput = useFilterInputHander(queryParams);

  const statusOptions = [
    {
      label: '全部',
      value: '',
    },
    {
      label: '审核驳回',
      value: -1,
    },
    {
      label: '待审核',
      value: 0,
    },
    {
      label: '启用',
      value: 1,
    },
    {
      label: '冻结',
      value: 2,
    },
  ];

  const STATUS_MAP = {
    0: '待审核',
    '-1': '审核驳回',
    1: '启用',
    2: '冻结',
  };

  const STATUS_COLOR_MAP = {
    0: '#FAAD14',
    '-1': '#FF2020',
    1: '#06BF7F',
    2: '#C4C4C4',
  };

  const totalRef = ref(0);
  const tableData = ref([]);

  const freezeOrRecoverBtnText = (status) => {
    if (status == 1) return '冻结';
    if (status == 2) return '恢复';
    return '';
  };

  const statusFormatter = (_row, _column, status, _index) => {
    return h('span', { style: `color: ${STATUS_COLOR_MAP[status]};` }, STATUS_MAP[status]);
  };

  const formateDateTime = (_row, _column, dateTime, _index) => {
    return useDateFormat(dateTime, 'YYYY-MM-DD HH:mm:ss').value;
  };

  const tableColumns = [
    { prop: 'name', label: '联系人', show: true },
    { prop: 'company_name', label: '公司名称', show: true },
    { prop: 'phone', label: '联系电话', show: true, width: 160 },
    { prop: 'status', label: '账号状态', show: true, formatter: statusFormatter, width: 100 },
    { prop: 'created_at', label: '注册时间', formatter: formateDateTime, show: true, width: 200 },
  ];

  const formateQueryParams = () => {
    const { dateRange, ...other } = queryParams;
    const params: any = { ...other };
    if (dateRange && Array.isArray(dateRange)) {
      const [create_start, create_end] = dateRange;
      if (create_start) {
        params.create_start = create_start;
      }
      if (create_end) {
        params.create_end = create_end;
      }
    }
    return params;
  };

  const dialogVisible = ref(false);
  const editId = ref(null);
  const edit = (row) => {
    dialogVisible.value = true;
    editId.value = row.id;
  }
  const resetPassword = async (row) => {
    try {
      const action = await ElMessageBox.confirm(
        `确定要重置联系人为${row.name}的CP账号密码吗?`,
        '确认重置',
        {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }
      );
      if (action == 'confirm') {
        const { code } = await postResetPassword(row.id);
        if (code === 0) {
          ElMessage.success('重置成功!');
        }
      }
    } catch (e) {}
  }

  watch(
    () => queryParams,
    async () => {
      await debunceGetList();
    },
    { deep: true }
  );

  const debunceGetList = useDebounceFn(async () => {
    await getList();
  }, 500);

  //获取列表数据
  const getList = async () => {
    const params = formateQueryParams();
    const {
      code,
      data: { rows: list, count: total },
    } = await getCpList(params);
    if (code === 0) {
      tableData.value = list;
      totalRef.value = total;
    }
  };

  const handleFreezeOrRecover = async (row) => {
    let tips = '';
    let titleTip = '';
    let status = row.status;
    if (row.status === 1) {
      tips = `确定要冻结联系人为${row.name}的CP吗?`;
      titleTip = '确认冻结';
      status = 2;
    } else if (row.status === 2) {
      tips = `确定要恢复联系人为${row.name}的CP吗?`;
      titleTip = '确认恢复';
      status = 1;
    }
    try {
      const action = await ElMessageBox.confirm(tips, {
        title: titleTip,
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      });
      if (action == 'confirm') {
        const { code } = await freezeOrRecoverCp(row.id, { status });
        if (code === 0) {
          ElMessage.success(`${freezeOrRecoverBtnText(row.status)}成功!`);
          useTimeoutFn(() => {
            getList();
          }, 1000);
        }
      }
    } catch (e) {}
  };

  onMounted(() => {
    getList();
  });

  //删除
  const handleDelete = async (row) => {
    try {
      const action = await ElMessageBox.confirm(
        `确定要删除联系人为${row.name}的CP吗?`,
        '确认删除',
        {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }
      );
      if (action == 'confirm') {
        const { code } = await removeCp(row.id);
        if (code === 0) {
          ElMessage.success('删除成功!');
          useTimeoutFn(() => {
            getList();
          }, 1000);
        }
      }
    } catch (e) {}
  };

  //审核
  const handleExamine = (row) => {
    router.push({
      path: '/audit/cp/detail',
      query: {
        id: row.id,
      },
    });
  };
</script>

<style lang="less" scoped>
  .box-card {
    min-height: 800px;

    .img {
      width: 60px;
      height: 60px;
    }
  }
</style>
