<template>
  <wrap-form title="注册账号" submit="formSubmit" submitText="注册" @submit="formSubmit">
    <el-form
      :model="formValue"
      hide-required-asterisk
      :rules="rules"
      ref="formRef"
      autocomplete="off"
    >
      <el-form-item prop="username">
        <el-input
          v-model="formValue.username"
          autocomplete="off"
          :readonly="readOnly"
          maxlength="16"
          @click="readOnly = false"
          @input="(value) => handleInput('username', value, 'no-zh-char')"
          placeholder="输入用户名"
        />
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          maxlength="16"
          @input="(value) => handleInput('password', value, 'no-zh-char')"
          type="password"
          autocomplete="off"
          :show-password="true"
          placeholder="请输入密码"
          v-model="formValue.password"
        />
      </el-form-item>
      <el-form-item prop="password_confirm">
        <el-input
          maxlength="16"
          @input="(value) => handleInput('password_confirm', value, 'no-zh-char')"
          type="password"
          :show-password="true"
          placeholder="确认密码"
          v-model="formValue.password_confirm"
        />
      </el-form-item>
      <el-form-item prop="image_code">
        <div class="flex flex-1 items-center">
          <el-input
            @input="(value) => handleInput('image_code', value, 'number')"
            maxlength="4"
            type="text"
            class="flex-1"
            v-model="formValue.image_code"
            placeholder="请输入图形验证码"
          />
          <div class="w-28 ml-2" @click="handleGetCaptch">
            <img class="w-full" :src="captchaRef" alt="图片验证码" />
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="phone">
        <el-input
          maxlength="11"
          placeholder="请输入手机号"
          @input="(value) => handleInput('phone', value, 'number')"
          v-model.trim="formValue.phone"
        />
      </el-form-item>
      <el-form-item prop="msg_code">
        <div class="flex flex-1 flex-between">
          <el-input
            maxlength="6"
            class="flex-1"
            @input="(value) => handleInput('msg_code', value, 'number')"
            v-model="formValue.msg_code"
            placeholder="请输入手机验证码"
          />
          <el-button
            :disabled="time > 0"
            class="w-28 ml-2 bg-btn-bg-color border-none text-xx-blue"
            @click="handleGetMsgCode"
          >
            {{ time > 0 ? `${time}s后获取` : '获取验证码' }}
          </el-button>
        </div>
      </el-form-item>
      <el-form-item prop="name">
        <el-input
          type="text"
          placeholder="请输入真实姓名"
          v-model="formValue.name"
          @input="(value) => handleInput('name', value)"
        />
      </el-form-item>
      <el-form-item label="服务类型" prop="cp_type">
        <el-radio-group v-model="formValue.cp_type">
          <el-space>
            <el-radio :label="2">企业CP</el-radio>
            <el-radio :label="1">个人CP</el-radio>
          </el-space>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="身份证正反面">
        <div>
          <div class="flex">
            <div class="mr-1">
              <el-form-item prop="id_card_img">
                <el-upload
                  :class="{ 'hidden-upload-btn': formValue.id_card_img.length >= 1 }"
                  accept=".png,.jpeg,.jpg"
                  v-model:file-list="formValue.id_card_img"
                  :limit="1"
                  :show-file-list="true"
                  list-type="picture-card"
                  :http-request="httpRequest"
                  :on-preview="handlePictureCardPreview()"
                  :on-success="handleSuccess('id_card_img')"
                  :before-upload="handleBeforeUpload()"
                >
                  点击上传人像正面
                </el-upload>
              </el-form-item>
            </div>
            <div class="ml-1">
              <el-form-item prop="id_card_imgback">
                <el-upload
                  :class="{ 'hidden-upload-btn': formValue.id_card_imgback.length >= 1 }"
                  accept=".png,.jpeg,.jpg"
                  v-model:file-list="formValue.id_card_imgback"
                  list-type="picture-card"
                  :limit="1"
                  :show-file-list="true"
                  :before-upload="handleBeforeUpload()"
                  :on-success="handleSuccess('id_card_imgback')"
                  :http-request="httpRequest"
                  :on-preview="handlePictureCardPreview()"
                >
                  点击上传国徽面
                </el-upload>
              </el-form-item>
            </div>
          </div>
          <p class="el-upload__tip">支持: jpg、jpeg、png格式；单个文件最大不超过10M</p>
        </div>
      </el-form-item>
      <el-form-item label="公司名称" prop="company_name" v-if="formValue.cp_type === 2">
        <el-input
          type="text"
          placeholder="请输入公司名称"
          maxlength="32"
          v-model="formValue.company_name"
          @input="(value) => handleInput('company_name', value)"
        />
      </el-form-item>
      <el-form-item label="公司营业执照" v-if="formValue.cp_type === 2">
        <div>
        <el-form-item prop="company_img">
          <el-upload
            :class="{ 'hidden-upload-btn': formValue.company_img.length >= 1 }"
            accept=".png,.jpeg,.jpg"
            v-model:file-list="formValue.company_img"
            list-type="picture-card"
            :http-request="httpRequest"
            :before-upload="handleBeforeUpload()"
            :on-preview="handlePictureCardPreview()"
            :on-success="handleSuccess('company_img')"
          >
            点击上传公司营业执照
            <template #tip>
            </template>
          </el-upload>
        </el-form-item>
        <p class="el-upload__tip" > 支持: jpg、jpeg、png格式；单个文件最大不超过10M </p>
        </div>
      </el-form-item>
    </el-form>
    <el-dialog v-model="dialogVisible">
      <div class="flex justify-center items-center">
        <img alt="图片" class="w-full object-contain" :src="dialogImageUrl" />
      </div>
    </el-dialog>
  </wrap-form>
</template>

<script lang="ts" setup>
  import { register } from '@/api/system/user';
  import WrapForm from '@/components/wrap-form';
  import { validatePhone, validateImgCode, validateSmsCode } from '@/utils/validator';
  import {
    useFilterInputHander,
    useCountTime,
    useMsgCode,
    useUpload,
    useEncrypt,
  } from '@/views/hooks';
  import { useTimeoutFn } from '@vueuse/core';
  import { ElMessage } from 'element-plus';
  import { onMounted, reactive, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  const { start, time } = useCountTime();
  const router = useRouter();
  const readOnly = ref(true);
  const encrypt = useEncrypt();

  const httpRequest = useUpload('register', { withToken: false });

  let formValue = reactive({
    username: '',
    password: '',
    password_confirm: '',
    image_code: '',
    phone: '',
    msg_code: '',
    name: '',
    cp_type: 2,
    id_card_img: [] as any[],
    id_card_imgback: [] as any[],
    company_img: [] as any[],
    company_name: '',
  });

  const validatePassSame = (_rule, value, callback) => {
    if (value == '') {
      return callback(new Error('请输入确认密码'));
    } else if (value !== formValue.password) {
      return callback(new Error('确认密码与密码不一致,请重新输入!'));
    } else {
      callback();
    }
  };

  const handleInput = useFilterInputHander(formValue);

  const rules = {
    username: [
      {
        required: true,
        message: '请输入用户名',
        trigger: 'blur',
      },
      {
        min: 8,
        message: '用户名长度至少为8个字符',
        trigger: 'blur',
      },
      {
        pattern: /^(?=.*[a-zA-Z])[a-zA-Z0-9_]+$/,
        message: '用户名必须由字母、数字和下划线组成（字母必填）',
        trigger: 'blur',
      },
    ],
    password: [
      {
        required: true,
        message: '请输入密码',
        trigger: 'blur',
      },
      {
        min: 8,
        message: '密码长度至少为8个字符',
        trigger: 'blur',
      },
      {
        max: 16,
        message: '密码长度最长16个字符',
        trigger: 'blur',
      },
      {
        pattern: /(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{6,30}/,
        message: '密码必须包含大写字母、小写字母、数字和特殊字符',
        trigger: 'blur',
      },
    ],
    password_confirm: [
      {
        required: true,
        message: '请输入确认密码',
        trigger: ['input', 'blur'],
      },
      {
        min: 8,
        message: '密码长度至少为8个字符',
        trigger: 'blur',
      },
      {
        max: 16,
        message: '密码长度最长16个字符',
        trigger: 'blur',
      },
      {
        pattern: /(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{6,30}/,
        message: '密码必须包含大写字母、小写字母、数字和特殊字符',
        trigger: 'blur',
      },
      {
        validator: validatePassSame,
        trigger: 'blur',
      },
    ],
    image_code: [
      {
        required: true,
        message: '请输入图片验证码',
        trigger: ['blur', 'change'],
      },
      {
        required: true,
        validator: validateImgCode,
        trigger: 'blur',
      },
    ],
    phone: [
      {
        required: true,
        validator: validatePhone,
        trigger: ['blur'],
      },
    ],
    msg_code: [
      {
        required: true,
        message: '请输入手机验证码',
        trigger: 'change',
      },
      {
        required: true,
        validator: validateSmsCode,
        trigger: 'blur',
      },
    ],
    name: [
      {
        required: true,
        message: '请输入真实姓名',
        trigger: 'change',
      },
      {
        min: 2,
        message: '真实姓名最少2个字符',
        trigger: 'blur',
      },
      {
        max: 32,
        message: '真实姓名最长32个字符',
        trigger: 'blur',
      },
      {
        pattern: /^[\u4e00-\u9fff]+$/g,
        message: '真实姓名只包含中文',
        trigger: 'blur',
      }
    ],
    cp_type: {
      required: true,
      message: '请选择服务类型',
      trigger: 'change',
    },
    id_card_img: {
      required: true,
      message: '请上传身份证人像面',
      trigger: 'change',
    },
    id_card_imgback: {
      required: true,
      message: '请上传身份证国徽面',
      trigger: 'change',
    },
    company_name: [
      {
        required: true,
        message: '请输入公司名称',
        trigger: 'change',
      },
      {
        min: 8,
        message: '公司名称最少8个字符',
        trigger: 'blur',
      },
      {
        max: 32,
        message: '公司名称最长32个字符',
        trigger: 'blur',
      },
      {
        validator: (_, value, callback) => {
          const r1 = /^[\u4e00-\u9fa5~`!@#$%^&*()-=_+{}|;:'",.<>/?！·￥……（）—【】{}|：“”‘’；、，。《》？]*[\u4e00-\u9fa5]+[\u4e00-\u9fa5~`!@#$%^&*()-=_+{}|;:'",.<>/?·！￥……（）—【】{}|：“”‘’；、，。《》？]*$/
          const r2 = /\d+/g
          if (!r1.test(value)||r2.test(value)) {
            callback(new Error('公司名称只能输入中文和特殊符号（中文必填）'));
          } else {
            callback();
          }
        },
        trigger: 'blur',
      }
    ],
    company_img: {
      required: true,
      message: '请上传营业执照',
      trigger: 'change',
    },
  };

  const formRef = ref();

  const dialogImageUrl = ref('');
  const dialogVisible = ref(false);

  const { captchaRef, fetchCaptch, getMsgCodeAction } = useMsgCode(formRef);

  const handleGetMsgCode = async () => {
    try {
      const { phone, image_code } = formValue;
      await getMsgCodeAction({ phone, image_code, type: 0 });
      start(60);
    } catch (e) {
      console.log('error', e)
    }
  };

  const handleGetCaptch = () => {
    fetchCaptch();
    formValue.image_code = '';
  };

  onMounted(() => {
    handleGetCaptch();
  });

  watch(
    () => formValue.cp_type,
    (n) => {
      if (n == 2) {
        formValue.company_img = [];
        formValue.company_name = '';
      }
    }
  );

  async function formSubmit() {
    console.log(1234)
    try {
      await formRef.value.validate();
      const { id_card_img, id_card_imgback, cp_type, company_name, company_img, ...other } =
        formValue;
      console.log({ id_card_img, id_card_imgback, cp_type, company_name, company_img, ...other })

      const params: any = {
        ...other,
        cp_type,
        id_card_img: id_card_img?.[0]?.id,
        id_card_imgback: id_card_imgback?.[0]?.id,
      };

      if (cp_type == 2) {
        params.company_img = company_img?.[0]?.id;
        params.company_name = company_name;
      }

      const { code } = await register(encrypt(params));
      if (code === 0) {
        ElMessage({
          type: 'success',
          message: '申请提交成功！',
        });
        useTimeoutFn(() => {
          router.push('/login');
        }, 1000);
      } else {
        handleGetCaptch();
        formValue.msg_code = '';
      }
    } catch (e) {
      // handleGetCaptch();
      console.log(e);
      // message.error('验证不通过！')
    }
  }

  const handlePictureCardPreview = () => (uploadFile) => {
    dialogVisible.value = true;
    dialogImageUrl.value = uploadFile.url;
  };

  const handleBeforeUpload = () => (rawFile) => {
    if (!/\.(jpg|png|jpeg)$/.test(rawFile.name)) {
      ElMessage({
        type: 'error',
        message: '仅支持jpg、jpeg、png格式文件上传!',
      });
      return false;
    }
    if (rawFile?.size / (1024 * 1024) > 10) {
      ElMessage({
        type: 'error',
        message: '上传单个文件最大不超过10M!',
      });
      return false;
    }
  };

  const handleSuccess = (type) => (response, files) => {
    files.id = response.id;
    formRef.value.validateField(type);
  };
</script>
