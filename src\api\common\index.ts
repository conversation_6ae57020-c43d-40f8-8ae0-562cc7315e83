import { http } from '@/utils/http/axios';

export type ScopeType = 'asset' | 'avatar' | 'bgm' | 'register' | 'template_icon';
export function uploadFile(scope: ScopeType, params) {
  return http.request({
    url: `/business/file/upload?scope=${scope}`,
    method: 'post',
    params,
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export function exportSystemLog(params) {
  return http.request({
    url: `/business/system-log/export`,
    method: 'get',
    params,
  });
}

export function reportPvlog(params) {
  return http.request({
    url: `/business/pv-log`,
    method: 'post',
    params,
  });
}
