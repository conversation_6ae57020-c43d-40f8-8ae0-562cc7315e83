import { IsDefined, ValueOrDefault, CopyObjectAttributes, IsObjectEmpty, FormatString, EscapeHtmlChars } from './core/core.js';
import { EventNotifier } from './core/eventnotifier.js';
import { SetLocalizedStrings, SetLanguageCode, Loc, FLoc } from './core/localization.js';
import { TaskRunner, RunTaskAsync, RunTasks, RunTasksBatch, WaitWhile } from './core/taskrunner.js';
import { Exporter } from './export/exporter.js';
import { Exporter3dm } from './export/exporter3dm.js';
import { ExportedFile, ExporterBase } from './export/exporterbase.js';
import { ExporterBim } from './export/exporterbim.js';
import { ExporterGltf } from './export/exportergltf.js';
import { ExporterSettings, ExporterModel } from './export/exportermodel.js';
import { ExporterObj } from './export/exporterobj.js';
import { ExporterOff } from './export/exporteroff.js';
import { ExporterPly } from './export/exporterply.js';
import { ExporterStl } from './export/exporterstl.js';
import { Box3D, BoundingBoxCalculator3D } from './geometry/box3d.js';
import { Coord2D, CoordIsEqual2D, AddCoord2D, SubCoord2D, CoordDistance2D, DotVector2D } from './geometry/coord2d.js';
import { Coord3D, CoordIsEqual3D, AddCoord3D, SubCoord3D, CoordDistance3D, DotVector3D, VectorAngle3D, CrossVector3D, VectorLength3D, ArrayToCoord3D } from './geometry/coord3d.js';
import { Coord4D } from './geometry/coord4d.js';
import { IsZero, IsLower, IsGreater, IsLowerOrEqual, IsGreaterOrEqual, IsEqual, IsEqualEps, IsPositive, IsNegative, Eps, BigEps, RadDeg, DegRad, Direction } from './geometry/geometry.js';
import { Segment2D, ProjectPointToSegment2D, SegmentPointDistance2D } from './geometry/line2d.js';
import { Matrix, MatrixIsEqual } from './geometry/matrix.js';
import { OctreeNode, Octree } from './geometry/octree.js';
import { Quaternion, QuaternionIsEqual, ArrayToQuaternion, QuaternionFromAxisAngle, QuaternionFromXYZ } from './geometry/quaternion.js';
import { Transformation, TransformationIsEqual } from './geometry/transformation.js';
import { BezierTweenFunction, LinearTweenFunction, ParabolicTweenFunction, TweenCoord3D } from './geometry/tween.js';
import { ImportSettings, ImportError, ImportResult, ImporterFileAccessor, Importer, ImportErrorCode } from './import/importer.js';
import { Importer3dm } from './import/importer3dm.js';
import { Importer3ds } from './import/importer3ds.js';
import { ImporterBase } from './import/importerbase.js';
import { ImporterBim } from './import/importerbim.js';
import { ImporterFcstd } from './import/importerfcstd.js';
import { InputFile, ImporterFile, ImporterFileList, InputFilesFromUrls, InputFilesFromFileObjects } from './import/importerfiles.js';
import { ImporterGltf } from './import/importergltf.js';
import { ImporterIfc } from './import/importerifc.js';
import { ImporterObj } from './import/importerobj.js';
import { ImporterOcct } from './import/importerocct.js';
import { ImporterOff } from './import/importeroff.js';
import { ImporterPly } from './import/importerply.js';
import { ImporterStl } from './import/importerstl.js';
import { ImporterThreeSvg } from './import/importersvg.js';
import { ImporterThreeBase, ImporterThreeFbx, ImporterThreeDae, ImporterThreeWrl, ImporterThree3mf, ImporterThreeAmf } from './import/importerthree.js';
import { ColorToMaterialConverter, NameFromLine, ParametersFromLine, ReadLines, IsPowerOfTwo, NextPowerOfTwo, UpdateMaterialTransparency, CreateOcctWorker, LoadExternalLibrary } from './import/importerutils.js';
import { BinaryReader } from './io/binaryreader.js';
import { BinaryWriter } from './io/binarywriter.js';
import { ArrayBufferToUtf8String, ArrayBufferToAsciiString, AsciiStringToArrayBuffer, Utf8StringToArrayBuffer, Base64DataURIToArrayBuffer, GetFileExtensionFromMimeType, CreateObjectUrl, CreateObjectUrlWithMimeType, RevokeObjectUrl } from './io/bufferutils.js';
import { LoadExternalLibraryFromUrl } from './io/externallibs.js';
import { GetFileName, GetFileExtension, RequestUrl, ReadFile, TransformFileHostUrls, IsUrl, FileSource, FileFormat } from './io/fileutils.js';
import { TextWriter } from './io/textwriter.js';
import { RGBColor, RGBAColor, ColorComponentFromFloat, ColorComponentToFloat, RGBColorFromFloatComponents, SRGBToLinear, LinearToSRGB, IntegerToHexString, RGBColorToHexString, RGBAColorToHexString, HexStringToRGBColor, HexStringToRGBAColor, ArrayToRGBColor, RGBColorIsEqual } from './model/color.js';
import { GeneratorParams, Generator, GeneratorHelper, GenerateCuboid, GenerateCone, GenerateCylinder, GenerateSphere, GeneratePlatonicSolid } from './model/generator.js';
import { Line } from './model/line.js';
import { TextureMap, MaterialBase, FaceMaterial, PhongMaterial, PhysicalMaterial, TextureMapIsEqual, TextureIsEqual, MaterialType, MaterialSource } from './model/material.js';
import { Mesh } from './model/mesh.js';
import { MeshPrimitiveBuffer, MeshBuffer, ConvertMeshToMeshBuffer } from './model/meshbuffer.js';
import { MeshInstanceId, MeshInstance } from './model/meshinstance.js';
import { IsEmptyMesh, CalculateTriangleNormal, TransformMesh, FlipMeshTrianglesOrientation } from './model/meshutils.js';
import { Model } from './model/model.js';
import { FinalizeModel, CheckModel } from './model/modelfinalization.js';
import { IsModelEmpty, GetBoundingBox, GetTopology, IsTwoManifold, GetDefaultMaterials, ReplaceDefaultMaterialsColor } from './model/modelutils.js';
import { Node } from './model/node.js';
import { Object3D, ModelObject3D } from './model/object.js';
import { Property, PropertyGroup, PropertyToString, PropertyType } from './model/property.js';
import { GetTriangleArea, GetTetrahedronSignedVolume, CalculateVolume, CalculateSurfaceArea } from './model/quantities.js';
import { TopologyVertex, TopologyEdge, TopologyTriangleEdge, TopologyTriangle, Topology } from './model/topology.js';
import { Triangle } from './model/triangle.js';
import { Unit } from './model/unit.js';
import { ParameterListBuilder, ParameterListParser, CreateUrlBuilder, CreateUrlParser, CreateModelUrlParameters, ParameterConverter } from './parameters/parameterlist.js';
import { ModelToThreeConversionParams, ModelToThreeConversionOutput, ThreeConversionStateHandler, ThreeNodeTree, ThreeMaterialHandler, ThreeMeshMaterialHandler, ConvertModelToThreeObject, MaterialGeometryType } from './threejs/threeconverter.js';
import { ThreeModelLoader } from './threejs/threemodelloader.js';
import { ThreeColorConverter, ThreeLinearToSRGBColorConverter, ThreeSRGBToLinearColorConverter, HasHighpDriverIssue, GetShadingType, ConvertThreeColorToColor, ConvertColorToThreeColor, ConvertThreeGeometryToMesh, CreateHighlightMaterial, CreateHighlightMaterials, DisposeThreeObjects, GetLineSegmentsProjectedDistance, ShadingType } from './threejs/threeutils.js';
import { Camera, CameraIsEqual3D, NavigationMode, ProjectionMode } from './viewer/camera.js';
import { GetIntegerFromStyle, GetDomElementExternalWidth, GetDomElementExternalHeight, GetDomElementInnerDimensions, GetDomElementClientCoordinates, CreateDomElement, AddDomElement, AddDiv, ClearDomElement, InsertDomElementBefore, InsertDomElementAfter, ShowDomElement, IsDomElementVisible, SetDomElementWidth, SetDomElementHeight, GetDomElementOuterWidth, GetDomElementOuterHeight, SetDomElementOuterWidth, SetDomElementOuterHeight, CreateDiv } from './viewer/domutils.js';
import { EmbeddedViewer, Init3DViewerFromUrlList, Init3DViewerFromFileList, Init3DViewerElements } from './viewer/embeddedviewer.js';
import { MouseInteraction, TouchInteraction, ClickDetector, Navigation, NavigationType } from './viewer/navigation.js';
import { EnvironmentSettings, ShadingModel } from './viewer/shadingmodel.js';
import { CameraValidator, UpVector, Viewer, GetDefaultCamera, TraverseThreeObject, GetShadingTypeOfObject } from './viewer/viewer.js';
import { ViewerModel, EdgeSettings, ViewerMainModel, SetThreeMeshPolygonOffset, IntersectionMode } from './viewer/viewermodel.js';

export {
    IsDefined,
    ValueOrDefault,
    CopyObjectAttributes,
    IsObjectEmpty,
    FormatString,
    EscapeHtmlChars,
    EventNotifier,
    SetLocalizedStrings,
    SetLanguageCode,
    Loc,
    FLoc,
    TaskRunner,
    RunTaskAsync,
    RunTasks,
    RunTasksBatch,
    WaitWhile,
    Exporter,
    Exporter3dm,
    ExportedFile,
    ExporterBase,
    ExporterBim,
    ExporterGltf,
    ExporterSettings,
    ExporterModel,
    ExporterObj,
    ExporterOff,
    ExporterPly,
    ExporterStl,
    Box3D,
    BoundingBoxCalculator3D,
    Coord2D,
    CoordIsEqual2D,
    AddCoord2D,
    SubCoord2D,
    CoordDistance2D,
    DotVector2D,
    Coord3D,
    CoordIsEqual3D,
    AddCoord3D,
    SubCoord3D,
    CoordDistance3D,
    DotVector3D,
    VectorAngle3D,
    CrossVector3D,
    VectorLength3D,
    ArrayToCoord3D,
    Coord4D,
    IsZero,
    IsLower,
    IsGreater,
    IsLowerOrEqual,
    IsGreaterOrEqual,
    IsEqual,
    IsEqualEps,
    IsPositive,
    IsNegative,
    Eps,
    BigEps,
    RadDeg,
    DegRad,
    Direction,
    Segment2D,
    ProjectPointToSegment2D,
    SegmentPointDistance2D,
    Matrix,
    MatrixIsEqual,
    OctreeNode,
    Octree,
    Quaternion,
    QuaternionIsEqual,
    ArrayToQuaternion,
    QuaternionFromAxisAngle,
    QuaternionFromXYZ,
    Transformation,
    TransformationIsEqual,
    BezierTweenFunction,
    LinearTweenFunction,
    ParabolicTweenFunction,
    TweenCoord3D,
    ImportSettings,
    ImportError,
    ImportResult,
    ImporterFileAccessor,
    Importer,
    ImportErrorCode,
    Importer3dm,
    Importer3ds,
    ImporterBase,
    ImporterBim,
    ImporterFcstd,
    InputFile,
    ImporterFile,
    ImporterFileList,
    InputFilesFromUrls,
    InputFilesFromFileObjects,
    ImporterGltf,
    ImporterIfc,
    ImporterObj,
    ImporterOcct,
    ImporterOff,
    ImporterPly,
    ImporterStl,
    ImporterThreeSvg,
    ImporterThreeBase,
    ImporterThreeFbx,
    ImporterThreeDae,
    ImporterThreeWrl,
    ImporterThree3mf,
    ImporterThreeAmf,
    ColorToMaterialConverter,
    NameFromLine,
    ParametersFromLine,
    ReadLines,
    IsPowerOfTwo,
    NextPowerOfTwo,
    UpdateMaterialTransparency,
    CreateOcctWorker,
    LoadExternalLibrary,
    BinaryReader,
    BinaryWriter,
    ArrayBufferToUtf8String,
    ArrayBufferToAsciiString,
    AsciiStringToArrayBuffer,
    Utf8StringToArrayBuffer,
    Base64DataURIToArrayBuffer,
    GetFileExtensionFromMimeType,
    CreateObjectUrl,
    CreateObjectUrlWithMimeType,
    RevokeObjectUrl,
    LoadExternalLibraryFromUrl,
    GetFileName,
    GetFileExtension,
    RequestUrl,
    ReadFile,
    TransformFileHostUrls,
    IsUrl,
    FileSource,
    FileFormat,
    TextWriter,
    RGBColor,
    RGBAColor,
    ColorComponentFromFloat,
    ColorComponentToFloat,
    RGBColorFromFloatComponents,
    SRGBToLinear,
    LinearToSRGB,
    IntegerToHexString,
    RGBColorToHexString,
    RGBAColorToHexString,
    HexStringToRGBColor,
    HexStringToRGBAColor,
    ArrayToRGBColor,
    RGBColorIsEqual,
    GeneratorParams,
    Generator,
    GeneratorHelper,
    GenerateCuboid,
    GenerateCone,
    GenerateCylinder,
    GenerateSphere,
    GeneratePlatonicSolid,
    Line,
    TextureMap,
    MaterialBase,
    FaceMaterial,
    PhongMaterial,
    PhysicalMaterial,
    TextureMapIsEqual,
    TextureIsEqual,
    MaterialType,
    MaterialSource,
    Mesh,
    MeshPrimitiveBuffer,
    MeshBuffer,
    ConvertMeshToMeshBuffer,
    MeshInstanceId,
    MeshInstance,
    IsEmptyMesh,
    CalculateTriangleNormal,
    TransformMesh,
    FlipMeshTrianglesOrientation,
    Model,
    FinalizeModel,
    CheckModel,
    IsModelEmpty,
    GetBoundingBox,
    GetTopology,
    IsTwoManifold,
    GetDefaultMaterials,
    ReplaceDefaultMaterialsColor,
    Node,
    Object3D,
    ModelObject3D,
    Property,
    PropertyGroup,
    PropertyToString,
    PropertyType,
    GetTriangleArea,
    GetTetrahedronSignedVolume,
    CalculateVolume,
    CalculateSurfaceArea,
    TopologyVertex,
    TopologyEdge,
    TopologyTriangleEdge,
    TopologyTriangle,
    Topology,
    Triangle,
    Unit,
    ParameterListBuilder,
    ParameterListParser,
    CreateUrlBuilder,
    CreateUrlParser,
    CreateModelUrlParameters,
    ParameterConverter,
    ModelToThreeConversionParams,
    ModelToThreeConversionOutput,
    ThreeConversionStateHandler,
    ThreeNodeTree,
    ThreeMaterialHandler,
    ThreeMeshMaterialHandler,
    ConvertModelToThreeObject,
    MaterialGeometryType,
    ThreeModelLoader,
    ThreeColorConverter,
    ThreeLinearToSRGBColorConverter,
    ThreeSRGBToLinearColorConverter,
    HasHighpDriverIssue,
    GetShadingType,
    ConvertThreeColorToColor,
    ConvertColorToThreeColor,
    ConvertThreeGeometryToMesh,
    CreateHighlightMaterial,
    CreateHighlightMaterials,
    DisposeThreeObjects,
    GetLineSegmentsProjectedDistance,
    ShadingType,
    Camera,
    CameraIsEqual3D,
    NavigationMode,
    ProjectionMode,
    GetIntegerFromStyle,
    GetDomElementExternalWidth,
    GetDomElementExternalHeight,
    GetDomElementInnerDimensions,
    GetDomElementClientCoordinates,
    CreateDomElement,
    AddDomElement,
    AddDiv,
    ClearDomElement,
    InsertDomElementBefore,
    InsertDomElementAfter,
    ShowDomElement,
    IsDomElementVisible,
    SetDomElementWidth,
    SetDomElementHeight,
    GetDomElementOuterWidth,
    GetDomElementOuterHeight,
    SetDomElementOuterWidth,
    SetDomElementOuterHeight,
    CreateDiv,
    EmbeddedViewer,
    Init3DViewerFromUrlList,
    Init3DViewerFromFileList,
    Init3DViewerElements,
    MouseInteraction,
    TouchInteraction,
    ClickDetector,
    Navigation,
    NavigationType,
    EnvironmentSettings,
    ShadingModel,
    CameraValidator,
    UpVector,
    Viewer,
    GetDefaultCamera,
    TraverseThreeObject,
    GetShadingTypeOfObject,
    ViewerModel,
    EdgeSettings,
    ViewerMainModel,
    SetThreeMeshPolygonOffset,
    IntersectionMode
};
