
import { FULite } from '../../../packages/sdk/fu_lite';
import { FUPCMPlayer } from '../../../packages/sdk/fu_audio_player/pcm_player';

export class Talk {
    isInited: boolean
    isTextStamp
    audioPlayer: FUPCMPlayer
    expressParser: FULite
    locateFile: Function
    constructor(options) {
        this.isInited = false
        this.audioPlayer = new FUPCMPlayer(Object.assign({}, options.PCMConfig, { manul: options.manul }));
        this.locateFile = options.locateFile ? options.locateFile : (path) => path
        this.expressParser = new FULite({
            locateFile: this.locateFile,
        });
    }

    async initialize() {
        if (this.isInited) return Promise.resolve()
        const decoderUrl = this.locateFile('data_decoder.bin');
        const BsConfig = this.locateFile('defaultBSConfig.bin');
        await this.expressParser.initialize({ decoderUrl, BsConfig });
        this.isInited = true
    }

    feedPCM(typedArray) {
        this.audioPlayer.feed(typedArray);
    }
    feedPhenome(timestamp, isTextStamp) {
        this.isTextStamp = isTextStamp
        this.expressParser.feed(timestamp);
    }
    getCurrentExpression(flag) {
        if (flag < 0) {
            // 空白表情
            return this.expressParser.blankExpression;
        }
        const time = Math.round(this.audioPlayer.getTimestamp() * 1000) / 1000;
        console.log('time', time)
        return this.expressParser.getExpressionByTime(time, this.isTextStamp);
    }
    stop() {
        this.audioPlayer.interrupt();
        this.expressParser.interrupt();
    }
    registerOnEnd(cb) {
        this.audioPlayer.setOnEnd(cb);
    }
    setRequestStop() {
        this.audioPlayer.setRequestStop();
    }
}
