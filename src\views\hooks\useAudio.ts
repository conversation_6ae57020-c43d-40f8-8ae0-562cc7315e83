import { ref, watch } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';

let audio =  new Audio();

export default function useAudio() {
  const audioRef = ref(audio);
  const isPlaying = ref(false);
  const currentBgm = ref('')
  watch(
    () => audioRef.value.currentSrc,
    (n) => {
      if (!audioRef?.value?.paused) {
        audioRef.value.pause();
        isPlaying.value = false;
      }
    }
  );
  const play = () => {
    if (!audioRef.value.paused) {
      audioRef.value.pause();
      isPlaying.value = false;
    }
    audioRef.value.play();
    isPlaying.value = true;
  };

  const pause = () => {
    audioRef.value.pause();
    isPlaying.value = false;
    currentBgm.value = ''
  };

  audioRef.value.addEventListener('ended', () => {
    isPlaying.value = false;
  });

  const setSrc = (src, { autoPlay = false } = {}) => {
    audioRef.value.src = src;
    currentBgm.value = src
    if (src) {
      if (autoPlay) {
        audioRef.value.play();
        isPlaying.value = true;
      }
    }
  };

  onBeforeRouteLeave(() => {
    pause()
  })

  return {
    play,
    pause,
    isPlaying,
    setSrc,
    audioRef,
    currentBgm,
  };
}
