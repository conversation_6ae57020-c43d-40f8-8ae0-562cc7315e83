<template>
  <el-upload class="upload-demo" drag :on-success="handleSuccess" :before-upload="beforeUpload"
    :http-request="handleRequest">
    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
    <div class="el-upload__text">
      点击或拖拽文件 上传
    </div>
    <template #tip>
      <div class="el-upload__tip">
        建议尺寸:640*320,小于4MM的jpg、jpeg、png格式
      </div>
    </template>
  </el-upload>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import { ElMessage } from 'element-plus'
import type { UploadProps } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
export default defineComponent({
  components: {
    UploadFilled
  },
  emits: [
    'handleSuccess',
  ],
  // props: {
  // },
  setup(props, { emit }) {
    const handleSuccess: UploadProps['onSuccess'] = (
      response,
      uploadFile
    ) => {
      let imgUrl = URL.createObjectURL(uploadFile.raw!)
      emit('handleSuccess', imgUrl);
    }

    const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
      if (rawFile.type !== 'image/jpeg') {
        ElMessage.error('请上传jpg、jpeg、png格式')
        return false
      } else if (rawFile.size / 1024 / 1024 > 4) {
        ElMessage.error('上传内容不能超过4M')
        return false
      }
      return true
    }
    const handleRequest = () => {
    }
    return {
      handleSuccess,
      beforeUpload,
      handleRequest
    }
  }
})
</script>
