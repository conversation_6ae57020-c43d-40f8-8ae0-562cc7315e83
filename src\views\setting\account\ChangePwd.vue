<template>
  <wrap-form title="更改密码" submit-text="确定" @submit="handleSubmit" class="update-pwd">
    <el-form
      ref="changePwdFormRef"
      label-placement="left"
      size="large"
      :model="formInline"
      :rules="rules"
    >
      <el-form-item prop="image_code">
        <div class="flex flex-1 items-center">
          <el-input
            @input="(value) => handleInput('image_code', value, 'number')"
            autocomplete="off"
            :readonly="readOnly"
            @click="readOnly = false"
            class="flex-1"
            v-model="formInline.image_code"
            maxlength="4"
            placeholder="请输入图形验证码"
          />
          <div class="w-28 ml-2" @click="handleGetCaptch">
            <img class="w-full" :src="captchaRef" alt="图片验证码" />
          </div>
        </div>
      </el-form-item>
      <el-form-item class="flex-1" prop="msg_code">
        <div class="flex flex-1 flex-between">
          <el-input
            class="flex-1"
            @input="(value) => handleInput('msg_code', value, 'number')"
            v-model="formInline.msg_code"
            maxlength="6"
            placeholder="请输入手机验证码"
          />
          <el-button
            :disabled="time > 0"
            tertiary
            type="info"
            class="w-28 ml-2 bg-btn-bg-color border-none text-xx-blue"
            @click="handleGetMsgCode"
          >
            {{ time > 0 ? `${time}s后获取` : '获取验证码' }}
          </el-button>
        </div>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          maxlength="16"
          @input="(value) => handleInput('password', value, 'no-zh-char')"
          v-model="formInline.password"
          type="password"
          :show-password="true"
          placeholder="新密码"
        />
      </el-form-item>
      <el-form-item prop="password_confirm">
        <el-input
          maxlength="16"
          @input="(value) => handleInput('password_confirm', value, 'no-zh-char')"
          v-model="formInline.password_confirm"
          type="password"
          :show-password="true"
          placeholder="确认密码"
        />
      </el-form-item>
    </el-form>
  </wrap-form>
</template>

<script lang="ts" setup>
  import { changePassword } from '@/api/system/user';
  import WrapForm from '@/components/wrap-form';
  import { useUserStore } from '@/store/modules/user';
  import {
    useMsgCode,
    useCountTime,
    useEncrypt,
    useFilterInputHander,
  } from '@/views/hooks';
  import { validateImgCode, validateSmsCode } from '@/utils/validator';
  import { useDebounceFn, useTimeoutFn } from '@vueuse/core';
  import { ElMessage } from 'element-plus';
  import { storeToRefs } from 'pinia';
  import { onMounted, reactive, ref, toRaw } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const userStore = useUserStore();
  const { info } = storeToRefs(userStore);
  const { start, time } = useCountTime();
  const encrypt = useEncrypt();
  const changePwdFormRef = ref();
  const readOnly = ref(true);

  const { captchaRef, fetchCaptch, getMsgCodeAction } = useMsgCode(changePwdFormRef);

  const formInline = reactive({
    password: '',
    password_confirm: '',
    msg_code: '',
    image_code: '',
  });

  const handleInput = useFilterInputHander(formInline);

  const validatePassSame = (_rule, value, callback) => {
    if (value == '') {
      return callback(new Error('请输入确认密码'));
    } else if (value !== formInline.password) {
      return callback(new Error('确认密码与密码不一致,请重新输入!'));
    } else {
      callback();
    }
  };

  const rules = {
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      {
        min: 8,
        message: '密码长度至少为8个字符',
        trigger: 'blur',
      },
      {
        max: 16,
        message: '密码长度最长16个字符',
        trigger: 'blur',
      },
      {
        pattern: /(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9]).{6,30}/,
        message: '密码必须包含大写字母、小写字母、数字和特殊字符',
        trigger: 'blur',
      },
    ],
    password_confirm: [
      { required: true, message: '请再次输入密码', trigger: 'blur' },
      {
        min: 8,
        message: '密码长度至少为8个字符',
        trigger: 'blur',
      },
      {
        max: 16,
        message: '密码长度最长16个字符',
        trigger: 'blur',
      },
      {
        pattern: /(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9]).{6,30}/,
        message: '密码必须包含大写字母、小写字母、数字和特殊字符',
        trigger: 'blur',
      },
      {
        validator: validatePassSame,
        trigger: 'blur',
      },
    ],
    image_code: [
      { required: true, message: '请输入图形验证码', trigger: 'blur' },
      {
        validator: validateImgCode,
        trigger: 'blur',
      },
    ],
    msg_code: [
      { required: true, message: '请输入手机验证码', trigger: 'blur' },
      {
        validator: validateSmsCode,
        trigger: 'blur',
      },
    ],
  };

  const handleGetMsgCode = async () => {
    try {
      const { phone } = toRaw(info.value);
      const { image_code } = formInline;
      await getMsgCodeAction({ image_code, phone, type: 4 });
      start(60);
    } catch (e) {console.log(e)}
  };

  const handleSubmit = useDebounceFn(async () => {
    try {
      await changePwdFormRef.value.validate();
      const params = toRaw(formInline);
      const { code } = await changePassword(encrypt(params));
      if (code === 0) {
        ElMessage({
          message: '密码修改成功！请重新登陆～',
          type: 'success',
        });
        await userStore.logout();
        useTimeoutFn(() => {
          router.replace('/login');
        }, 1000);
      }
    } catch (e) {console.log(e)}
  }, 500);

  const handleGetCaptch = () => {
    formInline.msg_code = '';
    fetchCaptch();
  };

  onMounted(() => {
    handleGetCaptch();
  });
</script>
