<template>
  <el-card class="box-card relative">
    <el-button class="top5 absolute right-6 cursor-pointer z-10" @click="openTop5Model"
      >访问TOP5</el-button
    >
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClickTab">
      <el-tab-pane label="安全日志" :name="1" />
      <el-tab-pane label="用户行为统计" :name="2" />
    </el-tabs>
    <component :is="activeName == 1 ? SecurityLog : UserBehavior" />
    <el-dialog v-model="pageRankingVisiable" width="60%" align-center>
      <div ref="barChart" class="flex justify-between h-96"> </div>
      <template #action></template>
    </el-dialog>
  </el-card>
</template>

<script lang="ts" setup>
  import { ref, Ref } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import SecurityLog from './security-log.vue';
  import UserBehavior from './user-behavior.vue';
  import { getPageRanking } from '@/api/system/log';

  const pageRankingVisiable = ref(false);
  const barChart = ref<HTMLDivElement | null>(null);
  const { setOptions: setBarChartOptions } = useECharts(barChart as Ref<HTMLDivElement>);

  const activeName = ref(1);
  const barChartOption = ref<any>({
    xAxis: {
      type: 'category',
      data: [],
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#959596',
        align: 'center',
        overflow: 'truncate',
        formatter: (val) => {
          let result = '';
          const len = val.length;
          const limit = 6;
          if (len > limit) {
            for (let i = 0; i < len; i += limit) {
              result += val.substring(i, i + limit) + '\n';
            }
            result = result.trim();
          } else {
            result = val;
          }
          return result;
        },
      },
      axisLine: {
        lineStyle: {
          color: '#E5E5E5',
        },
      },
    },
    yAxis: {
      type: 'value',
      name: '访问次数',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      formatter: '访问次数: {c0}<br />页面: {b0}',
    },
    series: [
      {
        data: [],
        type: 'bar',
        itemStyle: {
          color: '#407EFF',
        },
      },
    ],
  });

  const fetchPageRanking = async () => {
    const params = {};
    const {
      data: { rows = [] },
    } = await getPageRanking(params);
    const dates: string[] = [];
    const totals: number[] = [];
    rows.forEach(({ times, pageName }) => {
      dates.push(pageName);
      totals.push(times);
    });
    barChartOption.value.xAxis.data = dates;
    barChartOption.value.series[0].data = totals;
    setBarChartOptions(barChartOption.value, true);
  };

  const openTop5Model = async () => {
    pageRankingVisiable.value = true;
    await fetchPageRanking();
  };
  const handleClickTab = (tab) => {
    activeName.value = tab.props.name;
  };
</script>
