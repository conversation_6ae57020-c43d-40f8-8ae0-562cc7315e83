<template>
  <el-card class="n-layout-page-header">
    <el-form :model="form" label-width="80px" autocomplete="off" ref="formRef" :rules="rules">
      <el-form-item label="账户名称" required prop="username">
        <el-input
          autocomplete="off"
          :readonly="readOnly"
          @click="readOnly = false"
          v-model="form.username"
          maxlength="16"
          @input="(value) => handleInput('username', value, 'no-zh-char')"
        />
      </el-form-item>
      <el-form-item label="登录账号" required prop="phone">
        <el-input
          autocomplete="off"
          :readonly="readOnly"
          @click="readOnly = false"
          v-model="form.phone"
          :disabled="!!id"
          maxlength="11"
          @input="(value) => handleInput('phone', value, 'number')"
        />
      </el-form-item>
      <el-form-item label="密码" :required="required" prop="password">
        <el-input
          autocomplete="off"
          type="password"
          :show-password="true"
          placeholder="请输入密码"
          v-model="form.password"
          maxlength="16"
          :readonly="readOnly"
          @click="readOnly = false"
          @input="(value) => handleInput('password', value, 'no-zh-char')"
        />
      </el-form-item>
      <el-form-item label="确认密码" :required="required" prop="password_confirm">
        <el-input
          type="password"
          :show-password="true"
          placeholder="请输入确认密码"
          v-model="form.password_confirm"
          maxlength="16"
          @input="(value) => handleInput('password_confirm', value, 'no-zh-char')"
        />
      </el-form-item>
      <el-form-item label="角色" required prop="role_id">
        <el-select v-model="form.role_id">
          <el-option
            v-for="item in roleOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户头像">
        <CustomUpload
          v-model="iconInfo"
          scope="asset_icon"
          :show-file-list="false"
          list-type="picture-card"
          accept=".jpeg,.png,.jpg"
          :maxSize="4"
        >
          <template v-if="iconInfo.url">
            <img alt="图片" :src="iconInfo.url" class="avatar" />
          </template>
          <template v-else>
            <el-icon size="26"><Plus /></el-icon>
            <div class="el-upload__text"> 点击或拖拽文件上传 </div>
          </template>
          <template #tip>
            <div class="el-upload__tip"> 建议尺寸：640*320，小于4M的jpg、jpeg、png格式 </div>
          </template>
        </CustomUpload>
      </el-form-item>
      <el-form-item label="微信" prop="weixin">
        <el-input
          v-model="form.weixin"
          maxlength="20"
          @input="(value) => handleInput('weixin', value)"
        />
      </el-form-item>
      <el-form-item label="QQ" prop="qq">
        <el-input
          v-model="form.qq"
          maxlength="12"
          @input="(value) => handleInput('qq', value, 'number')"
        />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email" maxlength="100" />
      </el-form-item>
      <el-form-item label="账号介绍">
        <el-input v-model="form.introduction" type="textarea" maxlength="100" 
          @input="(value) => handleInput('introduction', value)"
        />
      </el-form-item>
    </el-form>
    <el-button @click="submit">提交</el-button>
    <el-button @click="goback">返回</el-button>
  </el-card>
</template>

<script lang="ts" setup>
  import { reactive, computed, ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage, FormInstance } from 'element-plus';
  import { Plus } from '@element-plus/icons-vue';
  import { getRoleList } from '@/api/system/role';
  import { getUserDetail, editUser, addUser } from '@/api/system/user';
  import { useEncrypt, useFilterInputHander } from '@/views/hooks';
  import CustomUpload from '@/views/operator/components/CustomUpload.vue';
import { validatePhone } from '@/utils/validator';
  const encrypt = useEncrypt();
  const router = useRouter();
  const route = useRoute();
  const { id } = route.query;
  const required = computed(() => !route?.query?.id);
  const readOnly = ref(true);
  const formRef = ref<FormInstance>();
  const form = reactive({
    name: '',
    phone: '',
    username: '',
    role_id: '',
    password: '',
    password_confirm: '',
    weixin: '',
    qq: '',
    email: '',
    introduction: '',
  });

  const handleInput = useFilterInputHander(form);

  const validatePassSame = (_rule, value, callback) => {
    if (value == '' && _rule.required) {
      return callback(new Error('请输入确认密码'));
    } else if (value !== form.password) {
      return callback(new Error('确认密码与密码不一致,请重新输入!'));
    } else {
      callback();
    }
  };

  const rules = {
    username: [
      {
        required: true,
        message: '请输入用户名',
        trigger: 'blur',
      },
      {
        min: 8,
        message: '用户名长度至少为8个字符',
        trigger: 'blur',
      },
      {
        pattern: /^(?=.*[a-zA-Z])[a-zA-Z0-9_]+$/,
        message: '用户名必须由字母、数字和下划线组成（字母必填）',
        trigger: 'blur',
      },
    ],
    phone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
    password: [
      {
        required: required.value,
        message: '请输入密码',
        trigger: 'blur',
      },
      {
        min: 8,
        message: '密码长度至少为8个字符',
        trigger: 'blur',
      },
      {
        max: 16,
        message: '密码长度最长16个字符',
        trigger: 'blur',
      },
      {
        pattern: /(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9]).{6,30}/,
        message: '密码必须包含大写字母、小写字母、数字和特殊字符',
        trigger: 'blur',
      },
    ],
    password_confirm: [
      {
        required: required.value,
        message: '请输入确认密码',
        trigger: ['input', 'blur'],
      },
      {
        min: 8,
        message: '密码长度至少为8个字符',
        trigger: 'blur',
      },
      {
        max: 16,
        message: '密码长度最长16个字符',
        trigger: 'blur',
      },
      {
        pattern: /(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9]).{6,30}/,
        message: '密码必须包含大写字母、小写字母、数字和特殊字符',
        trigger: 'blur',
      },
      {
        validator: validatePassSame,
        trigger: 'blur',
      },
    ],
    role_id: [{ required: true, message: '请选择角色', trigger: 'blur' }],
    email: [
      {
        validator: (_, value, callback) => {
          if (value?.length === 0 || value === null || value === undefined) {
            callback();
          } else if (!/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
            callback(new Error('请输入正确的邮箱格式'));
          } else {
            callback();
          }
        },
      },
    ],
    weixin: [
      {
        min: 6,
        message: '微信至少为6个字符',
        trigger: 'blur',
      },
    ],
    qq: [
      {
        min: 5,
        message: 'qq至少为5个字符',
        trigger: 'blur',
      },
    ]
  };

  const iconInfo = ref<any>({
    url: '',
    id: '',
  });
  onMounted(async () => {
    await queryRoleList();
    if (id) {
      const res = await getUserDetail(id);
      for (let k of ['name', 'phone', 'username', 'weixin', 'qq', 'email', 'introduction']) {
        form[k] = res.data[k];
      }
      form.role_id = res.data.roles?.[0]?.id;
      iconInfo.value.url = res.data.avatar_url;
    }
  });

  const roleOptions = ref<any[]>([]);
  async function queryRoleList() {
    const res = await getRoleList({
      // status: 1, 过滤角色状态
      page: 1,
      limit: 999,
    });
    roleOptions.value = res.data.rows;
  }

  async function submit() {
    if (!formRef.value) return;
    formRef.value.validate(async (valid) => {
      if (valid) {
        const { password, password_confirm, ...other } = form;
        const params: any = {
          ...other,
          name: form.username,
          avatar_id: iconInfo.value.id,
        };

        if (id) {
          // 编辑时换成新字段
          params.new_password = password;
          params.new_password_confirm = password_confirm;
          await editUser(id, encrypt(params));
          ElMessage({
            message: '修改成功！',
            type: 'success',
          });
        } else {
          params.password = password;
          params.password_confirm = password_confirm;
          await addUser(encrypt(params));
          ElMessage({
            message: '新增成功！',
            type: 'success',
          });
        }
        setTimeout(() => {
          goback();
        }, 1000);
      }
    });
  }

  function goback() {
    router.push({ path: '/user' });
  }
</script>

<style lang="less" scoped>
  .upload-demo {
    :deep(.el-upload--picture-card) {
      --el-upload-picture-card-size: unset;
      .el-upload-dragger {
        height: 100%;
        .avatar {
          max-width: 250px;
          max-height: 250px;
        }
      }
    }
    :deep(.el-upload-list--picture-card) {
      min-height: 156px;
      .el-upload--picture-card:nth-child(2) {
        display: none;
      }
    }
  }
</style>
