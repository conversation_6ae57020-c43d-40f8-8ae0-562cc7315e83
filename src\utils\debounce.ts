function debounce(func, wait, immediate?: () => void) {
    let timeout
    return function (...e) {
        if (typeof immediate === 'function') {
            immediate()
        }
        // @ts-ignore
        const context = this as unknown
        const args = Array.from(arguments)
        if (timeout) clearTimeout(timeout)
        timeout = setTimeout(() => {
            func.apply(context, args)
        }, wait)
    }
}

export default debounce