import { http } from '@/utils/http/axios';

/**
 * @description: 角色列表
 */
export function getPermissionList(params?) {
  return http.request({
    url: '/business/permission',
    method: 'GET',
    params,
  });
}

// 新增角色
export function addPermission(params) {
  return http.request({
    url: '/business/permission',
    method: 'POST',
    params,
  });
}

// 获取角色详情
export function getPermissionDetail(id) {
  return http.request({
    url: `/business/permission/${id}`,
    method: 'GET',
  });
}

// 编辑角色
export function editPermission(id, params) {
  return http.request({
    url: `/business/permission/${id}`,
    method: 'POST',
    params,
  });
}

// 删除角色
export function deletePermission(id) {
  return http.request({
    url: `/business/permission/${id}/delete`,
    method: 'POST',
  });
}

// 修改角色状态
export function changePermissionStatus(id, params) {
  return http.request({
    url: `/business/permission/${id}/status`,
    method: 'POST',
    params,
  });
}
