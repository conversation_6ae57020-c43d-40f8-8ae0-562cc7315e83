<!DOCTYPE html>
<html>

<head>
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<meta name="viewport" content="width=device-width, user-scalable=no">

	<title>Online 3D Viewer</title>

	<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/three@0.130.0/build/three.min.js"></script>
	<script type='text/javascript'>
		function HasHighpDriverIssue ()
		{
			let canvas = document.createElement ('canvas');
			document.body.appendChild (canvas);
			let parameters = {
				canvas : canvas,
				antialias : true
			};

			let renderer = new THREE.WebGLRenderer (parameters);
			renderer.setClearColor ('#ffffff', 1);
			renderer.setSize (10, 10);

			let scene = new THREE.Scene ();

			let ambientLight = new THREE.AmbientLight (0x888888);
			scene.add (ambientLight);

			let light = new THREE.DirectionalLight (0x888888);
			light.position.set (0.0, 0.0, 1.0);
			scene.add (light);

			let camera = new THREE.PerspectiveCamera (45.0, 1.0, 0.1, 1000.0);
			camera.position.set (0.0, 0.0, 1.0);
			camera.up.set (0.0, 1.0, 0.0);
			camera.lookAt (new THREE.Vector3 (0.0, 0.0, 0.0));
			scene.add (camera);

			let plane = new THREE.PlaneGeometry (1.0, 1.0);
			let mesh = new THREE.Mesh (plane, new THREE.MeshPhongMaterial ({
				color : 0xcc0000
			}));
			scene.add (mesh);
			renderer.render (scene, camera);

			let context = renderer.getContext ();
			var pixels = new Uint8Array (4);
			context.readPixels(
				5, 5, 1, 1,
				context.RGBA,
				context.UNSIGNED_BYTE,
				pixels
			);

			document.body.removeChild (canvas);
			if (pixels[0] < 50 && pixels[1] < 50 && pixels[2] < 50) {
				return true;
			}
			return false;
		}

		window.onload = function () {
			let hasIssue = HasHighpDriverIssue ();
			let resultDiv = document.createElement ('div');
			document.body.appendChild (resultDiv);
			resultDiv.innerHTML = hasIssue ? 'Invalid driver detected' : 'Everything is OK';
		};
	</script>
</head>

<body>

</body>

</html>
