@import 'transition/index.less';
.hidden-upload-btn {
    .el-upload--picture-card {
      display: none;
    }
  }

  .demo-partnerForm {
    margin-bottom: 110px;
  }
  .model {
    width: 100%;
    padding-left: 20px;
    .card-bgm-uploaded {
      display: flex;
      margin: 0 10px;
    }
    .bgm-card {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
  .right-section {
    div.file-name {
      padding-left: 15px;
      font-size: 14px;
      color: #262626;
      &:hover {
        color: #262626;
      }
    }
    .el-button {
      font-size: 14px;
      font-weight: 400;
      color: #3665ff;
      align-self: start;
      &.is-text:not(.is-disabled):hover {
        background-color: transparent;
      }
    }
  }

  .wrapper {
    background: #e5e7eb;
    margin: 0 20px;
    box-sizing: border-box;
  }

  .content-bottom {
    height: 100px;
    width: 100%;
    background: #fff;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 0;
  }
  .upload-demo {
    width: 100%;
    .el-upload--picture-card {
      --el-upload-picture-card-size: unset;
      .el-upload-dragger {
        border: none;
        height: 100%;
        .avatar {
          max-width: 250px;
          max-height: 250px;
        }
      }
    }
    .el-upload-list--picture-card {
      min-height: 156px;
      .el-upload--picture-card:nth-child(2) {
        display: none;
      }
    }
  }
  .avatar {
    max-width: 250px;
    max-height: 250px;
  }

  .logo-bar {
    background-color: #fff;
  }
  
  .cursor-move {
    cursor: move;
  }
  .n-base-close.n-dialog__close {
    background-color: #fff;
    &:hover {
      ::before {
        background-color: #fff;
      }
      background: none;
    }
  }
  .n-base-icon {
    background: #fff;
    border: none;
    &:hover,
    &:focus {
      background: #fff;
      border: none;
    }
  }
  .modal-table {
    .el-dialog__body {
      margin-top: 0;
      padding-top: 0;
    }
  }
  .editable-cell {
    &-content {
      position: relative;
      overflow-wrap: break-word;
      word-break: break-word;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      &-comp {
        flex: 1;
      }

      .edit-icon {
        font-size: 14px;
        //position: absolute;
        //top: 4px;
        //right: 0;
        display: none;
        width: 20px;
        cursor: pointer;
      }

      &:hover {
        .edit-icon {
          display: inline-block;
        }
      }
    }

    &-action {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .table-toolbar {
    &-inner-popover-title {
      padding: 3px 0;
    }

    &-right {
      &-icon {
        margin-left: 12px;
        font-size: 16px;
        color: var(--text-color);
        cursor: pointer;

        :hover {
          color: #1890ff;
        }
      }
    }
  }

  .table-toolbar-inner {
    &-checkbox {
      display: flex;
      align-items: center;
      padding: 10px 14px;

      &:hover {
        background: #e6f7ff;
      }

      .drag-icon {
        display: inline-flex;
        margin-right: 8px;
        cursor: move;
        &-hidden {
          visibility: hidden;
          cursor: default;
        }
      }

      .fixed-item {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-left: auto;
      }

      .ant-checkbox-wrapper {
        flex: 1;

        &:hover {
          color: #1890ff !important;
        }
      }
    }

    &-checkbox-dark {
      &:hover {
        background: hsla(0, 0%, 100%, 0.08);
      }
    }
  }

  .toolbar-popover {
    .n-popover__content {
      padding: 0;
    }
  }
  .layout-side-drawer {
    background-color: rgb(0, 20, 40);

    .layout-sider {
      min-height: 100vh;
      box-shadow: 2px 0 8px 0 rgb(29 35 41 / 5%);
      position: relative;
      z-index: 13;
      transition: all 0.2s ease-in-out;
    }
  }
  .hidden-upload-btn {
    .el-upload--picture-card {
      display: none;
    }
  }

  .upload-demo.bgm-upload {
    .el-upload-list--picture-card {
      min-height: 112px !important;
    }
    .card-bgm-uploaded {
      display: flex;
    }
    .right-section {
      div.file-name {
        padding-left: 15px;
        color: #262626;
        &:hover {
          color: #262626;
        }
      }
      .el-button {
        font-size: 14px;
        font-weight: 400;
        color: #3665ff;
        align-self: start;
        &.is-text:not(.is-disabled):hover {
          background-color: transparent;
        }
      }
    }
  }

  .box-card {
    // min-height: 740px;
  }

  .content {
    margin-bottom: 110px;
  }
  .el-upload-list--picture-card {
    // height: 112px;
  }

  .content-bottom {
    height: 100px;
    width: 100%;
    background: #fff;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 0;
  }
  .n-drawer .n-drawer-content .n-drawer-header {
    color: red;
    padding: 0 10px;
    .n-tab-pane {
      padding: 0;
    }
  }
  .modal-table {
    .el-dialog__body {
      margin-top: 0;
      padding-top: 0;
    }
  }
  #chart {
    width: 100%;
    height: 430px;
  }
  .xx-date-picker {
    margin-left: -200px;
  }
  .time {
    color: rgb(148 163 184);
  }
  .xx-date-picker {
    margin-left: -200px!important;
  }

  #chart {
    width: 100%;
    height: 430px;
  }
  .wrapper {
    background: #e5e7eb;
    box-sizing: border-box;
  }

  .box {
    box-sizing: border-box;
  }

  .item-box {
    background: rgb(248, 250, 252);
    width: 20.3%;
  }
  .hidden-upload-btn {
    .el-upload--picture-card {
      display: none;
    }
  }
  .main-view {
    position: relative;
  }

  .update-pwd {
    width: 560px;
    margin: auto;
    margin-top: 32px;

    .el-card__header {
      text-align: center;
      font-size: 24px;
      font-weight: 500;
      color: #222326;
      border-bottom: none;
    }
  }

  .code-img {
    background: rgb(241 245 249);
    color: rgb(37 99 235);
  }
  .main-view {
    position: relative;
  }

  .update-pwd {
    width: 560px;
    margin: auto;
    margin-top: 32px;

    .el-card__header {
      text-align: center;
      font-size: 24px;
      font-weight: 500;
      color: #222326;
      border-bottom: none;
    }
  }

  .code-img {
    background: rgb(241 245 249);
    color: rgb(37 99 235);
  }

  .echarts-tooltip-text {
    max-width: 350px;
    white-space: unset!important;
    p {
      margin: 0;
      width: 100%;
      word-break: break-all;
    }
  }