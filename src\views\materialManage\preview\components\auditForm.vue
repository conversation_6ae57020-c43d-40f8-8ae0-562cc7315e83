<template>
    <el-card class="box-card content">
      <h1 class="text-center">审核素材</h1>
      <div class="flex justify-center mt-10">
        <el-form class="w-6/12" ref="ruleFormRef" :model="detailForm" :rules="rules" label-width="120px" status-icon>
          <el-form-item label="审核意见：" prop="comment">
            <el-input
              v-model="detailForm.comment"
              type="textarea"
              placeholder="请描述"
              maxlength="100"
              rows="6"
              style="width: 100%"
              @input="(value) => handleInput('comment', value)"
            />
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <div class="content-bottom">
      <el-button @click="cancel">返回</el-button>
      <el-button type="primary" @click="handleSubmit">确认提交</el-button>
    </div>
  </template>
  <script lang="ts" setup>
  import {
    auditAssetPublish,
  } from '@/api/material/material';
  import {
    auditAvatarPublish,
  } from '@/api/material/avatar';
  import { FormInstance, FormRules } from 'element-plus';
  import { onMounted, reactive, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
import { useFilterInputHander } from '@/views/hooks';

  const route = useRoute()
  const router = useRouter()

  const ruleFormRef = ref<FormInstance>()
  const detailForm = reactive({
    username: '',
    phone: '',
    name: '',
    id_card_img: '',
    id_card_imgback: '',
    company_img: '',
    comment: ''
  })
  const handleInput = useFilterInputHander(detailForm);

  const rules = reactive<FormRules>({
    comment: [
      {
        required: true,
        message: '请输入审核意见',
        trigger: 'blur',
      },
    ],
  })

  onMounted(async () => {
  })

  const auditCpAction = async (id) => {
    const { comment } = detailForm;
    const params = {
      comment
    };
    const postFn = route.query.avatarId ? auditAvatarPublish : auditAssetPublish
    const backUrl = route.query.avatarId ? '/avatar/avatar-list' : '/material/material-list'
    const { code } = await postFn(id, params);
    if (code === 0) {
      router.replace(backUrl);
    }
  }

  const cancel = () => {
    router.replace('/material/material-list');
  }

  const handleSubmit = async () => {
    if (!ruleFormRef.value) return
    const valid = await ruleFormRef.value.validate();
    if (valid) {
      await auditCpAction(route.query.bundleId || route.query.avatarId);
    }
  }

  </script>
  <style lang="less" scoped>
  .box-card {
    min-height: 740px;
  }

  .content {
    margin-bottom: 110px;
  }

  .content-bottom {
    height: 100px;
    width: 100%;
    background: #fff;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 0;
  }
  </style>
