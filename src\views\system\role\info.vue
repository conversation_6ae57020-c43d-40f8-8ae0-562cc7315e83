<template>
  <el-card class="n-layout-page-header">
    <el-form :model="form" ref="formRef" label-width="80px" :rules="rules">
      <el-form-item label="角色名称" required prop="name">
        <el-input maxlength="100" v-model="form.name" @input="(value) => handleInput('name', value)"/>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input maxlength="100" v-model="form.description" type="textarea"  @input="(value) => handleInput('description', value)"/>
      </el-form-item>
      <el-form-item label="权限信息" required>
        <el-radio-group v-model="form.key" :disabled="id">
          <el-radio label="cpuser">CP用户</el-radio>
          <el-radio label="">管理员</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" prop="key">
        <el-tree
          ref="treeRef"
          :data="
            form.key === 'cpuser'
              ? permissionOptions[0].children
              : permissionOptions.filter((_, index) => index !== 0)
          "
          show-checkbox
          node-key="value"
          :props="{
            id: 'value',
            children: 'children',
            label: 'label',
            disabled: (data) => data.status === 2,
          }"
        />
      </el-form-item>
    </el-form>
    <el-button @click="submit">提交</el-button>
    <el-button @click="goback">返回</el-button>
  </el-card>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage, FormInstance } from 'element-plus';
  import { getPermissionList, addRole, editRole, getRoleDetail } from '@/api/system/role';
  import { useFilterInputHander } from '@/views/hooks';

  const router = useRouter();
  const route = useRoute();
  const { id } = route.query;

  const formRef = ref<FormInstance>();
  const form = reactive({
    name: '',
    key: '',
    description: '',
  });
  const handleInput = useFilterInputHander(form);

  const rules = {
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    key: [
      {
        validator: (_, __, callback) => {
          const permission_keys = treeRef.value.getCheckedKeys();
          if (permission_keys.length === 0) {
            callback(new Error('请选择角色权限'));
          } else {
            callback();
          }
        },
      },
    ],
  };

  const checkboxValue = ref<any>({});
  const treeRef = ref<any>(null);
  onMounted(async () => {
    await queryPermissionList();
    if (id) {
      const res = await getRoleDetail(id);
      for (let k in form) {
        form[k] = res.data[k];
      }
      if (treeRef.value) {
        treeRef.value.setCheckedKeys(res.data.permissions);
      }
      checkboxValue.value = res.data.permission_json;
    }
  });

  const permissionOptions = ref<any[]>([]);
  async function queryPermissionList() {
    const res = await getPermissionList();
    permissionOptions.value = res.data;
  }

  async function submit() {
    if (!formRef.value) return;
    const permission_keys = treeRef.value.getCheckedKeys();
    formRef.value.validate(async (valid) => {
      if (valid) {
        if (id) {
          await editRole(id, {
            ...form,
            permission_keys: permission_keys.filter((i) => i),
          });
          ElMessage({
            message: '修改成功！',
            type: 'success',
          });
        } else {
          await addRole({
            ...form,
            permission_keys: permission_keys.filter((i) => i),
          });
          ElMessage({
            message: '新增成功！',
            type: 'success',
          });
        }
        goback();
      }
    });
  }

  function goback() {
    router.push({ path: '/role' });
  }
</script>

<style lang="less" scoped>
  .upload-demo {
    :deep(.el-upload--picture-card) {
      --el-upload-picture-card-size: unset;
      .el-upload-dragger {
        height: 100%;
        .avatar {
          max-width: 250px;
          max-height: 250px;
        }
      }
    }
    :deep(.el-upload-list--picture-card) {
      min-height: 156px;
      .el-upload--picture-card:nth-child(2) {
        display: none;
      }
    }
  }
</style>
