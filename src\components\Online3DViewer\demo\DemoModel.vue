<template>
  <div class="demo-container">
    <h2>3D模型查看器演示</h2>
    <div class="model-container">
      <ModelViewer 
        ref="modelViewerRef"
        :modelUrl="modelUrl"
        :autoRotate="true"
      />
    </div>
    <div class="controls">
      <h3>示例模型</h3>
      <div class="model-list">
        <el-button 
          v-for="model in demoModels" 
          :key="model.name"
          @click="loadModel(model.url)"
          type="primary"
          plain
        >
          {{ model.name }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ModelViewer from '../ModelViewer.vue';

const modelViewerRef = ref<any>(null);
const modelUrl = ref('');

// 示例模型列表
const demoModels = [
  { 
    name: '<PERSON> (OBJ)', 
    url: 'https://storage.googleapis.com/files.flyingshapes.com/website/assets/suzanne.obj' 
  },
  { 
    name: '<PERSON> (STL)', 
    url: 'https://storage.googleapis.com/flyingshapes-public/StanfordBunny.stl' 
  },
  { 
    name: 'Damaged Helmet (GLTF)', 
    url: 'https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/DamagedHelmet/glTF/DamagedHelmet.gltf' 
  }
];

// 加载模型
const loadModel = (url: string) => {
  modelUrl.value = url;
};
</script>

<style scoped>
.demo-container {
  display: flex;
  flex-direction: column;
  padding: 20px;
  height: 100vh;
}

.model-container {
  flex: 1;
  min-height: 400px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 20px;
}

.controls {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.model-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}
</style> 