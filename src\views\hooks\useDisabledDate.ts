import { disabledDate } from '@/utils/dateUtil';

const ONE_DAY_MIS = 60 * 60 * 24 * 1000;

export default function useDisabledDate30(chooseDay) {
  const disabled30Date = (time) => {
    const d30 = 30 * ONE_DAY_MIS;
    if (chooseDay.value) {
      const min = chooseDay.value - d30;
      const max = chooseDay.value - 0 + d30;
      return time.getTime() < min || time.getTime() > max || disabledDate(time);
    } else {
      return disabledDate(time);
    }
  };
  return disabled30Date;
}

export function useDisabledOutCurrentMomthDate(chooseDay) {
  const disabledOutCurrentMomthDate = (time) => {
    if (chooseDay.value) {
      const selectedDay = new Date(chooseDay.value);
      const dayOfMomth = selectedDay.getDate();
      const lastDayOfMomth = new Date(
        selectedDay.getFullYear(),
        selectedDay.getMonth(),
        0
      ).getDate();
      const min = chooseDay.value - (dayOfMomth - 1) * ONE_DAY_MIS;
      const max = chooseDay.value - 1 + (lastDayOfMomth - dayOfMomth) * ONE_DAY_MIS;
      return time.getTime() < min || time.getTime() > max || disabledDate(time);
    } else {
      return disabledDate(time);
    }
  };
  return disabledOutCurrentMomthDate;
}
