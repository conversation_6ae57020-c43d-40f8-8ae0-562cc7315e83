<template>
  <el-card class="box-card">
    <div class="flex justify-between items-center">
      <h2>资产概览</h2>
      <div class="w-80 flex justify-start">
        <span class="flex-1">今日更新数量: {{ tipsTotal.todayAssetTotal }}</span>
        <span class="flex-1">本月更新数量: {{ tipsTotal.monthAssetTotal }}</span>
      </div>
    </div>
    <div class="flex flex-row flex-nowrap">
      <CountCard
        v-for="item in totalList"
        :key="item.key"
        :tip="item.tip"
        :title="item.title"
        :total="item.total"
      />
    </div>
  </el-card>
  <el-card class="box-card mt-5">
    <div class="flex justify-between items-center">
      <h2 class="text-header-text-color text-xl"
        >资源被使用TOP 10
        <el-tooltip content="所选时间范围内，上架素材资源和背景音乐被使用次数TOP 10">
          <el-icon
            :size="16"
            color="#6b7280"
            class="cursor-pointer align-text-middle"
            placement="right"
            ><InfoFilled /></el-icon
        ></el-tooltip>
      </h2>
      <el-form :inline="true" :model="topQueryParams" class="justify-self-end self-center text-end">
        <el-form-item label="操作时间:">
          <InnerMonthPicker
            class="xx-date-picker -ml-48"
            v-model="topQueryParams.dateRange"
          />
        </el-form-item>
      </el-form>
    </div>
    <el-select v-model="chartType">
      <el-option value="bar" label="柱状图" />
      <el-option value="pie" label="圆饼图" />
    </el-select>
    <div id="chart" ref="barChart" class="flex justify-between"> </div>
  </el-card>
  <el-card class="box-card mt-5">
    <h2 class="text-header-text-color text-xl">资源数量变化</h2>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClickTab">
      <el-tab-pane label="素材" :name="0" />
      <el-tab-pane label="预置形象" :name="1" />
      <el-tab-pane label="背景音乐" :name="2" />
    </el-tabs>
    <div class="flex justify-between items-center">
      <el-form :inline="true" :model="queryParams" class="justify-self-end self-center text-end">
        <el-form-item label="风格类型:" v-if="[0, 1].includes(activeName)">
          <StyleCascader
            :key="activeName"
            v-model="queryParams.style_id"
            :style="{ width: '200px', textAlign: 'left' }"
          />
        </el-form-item>
        <el-form-item label="素材类型：" v-if="activeName == 0">
          <el-tree-select
            clearable
            :style="{ width: '160px' }"
            v-model="queryParams.type"
            :data="typeTreeData"
            :check-strictly="false"
            :render-after-expand="false"
            :props="{ label: 'chinese', value: 'name' }"
          />
        </el-form-item>
        <el-form-item label="创建时间：">
          <el-date-picker
            :style="{ width: '240px' }"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            clearable
            @calendar-change="handlCalendarChange"
            @focus="chooseDay = null"
            @blur="chooseDay = null"
            @visible-change="chooseDay = null"
            :disabled-date="disabled30Date"
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
      </el-form>
    </div>
    <div ref="chartRef" id="chart"></div>
  </el-card>
</template>
<script lang="ts">
  export const barChartOption: any ={
    xAxis: {
      type: 'category',
      data: [],
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#959596',
        overflow: 'truncate',
        formatter: (val) => {
          let result = '';
          const len = val.length;
          const limit = 6;
          if (len > limit) {
            for (let i = 0; i <= limit; i += limit) {
              if (i < limit) {
                result += val.substring(i, i + limit) + '\n';
              } else {
                result += val.substring(i, i + Math.min(limit, 3)) + '...'
              }
            }
            result = result.trim();
          } else {
            result = val;
          }
          return result;
        },
      },
      axisLine: {
        lineStyle: {
          color: '#E5E5E5',
        },
      },
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      name: '调用次数',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      renderMode: 'html',
      className: 'echarts-tooltip-text',
      // formatter: '调用次数: {c0}<br />资源名称: {b0}',
      formatter: function (params) {
        console.log('params', params)
        const {name, value} = params[0]
        return `<div>
          <p>调用次数：<b>${value}</b></p>
          <p>
            资源名称：<b>${name}</b>
          </p>
        </div>`
      },
    },
    series: [
      {
        data: [],
        type: 'bar',
        itemStyle: {
          color: '#407EFF',
        },
      },
    ],
  }

  export const pieChartOption: any = {
    color: ['#18F6F8', '#288CFC', '#FFD91A'], // 配置各版块颜色
    legend: {
      top: '10px', // legend位置距离顶部10px
      icon: 'circle', // 将legend图标改为小圆点 // legend图标宽度
      itemWidth: 10, // legend图标宽度
      itemHeight: 10, // legend图标高度
      textStyle: {
        width: '100',
        overflow: 'truncate'
      }
    },
    tooltip: {
      confine: true,
      trigger: 'item',
      renderMode: 'html',
      className: 'echarts-tooltip-text',
      formatter: function (params) {
        console.log('params', params)
        const {name, value} = params
        return `<div>
          <p>调用次数：<b>${value}</b></p>
          <p>
            资源名称：<b>${name}</b>
          </p>
        </div>`
      },
      textStyle: {
        width: '100',
        overflow: 'truncate'
      },
    },
    series: [
      {
        avoidLabelOverlap: false,
        type: 'pie', // 图例类型：饼图
        // roseType: 'area', // 玫瑰图
        center: ['50%', '60%'], // 图例在容器中的位置，第一个控制左右，第二个控制上下
        radius: ['0%', '70%'], // 20%：内部空白圆的直径，60%：外部圆环的直径
        label: {
          show: false,
          formatter: '{b}', // 自定义lable处展示那些数据及其格式
          fontSize: 16,   // 字体大小
          width: 150,
          backgroundColor: 'white',
          padding: 5,
        },
        labelLine: {
          length: 50,  // 挨着图例的直线的长度
          length2: 20  // 挨着文字的直线的长度
        },
        data: []
      }
    ]
  }
</script>
<script lang="ts" setup>
  import { getAssetTypeList } from '@/api/material/material';
  import { getStatisticsTotal, getSumday, getOverviewTop10 } from '@/api/statistics';
  import dayjs from 'dayjs';
  import { Ref, onMounted, reactive, ref, watch } from 'vue';
  import CountCard from './CountCard.vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { useUserStore } from '@/store/modules/user';
  import useDisabledDate from '@/views/hooks/useDisabledDate';
  import { InfoFilled } from '@element-plus/icons-vue';
  import { NAME_MAP } from './const';
  import { format } from 'date-fns';
import { InnerMonthPicker } from '@/components/InnerMonthPicker';

  const userStore = useUserStore();

  const topQueryParams = reactive({
    dateRange: [
      format(new Date().setDate(1), 'yyyy-MM-dd'),
      format(new Date(), 'yyyy-MM-dd'),
    ] as any,
  });

  const chartRef = ref<HTMLDivElement | null>(null);
  const barChart = ref<HTMLDivElement | null>(null);
  const activeName = ref(0);
  const chooseDay = ref(null);
  const tipsTotal = ref({
    todayAssetTotal: 0,
    monthAssetTotal: 0,
  });

  const handlCalendarChange = (date) => {
    chooseDay.value = date[0];
  };
  const disabled30Date = useDisabledDate(chooseDay);

  const handleClickTab = (tab) => {
    activeName.value = tab.props.name;
    queryParams.resource_type = tab.props.name;
    queryParams.style_id = '';
    queryParams.type = '';
    queryParams.dateRange = [];
  };

  const typeTreeData = ref<any>([]);

  const fetchTypeTree = async () => {
    const { code, data } = await getAssetTypeList();
    if (code == 0) {
      typeTreeData.value = [
        {
          name: '',
          chinese: '全部',
          order: -1,
          level: 1,
          parent: null,
          gender: 0,
        },
        ...data,
      ];
    }
  };

  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const chartOptions = ref<any>({
    xAxis: {
      type: 'category',
      data: [],
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#959596',
      },
      axisLine: {
        lineStyle: {
          color: '#E5E5E5',
        },
      },
    },
    yAxis: {
      name: '资源数量',
      type: 'value',
      minInterval: 1,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      formatter: '资源数量: {c0}<br />时间: {b0}',
    },
    series: [
      {
        data: [], //total
        type: 'line',
        lineStyle: {
          color: '#407EFF',
        },
        symbol: 'circle',
        itemStyle: {
          color: '#407EFF',
        },
      },
    ],
  });
  const chartType = ref('bar')
  watch(chartType, (v) => {
    if (v === 'bar') {
      setBarChartOptions(barChartOption);
    } else {
      setBarChartOptions(pieChartOption);
    }
  })
  const { setOptions: setBarChartOptions } = useECharts(barChart as Ref<HTMLDivElement>);

  
  const fetchSumday = async () => {
    const params = generateParams();
    const { data } = await getSumday(params);
    const dates: string[] = [];
    const totals: number[] = [];
    data.forEach(({ date, total }) => {
      dates.push(dayjs(date).format('MM-DD'));
      totals.push(total);
    });
    chartOptions.value.xAxis.data = dates;
    chartOptions.value.series[0].data = totals;
    setOptions(chartOptions.value);
  };

  const totalList = ref<Array<{ title: string; tip: string; total: number; key: string }>>([]);

  const queryParams = reactive({
    type: '',
    dateRange: [],
    resource_type: 0,
    style_id: '',
  });

  const generateParams = () => {
    const provider_id = userStore.currentProviderId;
    const { dateRange, style_id, type, resource_type, ...other } = queryParams;
    const params: any = { ...other, resource_type, provider_id };
    if (dateRange && Array.isArray(dateRange)) {
      const [start, end] = dateRange;
      params.create_start = start;
      params.create_end = end;
    }
    if ([0, 1].includes(resource_type)) {
      params.style_id = style_id;
    }
    if (resource_type === 0) {
      params.type = type;
    }
    return params;
  };

  const fetchStatisticsTotal = async () => {
    const provider_id = userStore.currentProviderId;
    const {
      data: {
        act_total,
        background_total,
        cloth_total,
        model_total,
        all_asset_total,
        today_asset_total,
        month_asset_total,
      },
    } = await getStatisticsTotal({ provider_id });
    const list = [
      {
        title: NAME_MAP['model_total'],
        total: model_total,
        key: 'model_total',
        tip: '上线的头部和躯干素材',
      },
      {
        title: NAME_MAP['cloth_total'],
        total: cloth_total,
        key: 'cloth_total',
        tip: '上线的上装、下装、足部和配饰等素材',
      },
      {
        title: NAME_MAP['act_total'],
        total: act_total,
        key: 'act_total',
        tip: '上线的动作素材',
      },
      {
        title: NAME_MAP['background_total'],
        total: background_total,
        key: 'background_total',
        tip: '上线的背景和前景素材',
      },
      {
        title: NAME_MAP['all_asset_total'],
        total: all_asset_total,
        key: 'all_asset_total',
        tip: 'CP用户基于当前合作伙伴下制作的素材总和',
      },
    ];
    tipsTotal.value.monthAssetTotal = month_asset_total;
    tipsTotal.value.todayAssetTotal = today_asset_total;
    totalList.value = list;
  };

  const fetchOverviewTop10 = async () => {
    const { dateRange } = topQueryParams;
    const provider_id = userStore.currentProviderId;

    const params: any = { provider_id };
    if (dateRange && Array.isArray(dateRange)) {
      const [start, end] = dateRange;
      params.create_start = start;
      params.create_end = end;
    }
    const { data } = await getOverviewTop10(params);
    const dates: string[] = [];
    const totals: number[] = [];
    const pieData: any[] = []
    data.forEach(({ total, resource_name }) => {
      dates.push(resource_name);
      totals.push(total);
      pieData.push({
        name: resource_name,
        value: total
      })
    });
    barChartOption.xAxis.data = dates;
    barChartOption.series[0].data = totals;
    pieChartOption.series[0].data = pieData
    console.log('dates', dates)
    console.log('totals', totals)
    setBarChartOptions(chartType.value === 'bar' ? barChartOption : pieChartOption);
  };

  onMounted(() => {
    fetchTypeTree();
    fetchStatisticsTotal();
    fetchOverviewTop10();
    fetchSumday();
  });

  watch(
    () => topQueryParams,
    () => {
      fetchOverviewTop10();
    },
    { deep: true }
  );

  watch(
    () => queryParams,
    async () => {
      await fetchSumday();
    },
    { deep: true }
  );
</script>
