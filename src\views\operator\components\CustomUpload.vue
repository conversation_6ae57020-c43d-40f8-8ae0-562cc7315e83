<template>
  <el-upload
    ref="upload"
    class="upload-demo"
    drag
    :list-type="props['picture-card'] || 'text'"
    :show-file-list="props['showFileList']"
    :on-change="uploadIcon"
    :auto-upload="false"
    :limit="props.limit || 1"
    :on-exceed="handleExceed"
    :accept="props.accept"
    :before-upload="(file) => {
      emits('beforeUpload', file);
    }"
  >
    <slot name="default"></slot>
    <template #tip>
      <slot name="tip"> </slot>
    </template>
  </el-upload>
</template>

<script lang="ts" setup>
  import { uploadFile } from '@/api/common';
  import { ref } from 'vue';
  import { genFileId } from 'element-plus';
  import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
  import { useMessage } from 'naive-ui';
  // import { computed } from 'vue';

  const props = defineProps([
    'modelValue',
    'scope',
    'limit',
    'showFileList',
    'accept',
    'fileList',
    'maxSize',
  ]);
  const emits = defineEmits(['update:modelValue', 'upload', 'change', 'beforeUpload']);
  const upload = ref<UploadInstance>();
  const message = useMessage();
  async function uploadIcon(file, files) {
    const { size, raw, name } = file;
    if (props.maxSize && size / 1024 / 1024 >= props.maxSize) {
      message.error(`上传文件最大值不能超过${props.maxSize}M`);
      return false;
    }
    const formData = new FormData();
    formData.append('file', raw);
    emits('upload', true);
    try {
      const res = await uploadFile(props.scope, formData);
      emits('update:modelValue', {
        ...res.data,
        name: name,
      });
      emits('change', file, files);
    } catch (e) {
      console.log(e, '上传出错');
    }
    emits('upload', false);
    return false;
  }

  const handleExceed: UploadProps['onExceed'] = (files) => {
    upload.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    upload.value!.handleStart(file);
  };
</script>
