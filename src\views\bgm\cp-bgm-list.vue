<template>
  <el-card class="box-card">
    <div class="flex">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline flex-1">
        <el-form-item label="音乐状态" class="w-48">
          <el-select v-model="queryParams.status" clearable placeholder="请选择音乐状态">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              clearable
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间：" class="w-80">
          <el-date-picker
            clearable
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item>
          <el-input
            :prefix-icon="Search"
            v-model.trim="queryParams.keyword"
            placeholder="请输入音乐名称"
            clearable
          />
        </el-form-item>
      </el-form>
      <div class="flex justify-end mb-4">
        <el-button type="primary" :icon="Plus" @click="handleAddMusic">新增音乐</el-button>
        <el-button type="primary" @click="handleExport">导出</el-button>
        <el-button type="danger" @click="handleBatchRemove">批量删除</el-button>
      </div>
    </div>

    <el-table :data="tableData" style="width: 100%" ref="bgmTable">
      <el-table-column type="selection" :selectable="selectableRow" />
      <el-table-column label="音乐名称" width="250" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex ml-2 items-center">
            <el-image
              preview-teleported
              class="w-16 h-16 basis-16"
              :src="row.url_bgm_icon"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :z-index="10000"
              :preview-src-list="[row.url_bgm_icon]"
              fit="contain"
            >
              <template #error>
                <div class="flex justify-center items-center w-full h-full text-2xl">
                  <el-icon class="text-2xl"><icon-picture /></el-icon>
                </div>
              </template>
            </el-image>
            <div class="truncate block pl-1 flex-1">{{ row.name }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="file_path" min-width="200" label="音乐路径">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-tooltip trigger="hover" placement="top">
              <template #content>
                <span v-text="row.file_path" class="cursor-pointer"></span>
              </template>
              <template #default>
                <span v-text="row.file_path" class="cursor-pointer block truncate"></span>
              </template>
            </el-tooltip>
            <el-icon class="ml-1" @click="handleCopy(row)">
              <DocumentCopy class="cursor-copy" />
            </el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="provider.title"
        label="所属合作伙伴"
        width="120"
        show-overflow-tooltip
      />
      <template v-for="(item, index) in tableColumns">
        <el-table-column
          v-if="item.show"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :formatter="item.formatter"
          show-overflow-tooltip
        />
      </template>
      <el-table-column label="操作" width="300">
        <template #default="{ row }">
          <el-button
            class="m-0 p-0 mr-2"
            type="primary"
            :text="true"
            @click="handleDetailMusic(row)"
            >详情</el-button
          >
          <el-button
          class="m-0 p-0 mr-2"
          type="primary" :text="true" @click="handleTryListen(row)">{{
            currentRowId == row.id && isPlaying ? '暂停' : '试听'
          }}</el-button>
          <el-button
            class="m-0 p-0 mr-2"
            type="primary"
            :text="true"
            v-if="![EAssetStatus.Published, EAssetStatus.Reviewing].includes(row.status)"
            @click="handleEditMusic(row)"
            >编辑</el-button
          >
          <el-button
            class="m-0 p-0 mr-2"
            :text="true"
            v-if="EAssetStatus.Published !== row.status"
            type="danger"
            @click="handleDeleteMusic(row)"
            >删除</el-button
          >
          <el-button
            :text="true"
            class="m-0 p-0 mr-2"
            type="primary"
            @click="handleWithdraw(row)"
            v-if="EAssetStatus.Reviewing == row.status"
            >撤回</el-button
          >
          <el-button
            class="m-0 p-0 mr-2"
            :text="true"
            v-if="![EAssetStatus.Reviewing, EAssetStatus.Published].includes(row.status)"
            type="primary"
            @click="handlePublish(row)"
            >申请发布</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="flex my-4 justify-end"
      background
      layout="total, prev, pager, next, sizes, jumper"
      v-model:page-size="queryParams.limit"
      v-model:current-page="queryParams.page"
      :total="totalRef"
    />
  </el-card>
  <el-dialog v-model="auditDialogVisible" destroy-on-close title="背景音乐详情" center>
      <el-form>
        <el-form-item label="音乐icon:">
          <el-image
            v-if="auditForm.urlBgmIcon"
            preview-teleported
            class="w-4/5 max-w-max h-auto max-h-48 preview-image"
            :src="auditForm.urlBgmIcon"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :z-index="10000"
            :preview-src-list="[auditForm.urlBgmIcon]"
            fit="contain"
          >
            <template #error>
              <div class="flex justify-center items-center w-full h-full text-2xl">
                <el-icon class="text-2xl"><icon-picture /></el-icon>
              </div>
            </template>
          </el-image>
        </el-form-item>
        <el-form-item label="音乐名称:">
          {{ auditForm.name }}
        </el-form-item>
        <el-form-item label="驳回原因:" v-if="auditForm.status === EAssetStatus.Rejected">
          {{ auditForm.revising_reason }}
        </el-form-item>
        <el-form-item label="音频备注:">
          {{ auditForm.remark }}
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="auditDialogVisible = false"> 返回 </el-button>
        </span>
      </template>
    </el-dialog>
</template>

<script lang="ts" setup>
  import {
    getAssetBgmList,
    publishBgm,
    deleteAssetBgm,
    revokeBgm,
    batchDeleteAssetBgm,
  } from '@/api/bgm';
  import { useUserStore } from '@/store/modules/user';
  import { dateTimeFormatter } from '@/utils/format-table-datetime';
  import useAudio from '@/views/hooks/useAudio';
  import { Plus, Search } from '@element-plus/icons-vue';
  import 'dayjs/locale/zh-cn';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import { storeToRefs } from 'pinia';
  import { h, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { useExport } from '@/views/hooks';
  import { disabledDate } from '@/utils/dateUtil';
  import { useClipboard, useThrottleFn } from '@vueuse/core';
  import { DocumentCopy } from '@element-plus/icons-vue';
  import { Picture as IconPicture } from '@element-plus/icons-vue';
import { EAssetStatus } from '../materialManage/avatar/columns';

  const userStore = useUserStore();
  const { exportFile } = useExport();

  const currentRowId = ref('');
  const totalRef = ref(0);
  const bgmTable = ref();
  const auditDialogVisible = ref(false);

  const { setSrc, pause, isPlaying } = useAudio();
  const { currentProviderId } = storeToRefs(userStore);
  const auditForm = reactive({
    id: '',
    name: '',
    status: 0,
    remark: '',
    revising_reason:'',
    urlBgmIcon: '',
    audit_reason: '',
  });

  const STATUS_MAP = {
    [EAssetStatus.Draft]: '草稿',
    [EAssetStatus.Reviewing]: '待审核',
    [EAssetStatus.Published]: '审核通过',
    [EAssetStatus.Rejected]: '驳回待修改',
    [EAssetStatus.Unshelve]: '下架待修改',
  };

  const router = useRouter();
  interface queryParamsForm {
    keyword: string;
    status: string | number;
    dateRange: any[];
    page: number;
    limit: number;
  }

  const selectableRow = (row) => {
    if (row.status == 3) return false;
    return true;
  };

  const source = ref('');
  const { copy, isSupported } = useClipboard({ source });

  const handleCopy = useThrottleFn((row) => {
    source.value = row.file_path;
    if (!isSupported) {
      ElMessage.warning('您的浏览器不支持复制!');
      return;
    }
    copy(source.value);
    ElMessage.success('复制成功!');
    source.value = '';
  }, 3000);

  const queryParams = reactive<queryParamsForm>({
    keyword: '',
    status: '',
    dateRange: [] as any,
    page: 1,
    limit: 10,
  });

  const generateParamas = () => {
    const provider_id = currentProviderId.value;
    const { dateRange, ...other } = queryParams;
    const params: any = { provider_id, ...other };
    if (dateRange && Array.isArray(dateRange) && dateRange.length !== 0) {
      const [start, end] = dateRange;
      params.create_start = start;
      params.create_end = end;
    }
    return params;
  };

  const statusOptions = [
    { label: '全部', value: '' },
    {
      label: '草稿',
      value: 1,
    },
    {
      label: '待审核',
      value: 2,
    },
    {
      label: '审核通过',
      value: 3,
    },
    {
      label: '驳回待修改',
      value: 4,
    },
    {
      label: '下架待修改',
      value: 5,
    },
  ];

  const tableData = ref([]);

  const handleTryListen = (row) => {
    if (isPlaying.value) {
      pause();
      if (row.id !== currentRowId.value) {
        setSrc(row.url_bgm, { autoPlay: true });
      }
    } else {
      setSrc(row.url_bgm, { autoPlay: true });
    }
    currentRowId.value = row.id;
  };

  const fetchBgmList = async () => {
    const params = generateParamas();
    const {
      data: { total, rows: list },
    } = await getAssetBgmList(params);
    tableData.value = list;
    totalRef.value = total;
  };

  onMounted(async () => {
    await fetchBgmList();
  });

  onUnmounted(() => {
    pause();
  });

  const STATUS_COLOR_MAP = {
    3: '#06BF7F;',
    2: '#FAAD14;',
    4: '#FF2020;',
    1: '#C4C4C4;',
  };
  const statusFormatter = (_row, _column, status) => {
    return h('span', { style: `color: ${STATUS_COLOR_MAP[status]}` }, STATUS_MAP[status]);
  };

  const tableColumns = [
    { prop: 'created_at', label: '创建时间', show: true, width: 200, formatter: dateTimeFormatter },
    { prop: 'updated_at', label: '更新时间', show: true, width: 200, formatter: dateTimeFormatter },
    { prop: 'status', label: '音乐状态', show: true, formatter: statusFormatter, width: 150 },
  ];

  watch(
    [() => queryParams, () => currentProviderId.value],
    async () => {
      await fetchBgmList();
    },
    { deep: true }
  );

  //新增音乐
  const handleAddMusic = () => {
    router.push({
      path: '/bgm/add',
    });
  };

  //导出
  const handleExport = () => {
    const selectedRow = bgmTable.value?.getSelectionRows();
    const ids = selectedRow.map(({ id }) => id);
    const params = generateParamas();
    exportFile('/business/export-query/bgm/export/list', params, {ids});
  };
  //编辑音乐
  const handleEditMusic = (row) => {
    router.push({
      path: '/bgm/edit',
      query: {
        id: row?.id,
      },
    });
  };
  // 查看详情
  const handleDetailMusic = (row) => {
    auditDialogVisible.value = true;
    auditForm.name = row.name;
    auditForm.remark = row.remark;
    auditForm.revising_reason = row.revising_reason;
    auditForm.status = row.status;
    auditForm.urlBgmIcon = row.url_bgm_icon;
    auditForm.id = row.id;
  }

  const handleBatchRemove = async () => {
    try {
      const selectedRow = bgmTable.value?.getSelectionRows();
      const ids = selectedRow.map(({ id }) => id);
      if (ids.length <= 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要批量删除的背景音乐',
        });
        return;
      }
      const action = await ElMessageBox.confirm(`确认要批量删除所选背景音乐吗?`);
      if (action == 'confirm') {
        const { code } = await batchDeleteAssetBgm({ ids });
        if (code === 0) {
          ElMessage({
            type: 'success',
            message: '批量删除成功！',
          });
          fetchBgmList();
        }
      }
    } catch (e) {console.log(e)}
  };

  //删除
  const handleDeleteMusic = async (row) => {
    try {
      const action = await ElMessageBox.confirm(
        `确认要继续删除背景音乐 ${row.name}吗?`,
        '确认删除'
      );
      if (action == 'confirm') {
        const { code } = await deleteAssetBgm(row.id);
        if (code === 0) {
          ElMessage({
            type: 'success',
            message: '成功删除',
          });
          fetchBgmList();
        }
      }
    } catch (e) {console.log(e)}
  };

  //撤回
  const handleWithdraw = async (row) => {
    try {
      const action = await ElMessageBox.confirm(`确定要撤回背景音乐“${row.name}”吗？`, '确认撤回');
      if (action == 'confirm') {
        const { code } = await revokeBgm(row.id);
        if (code === 0) {
          ElMessage.success('撤回成功!');
          fetchBgmList();
        }
      }
    } catch (e) {console.log(e)}
  };
  //申请发布
  const handlePublish = async (row) => {
    try {
      const action = await ElMessageBox.confirm(
        `确定要申请发布背景音乐“${row.name}”吗？`,
        '确认申请发布'
      );
      if (action == 'confirm') {
        const { code } = await publishBgm(row.id);
        if (code === 0) {
          ElMessage.success('申请发布成功!');
          fetchBgmList();
        }
      }
    } catch (e) {console.log(e)}
  };
</script>
<style lang="less" scoped>
  .box-card {
    min-height: 800px;
    .title {
      flex: 1;
      margin-left: 10px;
    }
  }
</style>
