import { Wasm<PERSON><PERSON><PERSON> } from "./wasmHelper"

export class RenderKitPublic extends WasmHelper {
    // SDK 门面
    // get RenderKit() {
    //     return this.wasmCtx.module.FURenderKit;
    // }
    // RenderKit logger level
    get Canvas() {
        return this.wasmCtx.module.canvas
    }
    get EFuRenderLoggerLevel() {
        return this.wasmCtx.module.ELoggerLevel;
    }
    // RenderKit 使用的渲染像素格式
    get ERenderFormat() {
        return this.wasmCtx.module.ERenderFormat;
    }
    // RenderKit 抗锯齿多重采样级别
    get EMSAAOption() {
        return this.wasmCtx.module.EMSAAOption;
    }
    // RenderKit 阴影质量
    get EShadowQuality() {
        return this.wasmCtx.module.EShadowQuality;
    }
    // Scece 后处理效果类型
    get EPostProcessType() {
        return this.wasmCtx.module.EPostProcessType;
    }
    get EFuAvatarState() {
        return this.wasmCtx.module.EFuAvatarState;
    }
    // 人物部件的可见性
    get EFuAvatarOperateComponentState() {
        return this.wasmCtx.module.EFuAvatarOperateComponentState;
    }

    // 背景/前景图片的适应方式
    get ESpriteFitType() {
        return this.wasmCtx.module.ESpriteFitType;
    }

    // 相机投影模式
    get EProjectionMode() {
        return this.wasmCtx.module.EProjectionMode;
    }

    get ELightQuality() {
        return this.wasmCtx.module.ELightQuality;
    }
    get FUImageBackground() {
        return this.wasmCtx.module.FUImageBackground
    }
}
