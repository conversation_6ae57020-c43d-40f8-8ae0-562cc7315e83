import { RenderKitPublic } from "./RenderKitPublic"
import animGraph<PERSON>son from "../assets/AnimGraph.json"
import animLogicJson from "../assets/animLogic.json"
import maleDefaults from "../assets/male_defaults"
import femaleDefaults from "../assets/female_defaults"
import { FURenderKit } from "@/packages/types/FURenderKit"
import { FUResourceManager } from "../../fu_resource_manager"
import { ERayCastResult, FUScene } from "@/packages/types/FUScene"
import { FUAvatar } from "@/packages/types/FUAvatar"

export type ModifiedRenderer = ReturnType<FURenderKitSDK['_modifyRenderer']>
export type ModifiedScene = ReturnType<FURenderKitSDK['_modifyScene']>
export type ModifiedAvatar = ReturnType<FURenderKitSDK['_modifyAvatar']>

export class FURenderKitSDK extends RenderKitPublic {
    _resourceManager: FUResourceManager
    RenderKit?: FURenderKit
    _modifiedRenderer?: ModifiedRenderer
    engineAssetsPtr?: string
    engineAssetsSize?: string
    constructor(params) {
        super()
        this._resourceManager = params.resourceManager
    }
    async initialize() {
        if (this.engineAssetsPtr && this.engineAssetsSize) return
        // 加载wasm文件
        await this.wasmCtx.LoadWasm()
        // 加载data文件
        let engineAssetsUrl = this.wasmCtx.locateFile('FURenderKitSDK.data');
        const buffer = await fetch(engineAssetsUrl)
            .then((res) => {
                if (!res.ok) {
                    throw 'EngineAssets download failed';
                }
                return res.arrayBuffer();
            })
        const { ptr, size } = this.writeAssets(new Uint8Array(buffer));
        this.engineAssetsPtr = ptr;
        this.engineAssetsSize = size;
    }

    async authWithAuthpack(authpackUrl, authMethod) {
        const authpack = await this.LoadAssets(
            this.wasmCtx.locateFile(authpackUrl)
        )
        this.wasmCtx.module.SetAuthpack(authpack.ptr, authpack.size)
        const requestStr = this.wasmCtx.module.GetRequestString('1.6.0')

        const resStr = await authMethod(requestStr)
        this.wasmCtx.module.Auth(resStr)

        const renderer = this.wasmCtx.module.FURenderKit.getInstance()
        this.RenderKit = this.wasmCtx.module.FURenderKit
        this._modifiedRenderer = this._modifyRenderer(renderer)
    }

    _modifyRenderer(originRenderKit: FURenderKit) {
        let sdk = this
        const init = originRenderKit.init.bind(originRenderKit)
        const createScene = originRenderKit.createScene.bind(originRenderKit)
        const resize = originRenderKit.resize.bind(originRenderKit)
        const render = originRenderKit.render.bind(originRenderKit)
        const renderer = {
            nextRenderCb: () => { },
            getFUResourceManager: () => {
                return sdk._resourceManager
            },
            initialize: async ({ canvas }) => {
                if (!canvas) {
                    throw "A canvas should be provided"
                } else {
                    sdk.wasmCtx.module.canvas = canvas
                }
                if (sdk._resourceManager) {
                    await sdk._resourceManager.setModule(sdk.wasmCtx.module)
                }
                init(this.engineAssetsPtr, this.engineAssetsSize);
                window.addEventListener('resize', () => {
                    renderer.resize()
                })
            },
            customCreateScene: (name = "default") => {
                const _scene = createScene(name)
                return sdk._modifyScene(_scene)
            },
            resize: (devicePixelRatio = window.devicePixelRatio) => {
                const canvas = sdk.wasmCtx.module.canvas
                const width = canvas.clientWidth * devicePixelRatio
                const height = canvas.clientHeight * devicePixelRatio
                resize(width, height)
                canvas.width = width
                canvas.height = height

                return
            },
            getSize: () => {
                const canvas = sdk.wasmCtx.module.canvas
                return {
                    width: canvas.width,
                    height: canvas.height,
                }
            },
            captureImage: function (options, next) {
                const canvas = sdk.wasmCtx.module.canvas
                const oriWidth = canvas.width
                const oriHeight = canvas.height
                const {
                    width,
                    height,
                    quality = 1,
                    format = "image/png",
                    tmpCanvas,
                } = options

                if (width && height && width !== oriWidth && height !== oriHeight) {
                    if (!tmpCanvas) {
                        throw "captureImage require a tmpCanvas to hold renderring result"
                    }
                    // 准备遮罩 canvas
                    tmpCanvas.width = oriWidth
                    tmpCanvas.height = oriHeight
                    const glCtx = tmpCanvas.getContext("2d")

                    const parentNode = canvas.parentNode
                    const nextSibling = canvas.nextSibling
                    renderer.nextRenderCb = () => {
                        glCtx.drawImage(canvas, 0, 0)
                        next(nextSibling, (done) => {
                            parentNode.removeChild(canvas)

                            requestAnimationFrame(() => {
                                renderer.nextRenderCb = () => {
                                    const dataUrl = canvas.toDataURL(
                                        format,
                                        quality
                                    )
                                    canvas.width = oriWidth
                                    canvas.height = oriHeight
                                    resize(oriWidth, oriHeight)
                                    requestAnimationFrame(() => {
                                        parentNode.insertBefore(canvas, nextSibling)
                                        done(dataUrl)
                                    })
                                }
                                canvas.width = width * quality
                                canvas.height = height * quality
                                resize(width * quality, height * quality)
                            })
                        })
                    }
                } else {
                    next(false, (done) => {
                        renderer.nextRenderCb = () => {
                            const dataUrl = canvas.toDataURL(format, quality)
                            done(dataUrl)
                        }
                    })
                }
            },
            render: function (scene, time) {
                render(scene, time)
                renderer.nextRenderCb()
                renderer.nextRenderCb = () => { }
            },
        }
        return Object.assign(originRenderKit, renderer)
    }

    _modifyScene(originScene: FUScene) {
        const sdk = this
        const scene = {
            addAvatarWithBundleConfig: async function () {
                const avatar = originScene.addAvatar('avatar1')
                // controller_config
                if (sdk._resourceManager?.controllConfig) {
                    const file_buffer = new Uint8Array(sdk._resourceManager?.controllConfig)
                    const controller_config = sdk.writeAssets(file_buffer)
                    avatar.setConfig(controller_config.ptr, controller_config.size)
                    sdk.wasmCtx.module._free(controller_config.ptr)
                }
                return avatar
            },
            customAddAvatarAsync: async function (
                avatarConfig: {
                    data: {
                        gender: string
                        bundle_list: Record<string, string>
                        color_list: any
                        facepup_config: any
                    }
                },
            ) {

                const avatarJson = avatarConfig.data

                const bundleConfig =
                    avatarJson.gender === "male" ? maleDefaults : femaleDefaults

                const privilegedBundles: string[] = []
                if (bundleConfig.Default && bundleConfig.Default.length) {
                    for (let bundle of bundleConfig.Default) {
                        privilegedBundles.push(bundle)
                    }
                }

                // default 关联 bundle 一次性载入
                const requests: Promise<any>[] = []
                for (let path of privilegedBundles) {
                    requests.push(sdk._resourceManager.prepareData(path, true))
                }
                await Promise.all(requests)
                const avatar = await scene.addAvatarWithBundleConfig()
                const avt = sdk._modifyAvatar(
                    avatar,
                    scene
                )

                const anime_graph = sdk.writeUTF8String(JSON.stringify(animGraphJson))
                avt.setAnimGraph(anime_graph.ptr, anime_graph.size)
                sdk.wasmCtx.module._free(anime_graph.ptr)
                // anime_logic
                const anime_logic = sdk.writeUTF8String(JSON.stringify(animLogicJson))
                avt.setAnimLogic(anime_logic.ptr, anime_logic.size)
                sdk.wasmCtx.module._free(anime_logic.ptr)
                // avatar_json
                const { bundle_list, color_list, facepup_config } = avatarJson
                // 换装

                const largeBundles: string[] = []
                const normalBundles: string[] = []

                sdk._resourceManager.preLookupObj(bundle_list);
                for (let bundle of Object.entries(bundle_list).map(i => i[1])) {
                    if (
                        privilegedBundles &&
                        privilegedBundles.indexOf(bundle) !== -1
                    ) {
                        continue
                    } else if (
                        bundle.includes("hair")
                    ) {
                        largeBundles.push(bundle)
                    } else {
                        normalBundles.push(bundle)
                    }
                }

                const largeJobs: Promise<any>[] = []
                const normalJobs: Promise<any>[] = []

                for (let i = 0; i < largeBundles.length; i++) {
                    const bundle = largeBundles[i]

                    const job = sdk._resourceManager.prepareData(
                        bundle,
                        false,
                    )
                    largeJobs.push(job)
                }
                const largePaths = await Promise.all(largeJobs)

                for (let i = 0; i < normalBundles.length; i++) {
                    const bundle = normalBundles[i]

                    const job = sdk._resourceManager.prepareData(
                        bundle,
                        false
                    )
                    normalJobs.push(job)
                }

                const normalPaths = await Promise.all(normalJobs)
                avt.addComponents(
                    [
                        ...normalPaths,
                        ...largePaths,
                        ...privilegedBundles,
                    ]
                )
                if (bundle_list.length) {
                    // 设置颜色和捏脸数据
                    for (let colorObj of color_list) {
                        const name = colorObj.name
                        const color = colorObj.color
                        avt.setColor(name, color)
                    }
                    for (let bone_control_obj of facepup_config.bone_controllers) {
                        const name = bone_control_obj.name
                        const value = bone_control_obj.value
                        avt.setBoneTransform(name, value)
                    }
                }
                sdk._resourceManager.markFilesUsed(bundle_list)

                return avt
            },

            prepareAvatarAsync: async function (
                controller_config_path,
                itemListJson,
                avatarJson
            ) {
                await sdk._resourceManager?.prepareData(controller_config_path, true)
                const requests: any[] = []
                for (let bundle of avatarJson.bundle_list) {
                    requests.push(sdk._resourceManager?.prepareData(bundle))
                }
                await Promise.all(requests)
            },

            setForegroundAsync: async function (path) {
                await sdk._resourceManager?.prepareData(path)

                const result = originScene.addForeground(path)
                sdk._resourceManager?.markFileUsed(path)

                return result
            },

            setBackgroundAsync: async function (path) {
                await sdk._resourceManager?.prepareData(path)

                const result = originScene.addBackground(path)
                sdk._resourceManager?.markFileUsed(path)

                return result
            },

            setLight: function (path) {
                originScene.clearLight()
                const result = originScene.addLight(path)
                sdk._resourceManager?.markFileUsed(path)

                return result
            },

            setLightAsync: async function (path) {
                await sdk._resourceManager?.prepareData(path)
                scene.setLight(path)
            },

            setCameraAsync: async function (path) {
                await sdk._resourceManager?.prepareData(path)

                const result = originScene.setCamera(path)
                sdk._resourceManager?.markFileUsed(path)

                return result
            },

            loadCustomAvatarWithBundle: async function (bundlePath, bundleUrl) {
                const sceneSelf = this;
                const controllConfig = await sdk._resourceManager?.getControllConfig() as any;

                const controller_config_promise = sdk.LoadAssets(controllConfig);
                const avt = sdk._modifyAvatar(originScene.addAvatar(), sceneSelf);
                // controller_config
                const controller_config = await controller_config_promise;
                avt.setConfigWithData(controller_config.ptr, controller_config.size);
                sdk.wasmCtx.module._free(controller_config.ptr);
                // anime_graph
                const anime_graph = sdk.writeUTF8String(JSON.stringify(animGraphJson));
                avt.setAnimGraph(anime_graph.ptr, anime_graph.size);
                avt.useDefaultAnimationGraph(1);
                sdk.wasmCtx.module._free(anime_graph.ptr);
                // anime_logic
                const anime_logic = sdk.writeUTF8String(JSON.stringify(animLogicJson));
                avt.setAnimLogic(anime_logic.ptr, anime_logic.size);
                avt.useDefaultAnimationLogic(1);
                sdk.wasmCtx.module._free(anime_logic.ptr);

                const objArrayBuffer = await sdk._resourceManager?.downloadAsset(bundleUrl);
                const bundle = sdk.writeAssets(objArrayBuffer);
                let avatarItem = new sdk.wasmCtx.module.FUAvatarItem(bundle.ptr, bundle.size, bundlePath, bundlePath);
                avt.addItem(avatarItem);

                return avt;
            },

            /** 碰撞体点击 */
            getCollisionPoint: function (e) {
                const x = e.clientX
                const y = e.clientY
                const width = sdk.Canvas.clientWidth
                const height = sdk.Canvas.clientHeight
                const collisionPoint = originScene.getRayCastBodyName(width, height, x, y)
                return collisionPoint
            }
        }
        return Object.assign(originScene, scene)
    }

    _modifyAvatar(originAvatar: FUAvatar, scene) {
        const sdk = this
        const avatar = {
            _animationInterval: null as any,
            addComponents: function (pathList) {
                const result: any = []
                pathList.forEach(path => {
                    const removeList = originAvatar.addComponentWithMofifyTest(path)
                    const size = removeList.size()
                    for (let i = 0; i < size; i++) {
                        result.push(removeList.get(i))
                    }
                })
                return result
            },
            removeComponents: function (pathList) {
                pathList.forEach(path => {
                    const item = originAvatar.getComponent(path)
                    originAvatar.removeComponent(item)
                })
            },
            getAnimationProgress: function () {
                return originAvatar.getAnimationGraphParamFloat("DefaultAnimNodeProgress")
            },
            clearAnimInterval: function () {
                if (avatar._animationInterval) {
                    clearInterval(avatar._animationInterval)
                    avatar._animationInterval = null
                }
            },
            setAnimationSync: function (animationPath) {
                originAvatar.setAnimation(animationPath, 'DefaultAnimNode', true)
            },
            /**
             * Sets the animation batch for the given animation path list, and repeat the last one.
             *
             * @param {Array<string>} animationPathList - The list of animation paths.
             * @return {Promise<void>} A promise that resolves when all animations are set.
             */
            setAnimationBatch: async function (animationPathList) {
                for (let i = 0; i < animationPathList.length; i++) {
                    const path = animationPathList[i]
                    await new Promise(r => {
                        avatar.clearAnimInterval()
                        avatar._animationInterval = setInterval(() => {
                            if (avatar.getAnimationProgress() > 0.9) {
                                originAvatar.setAnimation(path, 'DefaultAnimNode', true)
                                avatar.clearAnimInterval()
                                r(path)
                            }
                        }, 50)
                    })
                }
            },
            setAnimationAsync: async function (path, once?, path2?, cb?) {
                await sdk._resourceManager?.prepareData(path)

                originAvatar.setAnimation(path, 'DefaultAnimNode', true)
                if (once) {
                    avatar.clearAnimInterval()
                    avatar._animationInterval = setInterval(() => {
                        if (avatar.getAnimationProgress() > 0.9) {
                            avatar.setAnimationAsync(path2)
                            avatar.clearAnimInterval()
                            if (cb) cb()
                        }
                    }, 50) as unknown as any
                }
                sdk._resourceManager?.markFileUsed(path)
            },

            addComponentsAsync: async function (paths) {
                const requests: any[] = []
                const result: any[] = []
                for (let path of paths) {
                    const p = sdk._resourceManager?.prepareData(path).then(() => {
                        const removeList = avatar.addComponents([path])
                        sdk._resourceManager?.markFilesUsed(paths)
                        const size = removeList.length
                        for (let i = 0; i < size; i++) {
                            result.push(removeList[i])
                        }
                    })
                    requests.push(p)
                }

                return Promise.all(requests).then(() => result)
            },

            prepareComponentsAsync: async function (paths, isPersistent) {
                const requests: any[] = []
                for (let path of paths) {
                    requests.push(
                        sdk._resourceManager?.prepareData(path, isPersistent)
                    )
                }
                await Promise.all(requests)
            },

            setAvatarMouthBlendShape: function (expression) {
                const module = sdk.wasmCtx.module
                const heapSpace = module._malloc(
                    expression.length * expression.BYTES_PER_ELEMENT
                )
                module.HEAPF32.set(expression, heapSpace >> 2) // 2
                originAvatar.blendMouthShape(heapSpace, 47)
                module._free(heapSpace)
            },
            extra: {} as Record<string, any>
        }

        return Object.assign(originAvatar, avatar)
    }
}
