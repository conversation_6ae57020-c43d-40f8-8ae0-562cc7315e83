<template>
  <el-form
    ref="partnerFormRef"
    :model="partnerForm"
    :rules="formRules"
    label-width="120px"
    class="demo-partnerForm"
    status-icon
  >
    <el-card class="box-card">
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="伙伴名称" prop="title">
            <el-input v-model="partnerForm.title" placeholder="请输入伙伴名称" maxlength="100" @input="(value) => handleInput('title', value)" />
          </el-form-item>
          <el-form-item label="封面icon" prop="icon">
            <CustomUpload
              class="bgm-upload"
              @upload="(status) => handleUploadStatusChange('icon', 0, status)"
              v-model="partnerForm.icon"
              scope="head"
              :show-file-list="false"
              list-type="picture-card"
              accept=".jpg,.png,.jpeg"
              :maxSize="4"
            >
              <template v-if="partnerForm.icon.path">
                <div class="card-bgm-uploaded flex items-center">
                  <div class="w-16 h-16 flex justify-center items-center rounded">
                    <img alt="图片" class="rounded" :width="60" :height="60" :src="partnerForm.icon.url" />
                  </div>
                  <div class="right-section ml-4 flex flex-col justify-between">
                    <div class="text-sm file-name text-start" v-text="partnerForm.icon.name"></div>
                    <el-button
                      class="bg-transparent"
                      type="primary"
                      :text="true"
                      @click="submitUploadIcon"
                      >重新上传</el-button
                    >
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="bgm-card">
                  <img alt="图片" :src="iconUpload" />
                  <div class="el-upload__text">点击或拖拽文件上传 </div>
                </div>
              </template>
              <template #tip>
                <div class="el-upload__tip"> 建议尺寸:640*320,小于4M的jpg、jpeg、png格式 </div>
              </template>
            </CustomUpload>
          </el-form-item>
          <el-form-item label="联系地址" prop="address">
            <el-input v-model="partnerForm.address" placeholder="请输入联系地址" @input="(value) => handleInput('address', value)" maxlength="100" />
          </el-form-item>
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="partnerForm.phone" placeholder="请输入联系电话" @input="(value) => handleInput('phone', value, 'number')" maxlength="11" />
          </el-form-item>
          <el-form-item label="伙伴的系统ID" prop="identifier">
            <el-input v-model="partnerForm.identifier" placeholder="请输入伙伴的系统ID" maxlength="100" @input="(value) => handleInput('identifier', value)"/>
          </el-form-item>
          <el-form-item label="伙伴描述" prop="description">
            <el-input
              type="textarea"
              v-model="partnerForm.description"
              rows="6"
              maxlength="100"
              placeholder="请输入伙伴描述"
              @input="(value) => handleInput('description', value)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item
            label="模型文档"
            :rules="formRules.modelDocument"
            prop="config.modelDocument"
          >
            <el-input
              v-model="partnerForm.config.modelDocument"
              placeholder="请输入模型文档"
              maxlength="300"
              @input="(value) => handleInput(['config', 'modelDocument'], value)"
            />
          </el-form-item>
          <el-form-item
            :rules="formRules.modelDescription"
            label="模型简介"
            prop="config.modelDescription"
          >
            <el-input
              v-model="partnerForm.config.modelDescription"
              rows="6"
              type="textarea"
              placeholder="请输入模型简介"
              maxlength="100"
              @input="(value) => handleInput(['config', 'modelDescription'], value)"
            />
          </el-form-item>
          <el-form-item
            label="绑定文档"
            :rules="formRules.bindingDocument"
            prop="config.bindingDocument"
          >
            <el-input
              v-model="partnerForm.config.bindingDocument"
              placeholder="请输入绑定文档"
              maxlength="300"
              @input="(value) => handleInput(['config', 'bindingDocument'], value)"
            />
          </el-form-item>
          <el-form-item
            label="绑定简介"
            :rules="formRules.bindingDescription"
            prop="config.bindingDescription"
          >
            <el-input
              v-model="partnerForm.config.bindingDescription"
              rows="6"
              type="textarea"
              placeholder="请输入绑定简介"
              maxlength="100"
              @input="(value) => handleInput(['config', 'bindingDescription'], value)"
            />
          </el-form-item>
          <el-form-item
            label="动画文档"
            :rules="formRules.animationDocument"
            prop="config.animationDocument"
          >
            <el-input
              v-model="partnerForm.config.animationDocument"
              placeholder="请输入动画文档" 
              maxlength="300"
              @input="(value) => handleInput(['config', 'animationDocument'], value)"
            />
          </el-form-item>
          <el-form-item
            label="动画简介"
            :rules="formRules.animationDescription"
            prop="config.animationDescription"
          >
            <el-input
              v-model="partnerForm.config.animationDescription"
              rows="6"
              type="textarea"
              placeholder="请输入动画简介"
              maxlength="100"
              @input="(value) => handleInput(['config', 'animationDescription'], value)"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card mt-2">
      <h2>配置信息</h2>
      <h4>工具配置</h4>
      <div
        class="flex flex-row justify-between items-center"
        v-for="(item, index) in partnerForm.toolConfig"
        :key="index"
      >
        <el-row :gutter="0" class="wrapper p-5 mb-5 flex-1 m-0">
          <el-col :span="8" :offset="2">
            <el-form-item
              label="工具名称"
              :prop="'toolConfig.' + index + '.name'"
              :rules="formRules.toolConfigName(index)"
            >
              <el-input
                v-model="item.name"
                placeholder="请输入工具名称"
                maxlength="100"
                @input="(value) => handleInput(['toolConfig', index, 'name'], value)"
              />
            </el-form-item>
            <el-form-item
              label="工具简介"
              :prop="'toolConfig.' + index + '.description'"
              :rules="formRules.toolConfigDescription"
            >
              <el-input
                v-model="item.description"
                rows="6"
                type="textarea"
                placeholder="请输入工具简介"
                maxlength="100"
                @input="(value) => handleInput(['toolConfig', index, 'description'], value)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="2">
            <el-form-item
              label="工具文件"
              :rules="formRules.toolConfigPath(index)"
              :prop="'toolConfig.' + index + '.filepath.path'"
            >
              <CustomUpload
                class="bgm-upload"
                v-model="item.filepath"
                scope="provider_config"
                @upload="(status) => handleUploadStatusChange('toolConfig', index, status)"
                @change="(file) => handleUploadChange('toolConfig', index, file)"
                :show-file-list="false"
                list-type="picture-card"
                :maxSize="2048"
              >
                <template v-if="item.filepath.path">
                  <div class="card-bgm-uploaded flex items-center">
                    <div
                      class="w-16 h-16 flex justify-center items-center rounded bg-card-bg-color"
                    >
                      <img alt="图片" :width="36" :height="36" :src="fileIcon" />
                    </div>
                    <div class="right-section px-4 w-0 flex-1 flex flex-col justify-between">
                      <div
                        class="text-sm file-name text-start pr-2 break-words"
                        v-text="item.filepath.name"
                      ></div>
                      <el-button class="bg-transparent" type="primary" :text="true"
                        >重新上传</el-button
                      >
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="bgm-card">
                    <img alt="图片" :src="uploadIcon" />
                    <div class="el-upload__text">点击或拖拽文件上传 </div>
                  </div>
                </template>
                <template #tip>
                  <div class="el-upload__tip"> 建议：小于50M的文件 </div>
                </template>
              </CustomUpload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-button type="danger" :text="true" @click="handleDeleteTool(index)">
          <el-icon class="el-icon--left">
            <Delete />
          </el-icon>
        </el-button>
      </div>
      <div class="flex justify-center m-5">
        <el-button type="primary" @click="handleAddTool">
          <el-icon class="el-icon--left"> <Plus /> </el-icon>新增工具
        </el-button>
      </div>
      <template v-if="isEdit">
        <h4>模型配置</h4>
        <div
          class="flex flex-row justify-between items-center"
          v-for="(item, index) in partnerForm.modelConfig"
          :key="index"
        >
          <el-row :gutter="0" class="wrapper p-5 mb-5 flex-1 m-0">
            <el-col :span="8" :offset="2">
              <el-form-item
                label="模型名称"
                :prop="'modelConfig.' + index + '.name'"
                :rules="formRules.modelConfigName(index)"
              >
                <el-input
                  v-model="item.name"
                  maxlength="100"
                  @input="(value) => handleInput(['modelConfig', index, 'name'], value)"
                />
              </el-form-item>
              <el-form-item
                label="风格类型"
                :prop="'modelConfig.' + index + '.style_id'"
                :rules="formRules.modelConfigStyleId(index)"
              >
                <el-select
                  v-model="item.style_id"
                  placeholder="请选择模型风格"
                  size="large"
                >
                  <el-option
                    v-for="style in styleOptions"
                    :key="style.id"
                    :label="style.title"
                    :value="style.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" :offset="2">
              <el-form-item
                label="模型文件"
                :rules="formRules.modelConfigPath(index)"
                :prop="'modelConfig.' + index + '.filepath.path'"
              >
                <CustomUpload
                  class="bgm-upload"
                  v-model="item.filepath"
                  scope="provider_config"
                  @upload="(status) => handleUploadStatusChange('modelConfig', index, status)"
                  @change="(file) => handleUploadChange('modelConfig', index, file)"
                  :show-file-list="false"
                  list-type="picture-card"
                  :maxSize="50"
                >
                  <template v-if="item.filepath.path">
                    <div class="card-bgm-uploaded flex items-center">
                      <div
                        class="w-16 h-16 flex justify-center items-center rounded bg-card-bg-color"
                      >
                        <img alt="图片" :width="36" :height="36" :src="fileIcon" />
                      </div>
                      <div class="right-section ml-4 flex w-0 flex-1 flex-col justify-between">
                        <div
                          class="text-sm file-name text-start pr-2 break-words"
                          v-text="item.filepath.name"
                        ></div>
                        <el-button
                          class="bg-transparent"
                          type="primary"
                          :text="true"
                          @click="submitUpload(index)"
                          >重新上传</el-button
                        >
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <div class="bgm-card">
                      <img alt="图片" :src="uploadIcon" />
                      <div class="el-upload__text">点击或拖拽文件上传 </div>
                    </div>
                  </template>
                  <template #tip>
                    <div class="el-upload__tip"> 小于50M的文件 </div>
                  </template>
                </CustomUpload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-button type="danger" :text="true" @click="handleDeleteMotel(index)">
            <el-icon class="el-icon--left">
              <Delete />
            </el-icon>
          </el-button>
        </div>
        <div class="flex justify-center m-5">
          <el-button type="primary" @click="handleAddModel">
            <el-icon class="el-icon--left"> <Plus /> </el-icon>新增模型
          </el-button>
        </div>
      </template>
    </el-card>
  </el-form>
  <div class="content-bottom">
    <el-button @click="goBack">返回</el-button>
    <el-button type="primary" :disabled="submitStatus" @click="handleSubmit(partnerFormRef)"
      >确认提交</el-button
    >
  </div>
</template>
<script lang="ts" setup>
  import {
    createAbilityProvider,
    editAbilityProvider,
    getAbilityProvider,
  } from '@/api/ability-provider';
  import { useUserStore } from '@/store/modules/user';
  import { validatePhone } from '@/utils/validator';
  import { Delete, Plus } from '@element-plus/icons-vue';
  import type { FormInstance } from 'element-plus';
  import { onMounted, computed, reactive, ref, toRaw, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import { useTimeoutFn } from '@vueuse/core';
  import uploadIcon from '@/assets/icons/upload.svg';
  import fileIcon from '@/assets/icons/file.svg';
  import iconUpload from '@/assets/icons/iconUpload.svg';
  import CustomUpload from '@/views/operator/components/CustomUpload.vue';
import { useFilterInputHander } from '../hooks';

  const userStore = useUserStore();

  // 合作伙伴风格列表
  const styleOptions = ref([] as { id: number; title: string }[]);
  const router = useRouter();
  const route = useRoute();
  const uploads = ref<any>([]);
  const uploadStatus = ref<{ icon: boolean[]; modelConfig: boolean[]; toolConfig: boolean[] }>({
    icon: [],
    modelConfig: [],
    toolConfig: [],
  }); // 保存上传状态
  const uploadIconRef = ref();
  const isEdit = ref(false); // 是否处于编辑态
  type FileType = {
    name: string;
    path: string;
    url: string;
  };

  type ToolConfig = {
    description?: string;
    name: string;
    style_id?: number | string;
    filepath: FileType;
  };

  const submitStatus = computed(() => {
    const iconSttatus = uploadStatus.value.icon.some((value) => !!value);
    const toolConfigStatus = uploadStatus.value.toolConfig.some((value) => !!value);
    const modelConfigStatus = uploadStatus.value.modelConfig.some((value) => !!value);
    return iconSttatus || toolConfigStatus || modelConfigStatus;
  });
  const partnerFormRef = ref<FormInstance>();

  interface FormData {
    title: string;
    icon: FileType;
    address: string;
    phone: string;
    description: string;
    identifier: string;
    config: {
      modelDocument: string;
      modelDescription: string;
      bindingDocument: string;
      bindingDescription: string;
      animationDocument: string;
      animationDescription: string;
    };
    toolConfig: ToolConfig[];
    modelConfig: ToolConfig[];
  }

  const validateToolConfigName = (index) => (rule, value, callback) => {
    if (partnerForm.toolConfig[index]?.filepath?.path) {
      if (!value) {
        return callback(new Error('请输入工具名称'));
      }
    }
    callback();
  };

  const validateToolConfigFile = (index) => (rule, value, callback) => {
    if (partnerForm.toolConfig[index]?.name) {
      if (!value) {
        return callback(new Error('请上传工具文件'));
      }
    }
    callback();
  };

  const validateModelConfigName = (index) => (rule, value, callback) => {
    if (partnerForm.modelConfig[index]?.filepath?.path) {
      if (!value) {
        return callback(new Error('请输入模型名称'));
      }
    }
    callback();
  };

  const validateModelConfigStyleId = (index) => (rule, value, callback) => {
    if (styleOptions.value.length == 0) {
      return callback(new Error('请先到风格管理配置风格,再到此选择风格!'));
    } else {
      callback();
    }
    /* if (partnerForm.modelConfig[index]?.style_id) {
      if (!value) {
        return callback(new Error('请选择风格类型'));
      }
    } */
  };
  const validateModelConfigPath = (index) => (rule, value, callback) => {
    if (partnerForm.modelConfig[index]?.name) {
      if (!value) {
        return callback(new Error('请上传模型文件'));
      }
    }
    callback();
  };

  const handleUploadChange = (type, index, result) => {
    useTimeoutFn(() => partnerFormRef.value?.validateField([`${type}.${index}.filepath.path`]), 0);
  };

  const handleUploadStatusChange = (type, index, status) => {
    uploadStatus.value[type][index] = status;
  };

  const partnerForm = reactive<FormData>({
    title: '',
    icon: {} as FileType,
    address: '',
    phone: '',
    description: '',
    identifier: '',
    config: {
      modelDocument: '',
      modelDescription: '',
      bindingDocument: '',
      bindingDescription: '',
      animationDocument: '',
      animationDescription: '',
    },
    toolConfig: [],
    modelConfig: [],
  });
  const handleInput = useFilterInputHander(partnerForm);

  const formRules = reactive({
    title: [
      { required: true, message: '请输入伙伴名称', trigger: 'blur' },
    ],
    address: [{ max: 100, message: '联系地址长度不能超过100个字符', trigger: ['blur', 'change'] }],
    icon: [
      {
        required: true,
        message: '请上传封面icon',
        trigger: 'change',
      },
    ],
    phone: [
      {
        validator: validatePhone,
        trigger: 'change',
      },
    ],
    identifier: [
      {
        required: true,
        message: '请输入合作伙伴唯一标识符',
        trigger: 'change',
      },
      { max: 100, message: '合作伙伴唯一标识符长度不能超过100个字符', trigger: ['blur', 'change'] },
    ],
    description: [
      { max: 150, message: '伙伴描述长度不能超过150个字符', trigger: ['blur', 'change'] },
    ],
    modelDocument: [
      { max: 1000, message: '模型文档长度不能超过1000个字符', trigger: ['blur', 'change'] },
    ],
    modelDescription: [
      { max: 150, message: '模型简介长度不能超过150个字符', trigger: ['blur', 'change'] },
    ],
    bindingDocument: [
      { max: 1000, message: '绑定文档长度不能超过1000个字符', trigger: ['blur', 'change'] },
    ],
    bindingDescription: [
      { max: 150, message: '绑定简介长度不能超过150个字符', trigger: ['blur', 'change'] },
    ],
    animationDocument: [
      { max: 1000, message: '动画文档长度不能超过1000个字符', trigger: ['blur', 'change'] },
    ],
    animationDescription: [
      { max: 150, message: '动画简介长度不能超过150个字符', trigger: ['blur', 'change'] },
    ],
    toolConfigName(index) {
      return [
        { required: true, message: '请输入工具名称', trigger: 'blur' },
        { validator: validateToolConfigName(index), trigger: ['blur', 'change'] },
      ];
    },
    toolConfigPath(index) {
      return [
        { required: true, message: '请上传工具文件', trigger: ['change'] },
        { validator: validateToolConfigFile(index), trigger: ['blur', 'change'] },
      ];
    },
    toolConfigDescription: [
      { max: 150, message: '工具说明长度不能超过150个字符', trigger: ['blur', 'change'] },
    ],
    modelConfigName(index) {
      return [
        { required: true, message: '请输入模型名称', trigger: ['blur', 'change'] },
        { validator: validateModelConfigName(index), trigger: ['blur', 'change'] },
      ];
    },
    modelConfigStyleId(index) {
      return [
        { validator: validateModelConfigStyleId(index), trigger: 'change' },
        { required: true, message: '请选择风格类型', trigger: 'change' },
      ];
    },
    modelConfigPath(index) {
      return [
        { required: true, message: '请上传模型文件', trigger: ['blur', 'change'] },
        { validator: validateModelConfigPath(index), trigger: ['blur', 'change'] },
      ];
    },
  });
  const submitUploadIcon = () => {
    uploadIconRef?.value?.submit();
  };
  const submitUpload = (index) => {
    uploads.value?.[index]?.submit();
  };

  const fetchDetail = async (id) => {
    const { code, data: result } = await getAbilityProvider(id);
    const {
      address,
      config,
      description,
      icon,
      icon_url,
      identifier,
      phone,
      title,
      modelConfig,
      modelStyles = [],
      toolConfig,
    } = result;
    styleOptions.value = modelStyles;
    const tool = toolConfig.map(({ name, description, filename, filepath, fileurl }) => {
      return {
        name,
        description,
        filename,
        filepath: {
          name: filename,
          path: filepath,
          url: fileurl,
        },
      };
    });
    const model = modelConfig.map(({ name, style_id, filename, filepath, fileurl }) => {
      return {
        name,
        style_id,
        filename,
        filepath: {
          name: filename,
          path: filepath,
          url: fileurl,
        },
      };
    });
    partnerForm.title = title;
    partnerForm.address = address;
    partnerForm.description = description;
    partnerForm.icon = { path: icon, url: icon_url } as FileType;
    partnerForm.identifier = identifier;
    partnerForm.phone = phone;
    partnerForm.config = { ...config };
    partnerForm.toolConfig = tool;
    partnerForm.modelConfig = model;
    uploadStatus.value.icon = [false];
    uploadStatus.value.toolConfig = tool?.map(() => false) ?? [];
    uploadStatus.value.modelConfig = model?.map(() => false) ?? [];
  };
  watch(partnerForm, (v) => {
    console.log('partnerForm', v);
  })
  onMounted(() => {
    if (route.query?.id) {
      isEdit.value = true;
      fetchDetail(route.query.id);
    }
  });

  const handleAddTool = () => {
    partnerForm.toolConfig.push({
      name: '',
      description: '',
      filepath: {},
    });
    uploadStatus.value.toolConfig.push(false);
  };

  const handleAddModel = () => {
    partnerForm.modelConfig.push({
      name: '',
      style_id: '',
      filepath: {},
    });
    uploadStatus.value.modelConfig.push(false);
  };

  const handleDeleteTool = (index) => {
    partnerForm.toolConfig.splice(index, 1);
    uploadStatus.value.toolConfig.splice(index, 1);
  };
  const handleDeleteMotel = (index) => {
    partnerForm.modelConfig.splice(index, 1);
    uploadStatus.value.modelConfig.splice(index, 1);
  };

  const goBack = () => {
    router.push({
      path: '/partner/list',
    });
  };
  // 组合新增和编辑提交的数据
  const generateParams = () => {
    const { icon, config, toolConfig, modelConfig, ...other } = toRaw(partnerForm);
    const tool = toolConfig.map(({ filepath, ...other }) => ({
      ...other,
      filepath: filepath?.path,
    }));
    const model = modelConfig.map(({ filepath, ...other }) => ({
      ...other,
      filepath: filepath?.path,
    }));
    const params = {
      ...other,
      icon: icon?.path,
      config,
      toolConfig: tool,
      modelConfig: model,
    };
    return params;
  };
  //新增
  const handleAddPartner = async () => {
    const params = generateParams();
    if (!params.icon) {
      ElMessage({
        message: '封面icon不能为空',
        type: 'warning',
      });
      return;
    }
    const { code, data } = await createAbilityProvider(params);
    if (code == 0) {
      ElMessage({
        message: '新增合作伙伴成功,请到风格管理配置新合作伙伴的风格!',
        type: 'success',
      });
      router.replace({
        path: '/partner/list',
      });
    }
  };

  //编辑
  const handleEditPartner = async () => {
    const id = route.query?.id;
    if (!id) {
      return;
    }
    const params = generateParams();
    if (!params.icon) {
      ElMessage({
        message: '封面icon不能为空',
        type: 'warning',
      });
      return;
    }
    const { code } = await editAbilityProvider(id, params);
    if (code == 0) {
      ElMessage({
        message: '编辑合作伙伴成功',
        type: 'success',
      });
      router.replace({
        path: '/partner/list',
      });
    }
  };

  const handleSubmit = async (formEl: FormInstance | undefined) => {
    try {
      if (!formEl) return;
      const valid = await formEl.validate();
      if (valid) {
        if (isEdit.value) {
          //编辑
          await handleEditPartner();
        } else {
          //新增
          await handleAddPartner();
        }
      }
      // 更新本地合作伙伴缓存数据
      await userStore.getProviders();
    } catch (e) {console.log(e)}
  };
</script>

