<template>
  <div id="preview-page" style="display: flex; flex-direction: column; height: calc(100vh - 100px)">
    <template v-if="route.query.mode === 'add' || route.query.mode === 'edit'">
      <AvatarDressup v-if="!loading" :style_id="form.style_id" :avatarConfig="avatarConfigRef"
        :currentProviderId="currentProviderId" @handleDressupChange="handleDressupChange" />
      <el-form :model="form" label-width="100px" style="background: #fff; margin-bottom: 10px; padding-top: 20px"
        :inline="true">
        <el-form-item label="性别选择">
          <el-select v-model="form.gender" @change="resetAvatar" :disabled="!!route.query.avatarId || loading">
            <el-option label="男性" value="male" />
            <el-option label="女性" value="female" />
          </el-select>
        </el-form-item>
        <el-form-item label="风格类型">
          <el-select v-model="form.style_id" @change="resetAvatar" :disabled="!!route.query.avatarId || loading">
            <el-option v-for="item in styles" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
    </template>
    <template v-if="route.query.mode === 'audit' || route.query.mode === 'detail'">
      <BetterDescriptions :form="form" :columns="columns" />
    </template>
    <div style="flex-grow: 1;overflow: hidden;display: flex;justify-content: center;overflow-x: auto;">
      <div style="height: 100%;flex-grow: 1;">
        <div id="fu-renderkit-container" ref="containerRef" v-loading="loading" element-loading-text="人物渲染中，请勿离开当前页面..."
          element-loading-background="white">
        </div>
      </div>
      <div v-loading="loading" v-show="route.query.mode === 'template-add' || route.query.mode === 'template-edit'">
        <TemplateHandler ref="templateHandler" :tempData="tempData" :gender="genderRef" :resetAvatar="resetAvatar"
          :avatar="avatar" />
      </div>
    </div>

    <div class="page-footer" v-if="route.query.mode !== 'preview'">
      <el-button v-if="route.query.mode !== 'template-preview'" @click="() => {
        router.push({
          name: route.meta.activeMenu as string,
        });
      }" :disabled="loading">
        返回
      </el-button>
      <template v-if="route.query.mode === 'audit'">
        <RejectButton :backpath="route.meta.activeMenu"
          :id="route.query.avatarId || route.query.bundleId || route.query.templateId"
          :postFn="route.query.avatarId ? auditAvatarPublish : route.query.bundleId ? auditAssetPublish : auditTemplatePublish" />
        <el-popconfirm title="确定审核通过吗？" @confirm="handleAudit" width="200px">
          <template #reference>
            <el-button>通过</el-button>
          </template>
        </el-popconfirm>
      </template>
      <el-button v-if="route.query.mode === 'template-add' || route.query.mode === 'template-edit'" @click="printshot"
        :disabled="loading">截取封面</el-button>
      <el-button v-if="route.query.mode === 'template-add' || route.query.mode === 'template-edit'" type="primary"
        @click="saveTemplate" :loading="saveLoading"
        :disabled="loading || (route.query.mode === 'template-add' && !tempData.imgBlob)" title="请截取封面后再保存">
        <span :style="{ color: '#fff' }">保存</span>
      </el-button>
      <el-button :loading="previewLoading" v-if="route.meta.activeMenu === 'template-list'" :disabled="loading"
        @click="previewTemplate">预览</el-button>
    </div>

    <n-button v-if="route.query.mode === 'add' || route.query.mode === 'edit'" class="apply" round size="small"
      type="primary" @click="() => {
      dialogVisible = true;
    }
      " :style="{
      width: '50pt',
      height: '30pt',
      'font-size': '12pt',
    }">
      <span :style="{ color: '#fff' }">保存</span>
    </n-button>

    <el-dialog v-model="dialogVisible" width="30%" :before-close="dialogClose" :closable="false" title="保存形象">
      <el-form ref="formRef" label-width="60px" :model="form"
        :rules="{ name: [{ required: true, message: '请输入名称', trigger: 'blur' }] }">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入形象名称" maxlength="100" :disabled="!!route.query.avatarId"
            @input="(value) => handleInput('name', value)" />
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input type="textarea" v-model="form.description" placeholder="请输入备注" maxlength="100" :rows="6"
            @input="(value) => handleInput('description', value)" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogClose">取消</el-button>
          <el-button type="primary" @click="handleApply">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onBeforeUnmount, ref, Ref, inject, watch, toRaw } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { AvatarDressup, TemplateHandler } from './components';
import {
  addAvatar,
  editAvatar,
  getAvatarDetail,
  getAuditAvatarDetail,
  auditAvatarPublish,
} from '@/api/material/avatar';
import { reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { getAuditAssetDetail, auditAssetPublish, getAssetDetail } from '@/api/material/material';
import moment from 'moment';
import { RESOURCE_MANAGER, TALK_INS } from '@/components/AvatarRender';
import { useAudio, useFilterInputHander } from '@/views/hooks';
import { EAssetStatus } from '../avatar/columns';
import BetterDescriptions from '@/components/BetterDescriptions/BetterDescriptions.vue';
import { provide } from 'vue';
import { auditTemplatePublish, editTemplate, getTemplateDetail } from '@/api/material/template';

export default defineComponent({
  name: 'Preview',
});

function transMode(mode) {
  console.log('mode', mode)
  switch (mode) {
    case 'add':
      return '新增形象';
    case 'edit':
      return '编辑形象';
    case 'audit':
      return '审核';
    case 'detail':
      return '详情';
    case 'template-add':
      return '新增模板';
    case 'template-edit':
      return '编辑模板';
    default:
      return '预览';
  }
}

</script>
<script lang="ts" setup>
import { computed, h } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { storeToRefs } from 'pinia';
import RejectButton from './components/RejectButton.vue';
import { useTitle } from '@vueuse/core';
import { ModifiedRenderer, ModifiedAvatar, ModifiedScene } from "@/packages/sdk/fu_render_kit/lib/RenderKitSDK"
import { printshotAvatar } from '@/utils/printShotAvatar'

const userStore = useUserStore() as any;
const styles = userStore.styles;
const defaultStyle = userStore.defaultStyle;
const currentProviderId = userStore.currentProviderId;
const isCpUser = userStore.isCpUser;
const containerRef = ref<HTMLDivElement | undefined>();
const rendererRef = inject('renderer') as Ref<ModifiedRenderer>;
const sceneRef = inject('scene') as Ref<ModifiedScene>;
const loading = ref(true);
const title = useTitle();
const route = useRoute();
console.log('route', route)
const columnSpan = route.query.mode === 'audit' ? 6 : 8
const form = reactive({
  style_id: -1,
  gender: 'male',
  provider: {
    title: '',
  },
  description: '',
  user: {
    name: '',
  },
  name: '',
  created_at: '',
  revising_reason: '',
  status: 0,
  type: '',
  url: '',
});

const columns = computed(() => [
  {
    label: route.query.bundlePath ? '资源名称' : route.query.templateId ? '模板名称' : '形象名称',
    span: columnSpan,
    data: form.name,
  },
  {
    label: '归属合作伙伴',
    span: columnSpan,
    data: form.provider.title,
  },
  {
    label: 'CP制作者',
    span: columnSpan,
    data: form.user?.name,
  },
  {
    label: '提交时间',
    span: columnSpan,
    data: moment(form.created_at).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    label: '驳回原因',
    span: columnSpan * 2,
    hide: form.status !== EAssetStatus.Rejected,
    data: form.revising_reason
  },
  {
    label: '素材类型',
    span: columnSpan,
    hide: !route.query.bundlePath,
    data: route.query.name
  },
  {
    label: '下载资源',
    span: columnSpan,
    hide: !route.query.bundlePath,
    data: () => h(
      'a',
      {
        href: form.url,
      },
      '点击下载'
    )
  },
  {
    label: '备注',
    span: columnSpan * 2,
    data: form.description,
    hide: route.query.templateId,
  },
])
const handleInput = useFilterInputHander(form);

let avatar: ModifiedAvatar | undefined = undefined;
let renderer = toRaw(rendererRef.value);
let scene = toRaw(sceneRef.value)
let avatarConfig;
const router = useRouter();
const { gender = 'male', bundlePath, type } = route.query;
const avatarRef = ref<ModifiedAvatar>()
provide('avatar', avatarRef)

async function initAvatar(avatarConfig?) {
  if (!containerRef.value || !scene) return;
  const genderIndex = Math.max(
    RESOURCE_MANAGER.defaultAvatarList.findIndex((i) => i.gender === gender),
    0
  );
  await scheduleUpdateAvatar(avatarConfig || RESOURCE_MANAGER.defaultAvatarList[genderIndex]);
  if (bundlePath) {
    const buffer = await RESOURCE_MANAGER.downloadAsset(
      bundlePath,
    )
    RESOURCE_MANAGER._writeFs(buffer, bundlePath)
  }
  initAvatarRotate();
  startRenderLoop();
  renderer.resize(2);
  if (bundlePath) {
    handleBundle(type, bundlePath);
  }
}
let previousFrameTimestamp;
let animationId;
function draw(timestep) {
  const elapsed = timestep - previousFrameTimestamp;
  if (elapsed >= 16) {
    // 自定义渲染间隔
    previousFrameTimestamp = timestep;
    update()
    renderer.render(scene, elapsed / 1000);
  }
}
// 嘴部动作
const inTalkSessionRef = ref(false)
provide('inTalkSessionRef', inTalkSessionRef)
const update = (flag = 1) => {
  if (avatar && inTalkSessionRef.value) {
    const exp = TALK_INS.getCurrentExpression(flag);
    console.log('exp', exp)
    avatar.setAvatarMouthBlendShape(exp);
  }
}
function tick(timestamp) {
  draw(timestamp);
  animationId = window.requestAnimationFrame(tick);
}

function startRenderLoop() {
  previousFrameTimestamp = performance.now();
  tick(previousFrameTimestamp);
  setTimeout(() => {
    loading.value = false;
  }, 500);
}

function stopRenderLoop() {
  if (animationId) {
    window.cancelAnimationFrame(animationId);
    animationId = undefined;
  }
}

let touchstartListener, touchmoveListener, touchendListener;
function initAvatarRotate() {
  const canvas = document.getElementById('canvas');
  if (!canvas) return;
  let originX = 0;
  let originRotation = 0;
  if (touchstartListener) {
    canvas.removeEventListener('mousedown', touchstartListener);
    canvas.removeEventListener('mousemove', touchmoveListener);
    canvas.removeEventListener('mouseup', touchendListener);
  }
  touchstartListener = (event) => {
    if (!canvas) return;
    originX = event.pageX;
    if (avatar) {
      originRotation = avatar.getRotate()[1];
    }
    canvas.addEventListener('mousemove', touchmoveListener);
    canvas.addEventListener('mouseup', touchendListener);
  };
  canvas.addEventListener('mousedown', touchstartListener);

  if (touchmoveListener) {
    canvas.removeEventListener('mousemove', touchmoveListener);
  }

  touchmoveListener = (event) => {
    let currentX = event.pageX;
    let x = currentX - originX;
    if (avatar) {
      avatar.setRotate([0, originRotation + (x / 150) * 180, 0]);
    }
  };

  touchendListener = () => {
    if (!canvas) return;
    canvas.removeEventListener('mousemove', touchmoveListener);
    canvas.removeEventListener('mouseup', touchendListener);
  };
}

const avatarConfigRef = ref<any>(null);
provide('avatarConfig', avatarConfigRef)
async function scheduleUpdateAvatar(avatarConfig) {
  if (!scene) return
  console.log('performance start', performance.now())
  if (avatar) {
    destroyAvatar(avatar);
  }
  avatar = await scene.customAddAvatarAsync({ data: avatarConfig });
  console.log('performance end', performance.now())
  avatarRef.value = avatar
  avatar.setAnimationSync(
    `${avatarConfig.gender}_huxi`
  );
  avatarConfigRef.value = avatarConfig;
}
// 注销avatar
function destroyAvatar(avatar) {
  scene?.removeAvatar(avatar);
  avatar = null;
}

// 筛选风格\性别
async function resetAvatar(gender = form.gender) {
  return new Promise(r => {
    const genderIndex = RESOURCE_MANAGER.defaultAvatarList.findIndex((i) => i.gender === gender);
    avatarConfigRef.value = null;
    form.gender = gender
    loading.value = true;
    setTimeout(async () => {
      await scheduleUpdateAvatar(RESOURCE_MANAGER.defaultAvatarList[genderIndex]);
      loading.value = false;
      r(true)
    }, 100)
  })
}

async function handleDressupChange(type, name?, value?, cb?) {
  if (!avatar) return
  if (type === 'color') {
    avatar.setColor(name, value);
  } else if (type === 'boneTransform') {
    avatar.setBoneTransform(name, value);
  } else if (type === 'save') {
    avatar.setRotate([0, 0, 0]);
    save();
  } else {
    const list = await avatar.addComponentsAsync([value]);
    cb && cb(list);
  }
}

async function handleBundle(type, path) {
  if (!avatar) return
  scene.clearBackground()
  if (type === 'animation') {
    await avatar.setAnimationSync(path);
  } else if (type === 'background') {
    await scene.addBackground(path);
  } else if (type === 'foreground') {
    await scene.addForeground(path);
  } else if (type === 'light') {
    await scene.addLight(path);
  } else if (type === 'camera') {
    await scene.setCamera(path);
  } else {
    await avatar.addComponents([path]);
  }
}

// 保存形象
const formRef = ref<any>(null);
const dialogVisible = ref(false);
function handleApply() {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      handleDressupChange('save');
      dialogClose();
    }
  });
}

function dialogClose() {
  dialogVisible.value = false;
}
async function save() {
  if (!form.name) {
    ElMessage({
      message: '请填写名称！',
      type: 'error',
    });
  }
  if (!avatar) return
  const imgBlob: any = await printshotAvatar(renderer);
  const formData = new FormData();

  formData.append('config_json', avatar.getJson());
  formData.append('icon', imgBlob);
  formData.append('description', form.description);
  if (route.query.avatarId) {
    await editAvatar(route.query.avatarId, formData);
    ElMessage({
      message: '修改成功！',
      type: 'success',
    });
  } else if (route.query.templateId) {
    await editTemplate(route.query.templateId, formData);
    ElMessage({
      message: '修改成功！',
      type: 'success',
    });
  } else {
    formData.append('name', form.name);
    formData.append('provider_id', currentProviderId.value || ('1' as any));
    formData.append('style_id', form.style_id as unknown as string);
    formData.append('gender', JSON.parse(avatar.getJson()).gender);
    await addAvatar(formData);
    ElMessage({
      message: '新增成功！',
      type: 'success',
    });
  }
  router.push({
    name: 'avatar-list',
  });
}

// 审核
async function handleAudit() {
  if (route.query.avatarId) {
    await auditAvatarPublish(route.query.avatarId, {
      audit_result: '1',
    });
  } else if (route.query.templateId) {
    await auditTemplatePublish(route.query.templateId, {
      audit_result: '1',
    })
  } else {
    await auditAssetPublish(route.query.bundleId, {
      audit_result: '1',
    });
  }
  ElMessage({
    message: '审核成功！',
    type: 'success',
  });
  router.push({
    name: route.query.bundleId ? 'material-list' : route.query.templateId ? 'template-list' : 'avatar-list',
  });
}

const genderRef = computed(() => form.gender)

const templateHandler = ref<any>(null)
const saveLoading = ref(false)
async function saveTemplate() {
  try {
    saveLoading.value = true
    await templateHandler.value.save()
  } catch (error) {
    console.log(error)
  } finally {
    saveLoading.value = false
  }
}

const { pause, setSrc, isPlaying, currentBgm } = useAudio();
provide('currentBgm', currentBgm)
provide('isPlaying', isPlaying)
provide('setSrc', setSrc)
provide('pause', pause)
const previewLoading = ref(false)
provide('previewLoading', previewLoading)
async function previewTemplate() {
  const genderIndex = ['male', 'female'].findIndex((i) => i === form.gender);
  if (genderIndex === -1) {
    ElMessage.error('形象性别错误！')
    return
  }
  if (
    tempData.text.trim().length === 0
    && !tempData.animation[genderIndex]
  ) {
    ElMessage.error('请输入文本或者选择动作！')
    return
  }
  ElMessage({
    message: '预览中请勿离开当前页面',
    type: 'warning',
  });
  pause()
  setSrc('')
  await templateHandler.value.getTtsModule()
}

async function printshot() {
  tempData.imgBlob = await printshotAvatar(renderer, 300, 647);
}

watch(rendererRef, async () => {
  if (rendererRef.value && sceneRef.value) {
    renderer = toRaw(rendererRef.value);
    scene = toRaw(sceneRef.value)
    initAvatar(avatarConfig);
  }
});

const tempData = reactive<any>({
  name: '',
  animation: ['', ''],
  background: '',
  foreground: '',
  music: '',
  musicId: '',
  text: '',
  imgBlob: null
})
onMounted(async () => {
  // 修改页面标题
  title.value = transMode(route.query.mode);
  
  const dom = document.getElementById('canvas');
  const container = document.getElementById('fu-renderkit-container');
  if (route.query.mode === 'preview' || route.query.mode === 'template-preview') {
    const sider: any = document.getElementsByClassName('layout-sider')[0];
    const header: any = document.getElementsByClassName('n-layout-header')[0];
    const layout: any = document.getElementsByClassName('layout-content')[0];
    const content: any = document.getElementsByClassName('layout-content-main')[0];
    const previewPage: any = document.getElementById('preview-page');
    sider.style.display = 'none';
    header.style.display = 'none';
    content.style['padding-top'] = '0';
    previewPage.style.height = '100%';
    layout.style['min-height'] = 'unset';
    if (container) {
      container.style.height = '550px';
      container.style.width = '255px';
    }
  }
  if (dom && container) {
    setTimeout(() => {
      container?.appendChild(dom);
    }, 500)
    dom.style.display = 'block';
    dom.style.width = container.clientWidth + 'px';
    dom.style.height = container.clientHeight + 'px';
  }

  let res;
  if (route.query.mode === 'audit' || !isCpUser.value) {
    if (route.query.bundleId) {
      res = await getAuditAssetDetail(route.query.bundleId);
    } else if (route.query.avatarId) {
      res = await getAuditAvatarDetail(route.query.avatarId);
    } else if (route.query.templateId) {
      res = await getTemplateDetail(route.query.templateId);
    }
  } else if (route.query.avatarId) {
    res = await getAvatarDetail(route.query.avatarId);
  } else if (route.query.bundleId) {
    res = await getAssetDetail(route.query.bundleId);
  } else if (route.query.templateId) {
    res = await getTemplateDetail(route.query.templateId);
  }
  console.log('res', res)
  if (res) {
    form.style_id = res.data.style_id;
    form.gender = res.data.gender || 'male';
    form.provider = res.data.provider;
    form.description = res.data.description;
    form.user = res.data.user;
    form.name = res.data.name;
    form.created_at = res.data.created_at;
    form.revising_reason = res.data.revising_reason;
    form.status = res.data.status;
    form.type = res.data.type;
    form.url = res.data.url;
    avatarConfig = res.data.config_json;
    if (res.data.data) {
      tempData.name = res.data.name
      tempData.text = res.data.data.extra.text
      tempData.background = res.data.data.extra.background
      tempData.foreground = res.data.data.extra.foreground
      tempData.music = res.data.data.extra.music
      tempData.musicId = res.data.data.extra.musicId
      tempData.animation = res.data.data.extra.animation
    }
  } else {
    form.style_id = defaultStyle.value as any;
  }
  if (rendererRef.value && sceneRef.value) {
    initAvatar(avatarConfig);
  }
});

onBeforeUnmount(() => {
  const dom = document.getElementById('canvas');
  const body = document.querySelector('body');
  if (body && dom) {
    dom.style.display = 'none';
    body.appendChild(dom);
  }
  destroyAvatar(avatar);
  stopRenderLoop();
  const canvas = document.getElementById('canvas');
  if (canvas) {
    canvas.removeEventListener('mousedown', touchstartListener);
    canvas.removeEventListener('mousemove', touchmoveListener);
    canvas.removeEventListener('mouseup', touchendListener);
  }
});
</script>
<style lang="less" scoped>
.apply {
  position: absolute;
  right: 30px;
  top: 200px;
}

#fu-renderkit-container {
  aspect-ratio: 720/1280;
  height: 100%;
  margin: 0 auto;

  canvas {
    height: 100%;
    width: 100%;
  }
}

.page-footer {
  background: #fff;
  text-align: center;
  margin-top: 10px;
  padding: 10px 0;

  button {}
}
</style>
<style>
.el-descriptions {
  background: #fff;
  padding: 20px;
}

#canvas {
  margin: 0 auto;
  background: #fff;
}
</style>
