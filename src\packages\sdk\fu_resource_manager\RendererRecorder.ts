
export type IHistory = ({
    type: 'body'
} | {
    type: 'dressup'
} | {
    type: 'color'
}) & {
    handler: (...args) => void
    oldValue: any
    newValue: any
}
interface IRecorderParams {
    maxLength: number
}
export class RendererRecorder {
    pointer: number
    maxLength: number
    history: IHistory[]
    subscription: Record<number, Function>
    callbackId: number
    constructor(params: IRecorderParams) {
        const {
            maxLength
        } = params
        this.maxLength = maxLength
        this.history = []
        this.pointer = -1
        this.subscription = {}; // 定义事件存储列表
        this.callbackId = 0; // 定义事件id  可以理解为公众号订阅者人数
    }
    get length() {
        return this.history.length
    }

    get isEmpty() {
        return this.history.length === 0
    }

    reset() {
        this.history = []
        this.pointer = -1
        this.emit()
    }

    backforward() {
        const result = this.history[this.pointer]
        this.pointer = Math.max(-1, --this.pointer)
        this.emit()
        return result
    }

    stepforward() {
        this.pointer = Math.min(this.maxLength - 1, ++this.pointer)
        this.emit()
        return this.history[this.pointer]
    }

    record(info: IHistory) {
        this.history.splice(this.pointer + 1)
        if (this.length === this.maxLength) {
            this.history.shift()
        }
        this.history.push(info)
        this.pointer = this.length - 1
        this.emit()
    }

    // 订阅组件更新
    sub(cb) {
        const callbackIndex = this.callbackId++
        this.subscription[callbackIndex] = cb
        return callbackIndex
    }

    unsub(id) {
        delete this.subscription[id]
    }

    // 触发组件更新
    emit() {
        Object.keys(this.subscription).forEach((fnKey) => {
            this.subscription[fnKey](this.history, this.pointer)
        })
    }

}