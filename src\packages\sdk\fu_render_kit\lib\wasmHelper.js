import FURenderKitSDK from "./FURenderKitSDK"
class RenderKitWasmLoader {
    module
    locateFile
    constructor() {
        this.locateFile = (path) => {
            return path?.startsWith('/')? `${import.meta.env.VITE_PUBLIC_PATH}${path}` : `${import.meta.env.VITE_PUBLIC_PATH}/${path}`
        }
    }

    async LoadWasm() {
        this.module = await FURenderKitSDK({
            locateFile: this.locateFile
        })
        return this.module
    }
}

export class WasmHelper {
    constructor(options) {
        this.wasmCtx = new RenderKitWasmLoader(options)
    }

    /**
     * 加载静态文件 返回
     * @param filename
     * @returns { ptr: 指针 size: 指针大小 }
     */
    async LoadAssets(filename) {
        // HACK:
        // let url = filename.includes("http") ? filename : "/assets/" + filename
        let url = filename
        const file_arraybuffer = await fetch(url)
            .then((res) => {
                if (!res.ok) {
                    throw 'EngineAssets download failed';
                }
                return res.arrayBuffer();
            })
        let file_buffer = new Uint8Array(file_arraybuffer)
        let file_heap8 = this.wasmCtx.module._malloc(file_buffer.length)
        this.wasmCtx.module.HEAP8.set(file_buffer, file_heap8)
        return {
            ptr: file_heap8,
            size: file_buffer.length,
        }
    }

    writeUTF8String(string) {
        if (!this.enc) {
            this.enc = new TextEncoder()
        }
        const file_buffer = this.enc.encode(string)
        let file_heap8 = this.wasmCtx.module._malloc(file_buffer.length)
        this.wasmCtx.module.HEAP8.set(file_buffer, file_heap8)
        return {
            ptr: file_heap8,
            size: file_buffer.length,
        }
    }
    writeAssets(uint8Array) {
        let file_heap8 = this.wasmCtx.module._malloc(uint8Array.length)
        this.wasmCtx.module.HEAP8.set(uint8Array, file_heap8)
        return {
            ptr: file_heap8,
            size: uint8Array.length,
        }
    }

    writeToFile(uint8Arr, path) {
        const index = path.lastIndexOf("/")
        const filename = path.substring(index + 1)
        if (index !== -1) {
            const dir = path.substring(0, index)
            try {
                Module.FS.stat(dir)
            } catch (s) {
                Module.FS.mkdirTree(dir)
            }
            Module.FS.writeFile(path, uint8Arr)
        }
    }

    /**
     * 转化函数
     */
    Float64ArrayToHeap(arr) {
        let float_array = new Float64Array(arr.length)
        for (let i = 0; i < arr.length; i++) {
            float_array[i] = arr[i]
        }
        let ret = this.wasmCtx.module._malloc(
            float_array.length * float_array.BYTES_PER_ELEMENT
        )
        this.wasmCtx.module.HEAPF64.set(float_array, ret >> 3)
        return {
            ptr: ret,
            size: arr.length,
        }
    }
    Int32ArrayToHeap(arr) {
        let int_array = new Int32Array(arr.length)
        for (let i = 0; i < arr.length; i++) {
            int_array[i] = arr[i]
        }
        let ret = this.wasmCtx.module._malloc(
            int_array.length * int_array.BYTES_PER_ELEMENT
        )
        this.wasmCtx.module.HEAP32.set(int_array, ret >> 2)
        return {
            ptr: ret,
            size: arr.length,
        }
    }
    // 释放molloc
    FreeMemory(ptr) {
        this.wasmCtx.module._free(ptr)
    }
}
