{"compilerOptions": {"target": "es2020", "module": "esnext", "moduleResolution": "node", "strict": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "strictFunctionTypes": false, "jsx": "preserve", "baseUrl": ".", "allowJs": true, "sourceMap": false, "esModuleInterop": true, "resolveJsonModule": true, "noUnusedLocals": true, "noUnusedParameters": true, "experimentalDecorators": true, "lib": ["dom", "esnext"], "typeRoots": ["./node_modules/@types/", "./types"], "noImplicitAny": false, "skipLibCheck": true, "noEmit": true, "paths": {"@/*": ["src/*"], "/#/*": ["types/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "components.d.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "**/*.js", "src/packages/**/*.js"]}