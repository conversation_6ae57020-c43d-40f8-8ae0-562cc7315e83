name: Release Publish

on:
  push:
    tags:
    - '*'

jobs:

  build:
    runs-on: windows-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18.x'
          registry-url: 'https://registry.npmjs.org'
      - run: npm install
      - run: npm run create_dist
      - uses: ncipollo/release-action@v1
        with:
          artifacts: "build/package/engine/*"
          bodyFile: ".github/workflows/release_template.md"
          draft: true
          token: ${{ secrets.GITHUB_TOKEN }}
