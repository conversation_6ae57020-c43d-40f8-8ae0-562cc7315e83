import Axios from "axios"

export const axios = function (config) {
    let configCom = {
        method: config.method || "get",
        headers: {
            // Authorization: !("authorization" in config) || config.authorization ? window.localStorage.getItem("token") : ''
        },
        ...config,
    }
    if (!configCom.url.startsWith('http')) {
        configCom.url = import.meta.env.VITE_GLOB_API_URL_PREFIX + configCom.url
    }

    return new Promise((resolve, reject) => {
        Axios(configCom)
            .then((res) => {
                resolve(res)
            })
            .catch((err) => {
                reject(err)
            })
    })
}

// 获取资源 arraybuffer
export const fetchArrayBufferByUrl = async function (url) {
    const res = await Axios.get(url, { responseType: "arraybuffer" });
    return res.data;
}

// 获取资源文本
export const fetchTextByUrl = function (url) {
    return Axios.get(url, { responseType: "text" });
}
