stages:
    - image-build-push
    - deploy

image-build-push:
    only:
        - tags
    stage: image-build-push
    script:
        - DOCKER_BUILDKIT=1 docker buildx build --rm --push -t harbor.faceunity.com/avatarx/jxyd/avatarx:${CI_BUILD_TAG} .

部署到测试开发环境:
    stage: deploy
    script:
        - date
        - ssh root@************* "kubectl set image deployment/avatarx-dev-v1 container-jxyd-avatarx-dev=harbor.faceunity.com/avatarx/jxyd/avatarx:${CI_BUILD_TAG} --namespace=jxyd"
        - sleep 10s
        - date
    rules:
        - if: $CI_COMMIT_TAG
          when: manual
