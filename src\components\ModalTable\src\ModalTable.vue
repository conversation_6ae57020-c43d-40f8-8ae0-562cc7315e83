<template>
  <el-dialog
    width="60%"
    v-model="visible"
    modal-class="modal-table"
    @close="onCloseModal"
    :title="getBindValue.title"
  >
    <template #default>
      <el-form>
        <el-form-item label="使用时间" style="width: 500px">
          <InnerMonthPicker
            v-model="queryParams.dateRange"
          />
        </el-form-item>
      </el-form>
      <el-card class="mt-4">
        <el-select v-model="chartType" placeholder="">
          <el-option label="折线图" value="line" />
          <el-option label="柱状图" value="bar" />
        </el-select>
        <div id="chart" ref="chartRef" style="width: 100%; height: 430px"></div>
      </el-card>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { getAuditDetail, getCpDetail } from '@/api/statistics';
  import { FormProps } from '@/components/Form';
  import { basicProps } from './props';
  import { deepMerge } from '@/utils';
  import { Ref, watch } from 'vue';
  import { computed, getCurrentInstance, reactive, ref, unref, useAttrs } from 'vue';
  import { ModalProps } from './type';
  import { useECharts } from '@/hooks/web/useECharts';

  import { format } from 'date-fns';
import { InnerMonthPicker } from '@/components/InnerMonthPicker';

  const role = ref('audit');

  const visible = ref(false);

  const attrs = useAttrs();
  const props = defineProps({ ...basicProps });

  const emit = defineEmits(['on-close', 'on-ok', 'register']);

  const propsRef = ref<Partial<ModalProps> | null>(null);

  const getProps = computed((): FormProps => {
    return { ...props, ...(unref(propsRef) as any) };
  });
  const chartRef = ref<HTMLDivElement | null>(null);

  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  // const { setOptions: setBarChartOptions } = useECharts(barChart as Ref<HTMLDivElement>);
  const chartType = ref('line');
  const lineChartOption = ref<any>({
    xAxis: {
      type: 'category',
      data: [],
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#959596',
      },
      axisLine: {
        lineStyle: {
          color: '#E5E5E5',
        },
      },
    },
    yAxis: {
      type: 'value',
      name: '使用次数',
      minInterval: 1,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      formatter: '使用次数: {c0}<br />时间: {b0}',
    },
    series: [
      {
        data: [],
        type: 'line',
        lineStyle: {
          color: '#407EFF',
        },
        symbol: 'circle',
        itemStyle: {
          color: '#407EFF',
        },
      },
    ],
  });
  watch(chartType, () => {
    lineChartOption.value.series[0].type = chartType;
    setOptions(lineChartOption.value);
  });
  async function setProps(modalProps: Partial<ModalProps>): Promise<void> {
    propsRef.value = deepMerge(unref(propsRef) || ({} as any), modalProps);
  }

  const getBindValue = computed(() => {
    return {
      ...attrs,
      ...unref(getProps),
      ...unref(propsRef),
    };
  });

  const queryParams = reactive({
    id: '',
    resource_type: 0,
    provider_id: props.data.provider_id,
    style_id: props.data.style_id,
    user_id: props.data.user_id,
    type: props.data.type,
    dateRange: [
      format(new Date(new Date().setDate(1)), 'yyyy-MM-dd'),
      format(new Date(), 'yyyy-MM-dd'),
    ],
  });

  const fetchAuditDetail = async () => {
    const { dateRange, ...other } = queryParams;
    const params = {
      ...other,
      create_start: dateRange[0],
      create_end: dateRange[1],
    };
    const {
      data: { rows },
    } = await getAuditDetail(params);
    const dates: string[] = [];
    const totals: number[] = [];
    rows.forEach(({ date, times }) => {
      dates.push(date);
      totals.push(times);
    });
    lineChartOption.value.xAxis.data = dates;
    lineChartOption.value.series[0].data = totals;
    setOptions(lineChartOption.value);
  };

  const fetchCpDetail = async () => {
    const { dateRange } = queryParams;
    const params = {
      create_start: dateRange[0],
      create_end: dateRange[1],
    };
    const {
      data: { rows },
    } = await getCpDetail(queryParams.id, params);
    const dates: string[] = [];
    const totals: number[] = [];
    rows.forEach(({ date, times }) => {
      dates.push(date);
      totals.push(times);
    });
    lineChartOption.value.xAxis.data = dates;
    lineChartOption.value.series[0].data = totals;
    setOptions(lineChartOption.value);
  };

  watch([() => queryParams.id, () => queryParams.dateRange], () => {
    if (visible.value) {
      fetchData();
    }
  });

  const fetchData = () => {
    if (role.value == 'cp') {
      fetchCpDetail();
    } else {
      fetchAuditDetail();
    }
  };

  function openModal(params) {
    queryParams.resource_type = params.resource_type;
    queryParams.provider_id = params.provider_id;
    queryParams.style_id = params.style_id;
    queryParams.user_id = params.user_id;
    queryParams.type = params.type;
    queryParams.id = params.id;
    if (params.role == 'cp') {
      role.value = 'cp';
    }
    visible.value = true;
  }

  function closeModal() {
    queryParams.resource_type = 0;
    queryParams.provider_id = '';
    queryParams.style_id = '';
    queryParams.user_id = '';
    queryParams.type = '';
    queryParams.id = '';
    queryParams.dateRange = [
      format(new Date(new Date().setDate(1)), 'yyyy-MM-dd'),
      format(new Date(), 'yyyy-MM-dd'),
    ];
    visible.value = false;
    emit('on-close');
  }

  function onCloseModal() {
    closeModal();
  }

  const modalMethods = {
    setProps,
    openModal,
    closeModal,
  };

  const instance = getCurrentInstance();
  if (instance) {
    emit('register', modalMethods);
  }
</script>
