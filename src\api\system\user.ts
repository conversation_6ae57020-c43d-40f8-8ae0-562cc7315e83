import { http } from '@/utils/http/axios';

export interface BasicResponseModel<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface BasicPageParams {
  pageNumber: number;
  pageSize: number;
  total: number;
}

/**
 * @description: 获取用户信息
 */
export function getUserInfo() {
  return http.request({
    url: '/business/user/info',
    method: 'get',
  });
}

/**
 * @description: 获取图片验证码
 */
export function getCaptcha() {
  return http.request({
    url: '/business/user/captcha',
    method: 'get',
  });
}

/**
 * @description: 获取图片验证码
 */
export function getPubPem() {
  return http.request({
    url: '/business/auth/publicPem',
    method: 'get',
  });
}


/**
 * @description: 获取手机验证码
 */
export function getMsgCode(params, { withToken = true } = {}) {
  return http.request(
    {
      url: '/business/user/msgcode',
      method: 'post',
      params,
    },
    {
      withToken,
    }
  );
}
/**
 * @description: 用户登录
 */
export function login(params) {
  return http.request<BasicResponseModel>(
    {
      url: '/business/user/login',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * @description: 用户退出
 */
export function logout() {
  return http.request<BasicResponseModel>(
    {
      url: '/business/user/logout',
      method: 'POST',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * @description: 重置密码
 */
export function resetPwd(params) {
  return http.request<BasicResponseModel>({
    url: '/business/user/password2reset',
    method: 'POST',
    params,
  });
}

/**
 * @description: 用户注册
 */
export function register(params) {
  return http.request<BasicResponseModel>(
    {
      url: '/business/user-apply',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * @description: CP列表
 */
export function getCpList(params) {
  return http.request<BasicResponseModel>(
    {
      url: '/business/user-apply/audit/list',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * @description: CP详情
 */
export function getCpDetail(id) {
  return http.request<BasicResponseModel>(
    {
      url: `/business/user-apply/${id}`,
      method: 'GET',
      params: { id },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * @description: 审核CP
 */
export function auditCp(id, params) {
  return http.request<BasicResponseModel>({
    url: `/business/user-apply/${id}/audit`,
    method: 'POST',
    params,
  });
}


/**
 * @description: 审核重置密码
 */
export function postResetPassword(id) {
  return http.request<BasicResponseModel>({
    url: `/business/user-apply/${id}/resetPassword`,
    method: 'POST',
  });
}

/**
 * @description: 审核编辑CP
 */
export function editCPUser(id, params) {
  return http.request<BasicResponseModel>({
    url: `/business/user-apply/${id}/edit`,
    method: 'POST',
    params,
  });
}

/**
 * @description: 用户修改密码
 */
export function changePassword(params) {
  return http.request({
    url: `/business/user/password`,
    method: 'POST',
    params,
  });
}

export function updatePhone(params) {
  return http.request(
    {
      url: `/business/user/phone`,
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

// 审核员冻结/恢复CP
export function freezeOrRecoverCp(id, params: { status: 1 | 2 }) {
  return http.request({
    url: `/business/user-apply/${id}/status`,
    method: 'POST',
    params,
  });
}

// 审核员删除CP
export function removeCp(id) {
  return http.request({
    url: `/business/user-apply/${id}/delete`,
    method: 'POST',
  });
}

// 获取用户列表
export function getUserList(params) {
  return http.request<BasicResponseModel>(
    {
      url: '/business/user/manage/list',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

// 新建用户
export function addUser(params) {
  return http.request<BasicResponseModel>({
    url: `/business/user/manage/user`,
    method: 'POST',
    params,
  });
}

// 编辑用户
export function editUser(id, params) {
  return http.request<BasicResponseModel>({
    url: `/business/user/manage/user/${id}`,
    method: 'POST',
    params,
  });
}

// 删除用户
export function deleteUser(id) {
  return http.request<BasicResponseModel>({
    url: `/business/user/manage/user/${id}/delete`,
    method: 'POST',
  });
}

// 获取用户详情
export function getUserDetail(id) {
  return http.request<BasicResponseModel>({
    url: `/business/user/manage/user/${id}`,
    method: 'GET',
  });
}

// 获取所有用户名
export function getAllUserNames() {
  return http.request<BasicResponseModel>({
    url: `/business/user/allnames`,
    method: 'GET',
  });
}
