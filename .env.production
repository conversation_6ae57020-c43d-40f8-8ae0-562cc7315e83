# 是否开启mock
VITE_USE_MOCK = true

# 网站根目录
VITE_PUBLIC_PATH =

# 网站前缀
VITE_BASE_URL = /

# 是否删除console
VITE_DROP_CONSOLE = true

# VITE_PROXY=[["/gassets","https://jxyd-test.faceunity.com:8086"],["/api","https://jxyd-test.faceunity.com:8086"]]
# VITE_PROXY=[["/api","http://*************:8090"],["/gassets","http://*************:8090"]]
VITE_PROXY=[["/api","http://127.0.0.1:8090"],["/gassets","http://127.0.0.1:8090"]]
# API
VITE_GLOB_API_URL =

# 图片上传地址
VITE_GLOB_UPLOAD_URL=/business/file/upload

# 图片前缀地址
VITE_GLOB_IMG_URL =

# 接口前缀
VITE_GLOB_API_URL_PREFIX=/api

# 是否启用gzip压缩或brotli压缩
# 可选: gzip | brotli | none
# 如果你需要多种形式，你可以用','来分隔
VITE_BUILD_COMPRESS = 'none'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false
