<template>
  <el-card :bordered="false" class="proCard" :style="{ marginBottom: '15px' }">
    <el-form :model="formParams" :rules="rules" ref="formRef" label-width="80px">
      <el-form-item label="形象风格">
        <el-radio-group v-model="formParams.style_id" name="style" v-if="isCpUser">
          <el-radio key="all" label="">全部</el-radio>
          <el-radio v-for="item in styles" :key="item.id" :label="item.id">
            {{ item.title }}
          </el-radio>
        </el-radio-group>
        <StyleCascader v-else v-model="formParams.style_id" />
      </el-form-item>
      <el-form-item label="适用范围">
        <el-radio-group v-model="formParams.gender">
          <el-radio label="">全部</el-radio>
          <el-radio label="male">男性</el-radio>
          <el-radio label="female">女性</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </el-card>
  <el-card :bordered="false" class="proCard">
    <el-form :rules="rules" ref="formRef" label-width="80px">
      <el-row>
        <el-col :span="4" v-if="!isCpUser">
          <el-form-item label="制作CP">
            <el-select v-model="formParams.user_id" placeholder="请选择制作CP">
              <el-option label="全部" value="" />
              <template v-for="item in allCpUsers" :key="item.id">
                
                <el-option :label="item.name" :value="item.id" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="形象状态">
            <el-select v-model="formParams.status" placeholder="请选择形象状态">
              
              <el-option label="全部" value="" />
              <template
                v-for="item in assetsSatusOptions.filter((item) => {
                  if (isCpUser) return true;
                  return item.value !== 1;
                })"
                :key="item.value"
              >
                <el-option :label="item.label" :value="item.value" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="创建日期">
            <el-date-picker
              v-model="formParams.dates"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              date-format="YYYY/MM/DD"
              clearable
              :disabled-date="(date: Date) => 
                {
                return date.getTime() > new Date().getTime()
              }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="" label-width="10">
            <el-input
              placeholder="请输入形象名称"
              clearable
              v-model="formParams.keyword"
              :prefix-icon="Search"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :style="{ textAlign: 'right' }">
          <el-button
            type="primary"
            @click="addTable"
            :icon="PlusOutlined"
            v-if="hasPermission([PERMISSION_KEYS.cpuser.asset_avatar_create])"
          >
            新建
          </el-button>
          <el-button
            type="primary"
            @click="handleExport"
            v-if="hasPermission([PERMISSION_KEYS.cpuser.asset_avatar_read])"
          >
            导出
          </el-button>
          <el-button type="danger" @click="handleBatchRemove" v-if="isCpUser">批量删除</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div class="material-table">
      <BasicTable
        :columns="avatarColumns"
        :request="loadDataTable"
        :row-key="(row: ListData) => row.id"
        ref="actionRef"
        :actionColumn="actionColumn"
        :scroll-x="1090"
      />
    </div>
  </el-card>
  <el-dialog v-model="dialogVisible" title="预览" width="900px" destroy-on-close>
    <VFrame :flowSrc="flowSrc" />
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  applyAvatarPublish,
  deleteAvatar,
  getAuditAvatarList,
  getAvatarList,
  revokeAvatarPublish,
  revisingAvatarPublish,
  batchDeleteAvatar,
} from '@/api/material/avatar';
import { BasicTable, TableAction } from '@/components/Table';
import { Search } from '@element-plus/icons-vue';
import { PlusOutlined } from '@vicons/antd';
import moment from 'moment';
import { type FormRules as NaiveFormRules } from 'naive-ui';
import { FormRules } from 'element-plus';
import { h, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { EAssetStatus, ListData } from './columns';

import { StyleCascader } from '@/components/StyleCascader';
import { usePermission } from '@/hooks/web/usePermission';
import { PERMISSION_KEYS, useUser } from '@/store/modules/user';
import { ElImage, ElMessage, ElMessageBox, ElTooltip } from 'element-plus';
import { storeToRefs } from 'pinia';
import { watch } from 'vue';
import { AssetStatus, AssetStatusColor, assetsSatusOptions } from '../material/columns';
import { useAllCpUsers, useExport } from '@/views/hooks';
// 风格
const userStore = useUser();
const { styles, currentProviderId, isCpUser } = storeToRefs(userStore);
const { hasPermission } = usePermission();
const { exportFile } = useExport();
const allCpUsers = useAllCpUsers(isCpUser.value)

const avatarColumns = [
  isCpUser.value ? {
    type: 'selection',
    disabled (row) {
      return row.status === EAssetStatus.Published
    }
  } : null,
  {
    title: '形象名称',
    key: 'originalname',
    width: 250,
    render(row) {
      return h('div', [
        h(ElImage, {
          style: { width: '140px', height: '100px' },
          zoomRate: 1.2,
          maxScale: 6,
          minScale: 0.2,
          previewSrcList: [row.url_icon],
          fit: 'cover',
          src: row.url_icon,
          title: '点击放大',
          hideOnClickModal: true,
        }),
        h(
          ElTooltip,
          {
            trigger: 'hover',
            placement: 'top',
          },
          {
            content: () => [
              h('div', { style: 'text-align: center' }, row.name),
            ],
            default: () => [
              h('div', row.name),
            ]
          },
        )
      ]);
    },
  },
  isCpUser.value ? null : {
    title: '制作CP',
    key: 'userId',
    render(row) {
      return row.user?.name
    }
  },
  {
    title: '所属合作伙伴',
    key: 'address',
    render(row) {
      return row.provider?.title;
    },
  },
  {
    title: '风格类型',
    key: 'style',
    render(row) {
      return row.style?.title
    }
  },
  {
    title: '状态',
    key: 'status',
    render(row) {
      return h(
        'span',
        {
          style: {color: AssetStatusColor[row.status]},
        },
        AssetStatus[row.status]
      )
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    render(row) {
      return moment(row['created_at']).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    title: '更新时间',
    key: 'updated_at',
    render(row) {
      return moment(row['updated_at']).format('YYYY-MM-DD HH:mm:ss')
    }
  },
].filter(i => i).map(i => {
  return { ...i, align: 'center' }
})
const rules: FormRules = {
  name: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入名称',
  },
  address: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入地址',
  },
  date: {
    type: 'number',
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择日期',
  },
};

const router = useRouter();
const formRef: any = ref(null);
const actionRef = ref();

const formParams = reactive({
  style_id: '',
  keyword: '',
  gender: '',
  status: '',
  dates: [],
  user_id: ''
});

const actionColumn = reactive({
  title: '操作',
  key: 'action',
  width: '300px',
  render(record) {
    return h(TableAction as any, {
      style: 'button',
      actions: [
        {
          label: '详情',
          onClick: handleDetail.bind(null, record),
        },
        {
          label: '审核',
          confirmed: true,
          onClick: handleAudit.bind(null, record),
          ifShow: () => {
            return record.status === 2;
          },
          auth: [PERMISSION_KEYS.auditor.audit_asset_avatar]
        },
        {
          label: '撤回',
          type: 'warning',
          // 根据业务控制是否显示 isShow 和 auth 是并且关系
          ifShow: () => {
            return record.status === 2;
          },
          popConfirm: {
            title: '确定要撤回吗？',
            confirm: handleCancelPublish.bind(null, record)
          },
          auth: [
            PERMISSION_KEYS.cpuser.asset_avatar_unpublish
          ]
        },
        {
          label: '预览',
          onClick: handlePreview.bind(null, record),
          ifShow: () => {
            return true;
          },
          auth: [
            PERMISSION_KEYS.cpuser.asset_avatar_read,
            PERMISSION_KEYS.auditor.audit_asset_avatar
          ]
        },
        {
          label: '删除',
          type: 'error',
          // 根据业务控制是否显示 isShow 和 auth 是并且关系
          ifShow: () => {
            return record.status !== EAssetStatus.Reviewing && record.status !== EAssetStatus.Published;
          },
          popConfirm: {
            title: '确定要删除吗？',
            confirm: handleDelete.bind(null, record)
          },
          auth: [
            PERMISSION_KEYS.cpuser.asset_avatar_remove
          ]
        },
        {
          label: '编辑',
          onClick: handleEdit.bind(null, record),
          ifShow: () => {
            return [
                EAssetStatus.Draft,
                EAssetStatus.Rejected,
                EAssetStatus.Unshelve
              ].includes(record.status)
          },
          auth: [
            PERMISSION_KEYS.cpuser.asset_avatar_edit
          ]
        },
        {
          label: '申请发布',
          // 根据业务控制是否显示 isShow 和 auth 是并且关系
          ifShow: () => {
            return [
              EAssetStatus.Draft,
              EAssetStatus.Rejected,
              EAssetStatus.Unshelve
            ].includes(record.status)
          },
          popConfirm: {
            title: '确定要申请发布吗？',
            confirm: handleApplyPublish.bind(null, record)
          },
          auth: [
            PERMISSION_KEYS.cpuser.asset_avatar_publish
          ]
        },
        {
          label: '下架',
          // 根据业务控制是否显示 isShow 和 auth 是并且关系
          ifShow: () => {
            return record.status === EAssetStatus.Published;
          },
          type: 'error',
          popConfirm: {
            title: '确定要下架吗？',
            confirm: handleUnpublish.bind(null, record)
          },
          auth: [
            PERMISSION_KEYS.auditor.audit_asset_avatar
          ]
        },
      ],
    });
  },
});

async function handleAudit(record: ListData) {
  const { id } = record
  router.push({
    name: 'avatar-preview',
    query: {
      avatarId: id,
      mode: 'audit',
    }
  })
}

function handleDetail(record: ListData) {
  const { id } = record
  router.push({
    name: 'avatar-preview',
    query: {
      avatarId: id,
      mode: 'detail',
    }
  })
}

async function handleUnpublish(record) {
  const { id } = record
  await revisingAvatarPublish(id)
  ElMessage({
    message: '下架成功',
    type: 'success'
  })
  reloadTable()
}

const dialogVisible = ref(false)
const flowSrc = ref('')
function addTable() {
  router.push({
    path: 'editAvatar',
    query: {
      mode: 'add',
    }
  })
}

let paramsDraft = null
// 导出
const handleExport = () => {
  const ids = actionRef.value?.getSelectionRowKeys();
  if (hasPermission([PERMISSION_KEYS.auditor.audit_asset_avatar])) {
    exportFile('/business/export-query/asset-avatar/export/audit/list', paramsDraft, {ids});
  } else {
    exportFile('/business/export-query/asset-avatar/export/list', paramsDraft, {ids});
  }
};

const loadDataTable = async (res) => {
  const params = {
    ...formParams,
    ...res,
    provider_id: currentProviderId.value,
  }
  params.create_start = params.dates?.[0]
  params.create_end = params.dates?.[1]
  delete params.dates
  paramsDraft = params
  let data
  if (hasPermission([PERMISSION_KEYS.auditor.audit_asset_avatar])) {
    data = await getAuditAvatarList(params);
  } else {
    data = await getAvatarList(params);
  }
  if (data.code === 0) {
    inited.value = true
    return data.data
  }
  return;
};

const inited = ref(false)
watch(formParams, () => {
  reloadTable();
}, { deep: true })

function reloadTable() {
  actionRef.value.updatePage(1)
}

function handlePreview(record: ListData) {
  // 使用Vue Router的replace方法直接跳转，这样不会添加历史记录
  const modelUrl = record.url || '';
  
  if (!modelUrl) {
    ElMessage.warning('该形象没有关联的模型文件');
    return;
  }
  
  // 在控制台输出详细的URL信息，帮助调试
  console.log('原始模型URL:', modelUrl);
  console.log('完整模型URL:', new URL(modelUrl, window.location.origin).href);
  
  // 添加一个时间戳参数，避免浏览器缓存
  const urlWithTimestamp = modelUrl.includes('?') 
    ? `${modelUrl}&_t=${new Date().getTime()}` 
    : `${modelUrl}?_t=${new Date().getTime()}`;
  
  // 获取实际文件扩展名，如果是bundle，假设它是FBX
  let format = modelUrl ? (modelUrl.split('.').pop() || '未知') : '未知';
  if (format.toLowerCase() === 'bundle') {
    format = 'fbx (bundle)';
  }
  
  router.replace({
    name: 'model3d-preview',
    query: {
      modelUrl: urlWithTimestamp,
      modelInfo: JSON.stringify({
        name: record.name || '未命名',
        format: format,
        size: record.size || 0,
        uploadTime: record.created_at || new Date().toISOString()
      }),
      source: 'avatar', // 添加来源标识，用于区分不同模块
      avatarId: record.id // 保留原有的avatarId参数
    }
  });
}

function handleEdit(record: Recordable) {
  router.push({
    path: 'editAvatar',
    query: {
      mode: 'edit',
      avatarId: record.id
    }
  })
}

async function handleCancelPublish(record) {
  await revokeAvatarPublish(record.id)
  ElMessage({
    message: '撤回成功',
    type: 'success'
  })
  reloadTable();
}

async function handleDelete(record: Recordable) {
  await deleteAvatar(record.id)
  ElMessage({
    message: '删除成功',
    type: 'success'
  })
  reloadTable()
}

async function handleApplyPublish(record) {
  await applyAvatarPublish(record.id)
  ElMessage({
    message: '申请已提交',
    type: 'success'
  })
  reloadTable()
}

const handleBatchRemove = async () => {
  try {
    const ids = actionRef.value?.getSelectionRowKeys();
    if (ids.length <= 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要批量删除的形象',
      });
      return;
    }
    const action = await ElMessageBox.confirm(`确认要批量删除所选形象吗?`, '批量删除形象');
    if (action == 'confirm') {
      const { code } = await batchDeleteAvatar({ ids });
      if (code === 0) {
        ElMessage({
          type: 'success',
          message: '批量删除成功！',
        });
        reloadTable();
      }
    }
  } catch (e) {console.log(e)}
};
</script>

<style lang="less" scoped>
  .material-table {
    :deep(.n-data-table-base-table-body) {
      max-height: unset !important;
    }
  }
</style>