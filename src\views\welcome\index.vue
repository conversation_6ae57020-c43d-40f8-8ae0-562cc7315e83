<template>
  <div
    :style="{
      display: 'flex',
      flexDirection: 'column',
      background: '#fff',
      fontSize: '30px',
      height: 'calc(100vh - 130px)',
      padding: '20px',
      textAlign: 'center',
      justifyContent: 'center',
    }"
  >
    <img alt="图片" :src="getImgUrl('/slices/welcome.png')" style="max-width: 400px;margin: 20px auto;"/>
    欢迎登录{{ info.name }}
  </div>
</template>

<script lang="tsx" setup>
  import { useGlobSetting } from '@/hooks/setting';
import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  const globSetting = useGlobSetting();
  function getImgUrl(url: string): string {
    const { imgUrl } = globSetting;
    return /(^http|https:\/\/)/g.test(url) ? url : `${imgUrl}${url}`;
  }
  const userStore = useUserStore();
  const { info } = storeToRefs(userStore);
</script>
