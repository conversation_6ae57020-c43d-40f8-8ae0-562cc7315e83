<template>
  <el-card class="box-card">
    <div class="flex">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline flex-1">
        <el-form-item label="用户:">
          <el-input v-model.trim="queryParams.name" placeholder="请输入操作人" clearable />
        </el-form-item>
        <el-form-item label="操作页面:">
          <el-select v-model.trim="queryParams.pagex" placeholder="请选择操作页面" clearable>
            <el-option value="" label="全部" />
            <el-option
              :value="option.value"
              :label="option.value"
              v-for="option in pagesOptions"
              :key="option.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="操作信息:">
          <el-input v-model.trim="queryParams.info" placeholder="请输入操作信息" clearable />
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="user_name" label="用户" />
      <el-table-column prop="page" label="操作页面" />
      <el-table-column prop="info" label="操作信息" />
      <el-table-column prop="times" width="120" label="次数" />
    </el-table>
    <el-pagination
      class="flex my-4 justify-end"
      background
      layout="total, prev, pager, next, sizes, jumper"
      v-model:page-size="queryParams.limit"
      v-model:current-page="queryParams.page"
      :total="totalRef"
    />
  </el-card>
</template>

<script lang="ts" setup>
  import { getUserBehaviorList, getPages } from '@/api/system/log';
  import { useDebounceFn } from '@vueuse/core';
  import { onMounted, reactive, ref, watch } from 'vue';

  const queryParams = reactive({
    name: '',
    pagex: '',
    info: '',
    limit: 10,
    page: 1,
  });

  const totalRef = ref(0);
  const tableData = ref([]);
  const pagesOptions = ref<any>([]);

  const generateParamas = () => {
    const { ...other } = queryParams;
    const params: any = { ...other };
    return params;
  };

  const fetchPages = async () => {
    const {
      data: { rows = [] },
    } = await getPages({});
    pagesOptions.value = rows;
  };

  const fetchBehaviorList = useDebounceFn(async () => {
    const params = generateParamas();
    const {
      data: { rows: list, count },
    } = await getUserBehaviorList(params);
    totalRef.value = count;
    tableData.value = list;
  }, 500);

  onMounted(async () => {
    await fetchPages();
    await fetchBehaviorList();
  });

  watch(
    () => queryParams,
    async () => {
      await fetchBehaviorList();
    },
    { deep: true }
  );
</script>
