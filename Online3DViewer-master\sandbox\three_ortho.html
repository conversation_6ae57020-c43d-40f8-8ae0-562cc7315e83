<!DOCTYPE html>
<html>

<head>
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<meta name="viewport" content="width=device-width, user-scalable=no">

	<title>Online 3D Viewer</title>

	<style>
		canvas
		{
			border: 1px solid #cccccc;
		}
	</style>

	<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/three@0.147.0/build/three.min.js"></script>
	<script type='text/javascript'>
		function InitRenderer (canvasId, camera, width, height)
		{
			function CreateAxisLine (color, dir)
			{
				const lineMaterial = new THREE.LineBasicMaterial ({
					color: color
				});
				const lineGeometry = new THREE.BufferGeometry ().setFromPoints ([
					new THREE.Vector3 (0, 0, 0),
					dir
				]);
				return new THREE.Line (lineGeometry, lineMaterial);
			}

			let canvas = document.getElementById (canvasId);

			let parameters = {
				canvas : canvas,
				antialias : true
			};

			let renderer = new THREE.WebGLRenderer (parameters);
			renderer.setClearColor ('#ffffff', 1);
			renderer.setSize (width, height);

			let scene = new THREE.Scene ();

			let ambientLight = new THREE.AmbientLight (0x888888);
			scene.add (ambientLight);

			let light = new THREE.DirectionalLight (0x888888);
			light.position.set (3.0, -1.5, 2.0);
			scene.add (light);

			let box = new THREE.BoxGeometry (1.0, 1.0, 1.0);
			let material = new THREE.MeshPhongMaterial ({
				color : 0xaaaaaa
			});
			let mesh = new THREE.Mesh (box, material);
			scene.add (mesh);

			scene.add (CreateAxisLine (0xff0000, new THREE.Vector3 (2.0, 0.0, 0.0)));
			scene.add (CreateAxisLine (0x00ff00, new THREE.Vector3 (0.0, 2.0, 0.0)));
			scene.add (CreateAxisLine (0x0000ff, new THREE.Vector3 (0.0, 0.0, 2.0)));

			scene.add (camera);
			renderer.render (scene, camera);
		}

		function Sandbox3D ()
		{
			let width = 600;
			let height = 400;
			let aspect = width / height;
			let nearPlane = 0.1;
			let farPlane = 100.0;

			let eye = new THREE.Vector3 (3.0, -1.5, 2.0);
			let center = new THREE.Vector3 (0.0, 0.0, 0.0);
			let up = new THREE.Vector3 (0.0, 0.0, 1.0);

			let fieldOfView = 45.0;
			let perspectiveCamera = new THREE.PerspectiveCamera (fieldOfView, aspect, nearPlane, farPlane);
			perspectiveCamera.position.set (eye.x, eye.y, eye.z);
			perspectiveCamera.up.set (up.x, up.y, up.z);
			perspectiveCamera.lookAt (center);
			InitRenderer ('perspective_canvas', perspectiveCamera, width, height);

			let eyeCenterDistance = eye.distanceTo (center);
			var frustumHalfHeight = eyeCenterDistance * Math.tan (0.5 * fieldOfView * (Math.PI / 180.0));
			let orthographicCamera = new THREE.OrthographicCamera (-frustumHalfHeight * aspect, frustumHalfHeight * aspect, frustumHalfHeight, -frustumHalfHeight, nearPlane, farPlane);
			orthographicCamera.position.set (eye.x, eye.y, eye.z);
			orthographicCamera.up.set (up.x, up.y, up.z);
			orthographicCamera.lookAt (center);
			InitRenderer ('orthographic_canvas', orthographicCamera, width, height);
		}

		window.onload = function () {
			Sandbox3D ();
		};
	</script>
</head>

<body>
	<canvas id="perspective_canvas"></canvas>
	<canvas id="orthographic_canvas"></canvas>
</body>

</html>
