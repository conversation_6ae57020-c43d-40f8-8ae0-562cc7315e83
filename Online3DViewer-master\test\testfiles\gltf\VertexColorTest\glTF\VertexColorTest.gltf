{"accessors": [{"bufferView": 0, "componentType": 5121, "count": 36, "max": [23], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "componentType": 5126, "count": 24, "max": [0.7554783821105957, -0.8512416481971741, 0.9750328660011292], "min": [-0.7045658230781555, -0.8512417078018188, -0.9611154794692993], "type": "VEC3"}, {"bufferView": 2, "componentType": 5126, "count": 24, "max": [0.0, 1.0, -0.0], "min": [0.0, 1.0, -0.0], "type": "VEC3"}, {"bufferView": 3, "componentType": 5126, "count": 24, "max": [1.0, 7.121938949694595e-08, 1.5105192119335697e-07, 1.0], "min": [1.0, -1.4348813692777185e-07, -1.1257676391096538e-07, 1.0], "type": "VEC4"}, {"bufferView": 4, "componentType": 5126, "count": 24, "max": [0.9999998807907104, 0.8010409474372864], "min": [0.0, 0.05539727210998535], "type": "VEC2"}, {"bufferView": 5, "componentType": 5121, "count": 72, "max": [47], "min": [0], "type": "SCALAR"}, {"bufferView": 6, "componentType": 5126, "count": 48, "max": [1.0000004768371582, 1.0, 0.48742246627807617], "min": [-1.0000003576278687, -1.0, -0.1718665361404419], "type": "VEC3"}, {"bufferView": 7, "componentType": 5126, "count": 48, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 8, "componentType": 5126, "count": 48, "max": [1.0, 7.940933880509066e-23, 4.470348358154297e-08, 1.0], "min": [1.0, -6.617444900424222e-24, -0.0, 1.0], "type": "VEC4"}, {"bufferView": 9, "componentType": 5126, "count": 48, "max": [0.8361296057701111, 0.7666319012641907], "min": [0.22613045573234558, 0.2321176528930664], "type": "VEC2"}, {"bufferView": 10, "componentType": 5126, "count": 48, "max": [1.0, 1.0, 1.0, 1.0], "min": [0.0, 0.0, 0.0, 1.0], "type": "VEC4"}], "asset": {"copyright": "Copyright 2018 Analytical Graphics, Inc., CC-BY 4.0 https://creativecommons.org/licenses/by/4.0/ - Mesh and textures by <PERSON>.", "generator": "Khronos Blender glTF 2.0 exporter", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 36, "byteOffset": 0, "target": 34963}, {"buffer": 0, "byteLength": 288, "byteOffset": 36, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 324, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 612, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 996, "target": 34962}, {"buffer": 0, "byteLength": 72, "byteOffset": 1188, "target": 34963}, {"buffer": 0, "byteLength": 576, "byteOffset": 1260, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 1836, "target": 34962}, {"buffer": 0, "byteLength": 768, "byteOffset": 2412, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 3180, "target": 34962}, {"buffer": 0, "byteLength": 768, "byteOffset": 3564, "target": 34962}], "buffers": [{"byteLength": 4332, "uri": "VertexColorTest.bin"}], "images": [{"uri": "VertexColorTestLabels.png"}, {"uri": "VertexColorChecks.png"}], "materials": [{"doubleSided": true, "name": "Label_Mat", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.0, "roughnessFactor": 0.8999999761581421}}, {"name": "VC_Checks_Mat", "pbrMetallicRoughness": {"baseColorTexture": {"index": 1}, "metallicFactor": 0.0, "roughnessFactor": 0.8999999761581421}}], "meshes": [{"name": "LabelMesh", "primitives": [{"attributes": {"NORMAL": 2, "POSITION": 1, "TANGENT": 3, "TEXCOORD_0": 4}, "indices": 0, "material": 0}]}, {"name": "VertexColorTestMesh", "primitives": [{"attributes": {"COLOR_0": 10, "NORMAL": 7, "POSITION": 6, "TANGENT": 8, "TEXCOORD_0": 9}, "indices": 5, "material": 1}]}], "nodes": [{"mesh": 0, "name": "Labels", "rotation": [0.7071068286895752, 0.0, -0.0, 0.7071068286895752], "translation": [0.0, 0.0, 0.9126079678535461]}, {"mesh": 1, "name": "VertexColorTest", "scale": [1.0, 1.0, 0.07413393259048462]}], "samplers": [{}], "scene": 0, "scenes": [{"name": "Scene", "nodes": [0, 1]}], "textures": [{"sampler": 0, "source": 0}, {"sampler": 0, "source": 1}]}