import * as Api from "../../api"
import { axios } from "../../api/axios"
import { RendererRecorder } from './RendererRecorder'
import { FURenderKit } from "@/packages/types/FURenderKit"
import FURenderKitSDK from "../fu_render_kit"
import { getDefaultAvatarList } from "@/api/AvatarRender"
import { SOCKET_INS } from "@/components/AvatarRender"
export interface IAnimation {
    filter: {
        gender: string
    }
    icon_url: string
    name: string
    path: string
}
export class FUResourceManager {
    token = ''
    snapshot: Record<string, any> = { list: [] }
    avatarsInfo = {}
    controllConfig?: ArrayBuffer
    mountRoot = "GAssets"
    module?: Record<string, any>
    dressupComponents: Record<string, any> = {}
    usingIDBFS = false
    defaultAvatar: any
    defaultAvatarList: any[] = []
    pendingSyncTasks: any[] = []
    syncTimeout
    _fileInfo = new Map() // 记录文件信息  path: { hash, used, latest }
    animationList: IAnimation[] = []
    talkSkills: any[] = []
    demoSkills: any[] = []
    voiceList: any[] = []
    api: typeof Api
    appid: string
    cacheLimit: number
    persistentPaths: Set<string>
    preferIDBFS: boolean
    appversion: string
    sdkversion: string
    resourceConfig: Record<string, string>
    recorder: RendererRecorder
    sdk?: FURenderKitSDK
    constructor(param) {
        this.recorder = new RendererRecorder({
            maxLength: param.maxHistoryLength
        })
        this.appid = ''
        this.appversion = ''
        this.sdkversion = ''
        // this.projectVersion = param.projectVersion;
        this.cacheLimit = param.cacheLimit
        this.preferIDBFS = param.preferIDBFS
        this.api = param.api
        this.persistentPaths = new Set()
        this.resourceConfig = {}
    }

    get itemList() {
        return this.snapshot
    }

    setModule(module) {
        if (!module.FS) {
            throw "There is no FileSystem in module"
        }
        this.module = module
        let resolver
        let ret = new Promise((res) => (resolver = res))
        if (this.preferIDBFS) {
            module.FS.mkdir(this.mountRoot)
            module.FS.mount(module.FS.filesystems.IDBFS, {}, this.mountRoot)
            // 同步缓存
            module.FS.syncfs(true, (err) => {
                if (!err) {
                    this.usingIDBFS = true
                    // 同步结束 写入文件信息到 ResourceManager
                    this.populateFS()
                } else {
                    console.error(err)
                }
                resolver()
            })
        } else {
            resolver()
        }
        return ret
    }

    clearHashIinfo() {
        for (let i = 0; i < window.localStorage.length; i++) {
            const key = window.localStorage.key(i)
            if (key?.startsWith("GAssets")) {
                window.localStorage.setItem(key, "")
            }
        }
    }

    populateFS() {
        const version_key = "fu_renderkit_version"
        const version = window.localStorage.getItem(version_key)
        if (version !== this.sdkversion) {
            this.clearHashIinfo()
            window.localStorage.setItem(version_key, this.sdkversion)
        }
        const dfs = (parent, fileInfo) => {
            const childrenPaths = this.module?.FS.readdir(parent)
            for (let path of childrenPaths) {
                if (path === "." || path === "..") {
                    continue
                } else if (path.endsWith("bundle")) {
                    const fileId = `${parent}/${path}`
                    const md5 = window.localStorage.getItem(fileId)
                    fileInfo.set(`${parent}/${path}`, {
                        used: false,
                        latest: false,
                        hash: md5,
                    })
                } else {
                    dfs(`${parent}/${path}`, fileInfo)
                }
            }
        }
        dfs(this.mountRoot, this._fileInfo)
    }

    /***
     * 项目登录授权
     */
    async initialize({ sdk }) {
        this.sdk = sdk
        await this.renewToken()
        if (this.token) {
            await this.getAllResourceConfig()
            await this.getAllBundleList()
        }
    }

    async renewToken() {
        const EXTRA_TIME = 10 * 60 * 1000;

        let res = await this.api.getKey()
        if (!res) {
            throw "token service is down"
        }
        this.token = res.sdk_app.app_token.token
        this.appid = res.sdk_app.VITE_APP_ID
        this.appversion = res.sdk_app.VITE_APP_VERSION
        this.sdkversion = res.sdk_app.VITE_SDK_VERSION
        SOCKET_INS.url = res.sdk_app.VITE_SOCKET_URL
        window.localStorage.setItem('token', this.token)
        setTimeout(() => {
            this.renewToken()
        }, EXTRA_TIME)
        return this.token
    }

    async getItemListVersion() {
        let projects = await this.api.getProjectV3({
            appid: this.appid,
            token: this.token,
            appversion: this.appversion,
            sdkversion: this.sdkversion,
        })
        const project = projects.list.find((i) => i.type === "PTAG")
        return {
            snapshotUrl: project.item_list_url,
            avatarList: project.avatar_list
        }
    }

    // 获取所有bundle资源
    async getAllResourceConfig() {
        const projectInfo = await this.getItemListVersion()
        this.resourceConfig = await this.api.getCentreConfigV3({
            token: this.token,
            appversion: this.appversion,
            sdkversion: this.sdkversion,
            appid: this.appid,
        })
        const res = await getDefaultAvatarList()
        const { snapshotUrl } = projectInfo
        const requests = [
            this.api.getItemsSnapshot(snapshotUrl),
        ]
        const results = await Promise.all(requests)
        this.snapshot = results[0] || {}
        this.defaultAvatarList = res.data
        this.defaultAvatar = this.defaultAvatarList[0]
        await this.getControllConfig()
    }

    // 所有bundle资源
    async getAllBundleList() {
        const res = await axios({
            url: this.resourceConfig.editItemListPath,
            method: 'get'
        })
        this.dressupComponents = res.data.map
    }

    // 获取所有动画资源
    async getAnimationList() {
        const res = await axios({
            url: this.resourceConfig.animationItemListPath,
            method: 'get'
        })
        this.animationList = res.data.map.animation
    }
    async getAvatarConfig(avatarid) {
        if (!this.avatarsInfo[avatarid]) {
            let avatarConfig = await this.api.getAvatarInfo({
                token: this.token,
                avatarid,
            })
            this.avatarsInfo[avatarid] = avatarConfig
        }

        return this.avatarsInfo[avatarid]
    }
    async getControllConfig() {
        if (!this.controllConfig) {
            const items = this.getGroupInfoFromSnapshot(
                "GAssets/config/controller_config.bundle"
            )
            const { url } = await this.lookupObj(items[0].resource_id)
            this.controllConfig = await this.LoadAssets(url)
        }
        return this.controllConfig
    }

    async LoadAssets(filename) {
        let url = filename
        let file_arraybuffer = await fetch(url, { credentials: 'same-origin' }).then((response) => {
            return response['arrayBuffer']();
        })
        return file_arraybuffer
    }

    async downloadAsset(url) {
        const file_arraybuffer = await this.api.fetchArrayBufferByUrl(url)
        return new Uint8Array(file_arraybuffer)
    }

    downloadJson(url) {
        return this.api.fetchTextByUrl(url)
    }

    getGroupInfoFromSnapshot(path) {
        const item = this.snapshot.list.find((v) => v.path === path)
        if (!item) {
            console.warn(`there is no item in snapshot with path: ${path}`)
            return []
        } else {
            let ret = [
                {
                    md5: item.md5,
                    path: item.path,
                    resource_id: item.resource_id,
                },
            ]
            if (item.config && item.config.children_paths) {
                for (let child of item.config.children_paths) {
                    const child_ret = this.getGroupInfoFromSnapshot(child)
                    ret = ret.concat(child_ret)
                }
            }
            return ret
        }
    }

    lookupObj(resource_id) {
        return this.api.getResourceById({
            token: this.token,
            resourceid: resource_id,
        }) // 包含 url 和 hash
    }

    scheduleSyncFS(task) {
        this.pendingSyncTasks.push(task);

        if (this.syncTimeout) {
            clearTimeout(this.syncTimeout);
        }
        this.syncTimeout = setTimeout(() => {
            const doingSyncTasks = this.pendingSyncTasks;
            this.pendingSyncTasks = [];

            const timeout = setTimeout(() => {
                this.usingIDBFS = false;
            }, 1000);
            this.module?.FS.syncfs(false, (err) => {
                timeout && clearTimeout(timeout);
                if (err) {
                    this.usingIDBFS = false;
                }
                for (let cd of doingSyncTasks) {
                    cd();
                }
            });
        }, 3000);
    }

    preLookupObj(bundleList) {
        for (let bundle of bundleList) {
            const items = this.getGroupInfoFromSnapshot(bundle);
            for (let item of items) {
                this.lookupObj(item.resource_id);
            }
        }
    }

    /** 准备bundle数据并塞进缓存中，下次优先读取缓存中的内容 */
    prepareData(path, isPersistent?, scheduleJob?) {
        if (!this.usingIDBFS) {
            const toRecycleNum = this._fileInfo.size - this.cacheLimit + 1 // 希望总文件数控制在 cacheLimit
            this._recycleRes(toRecycleNum)
            if (isPersistent && !this.persistentPaths.has(path)) {
                this.persistentPaths.add(path)
                this.cacheLimit++
            }
        }

        const items = this.getGroupInfoFromSnapshot(path)

        const requests: any[] = []

        for (let item of items) {
            const itemPath = item.path
            const itemMD5 = item.md5
            const itemResourceId = item.resource_id

            if (
                !this._fileInfo.has(itemPath) ||
                !this._fileInfo.get(itemPath).latest
            ) {
                if (
                    !this._fileInfo.has(itemPath) ||
                    this._fileInfo.get(itemPath).hash !== itemMD5
                ) {
                    const p = this.lookupObj(itemResourceId)
                        .then(({ url }) => this.downloadAsset(url))
                        .then((buffer) => {
                            scheduleJob && scheduleJob()
                            this._writeFs(buffer, itemPath)
                            if (this.usingIDBFS) {
                                this.scheduleSyncFS(() => {
                                    window.localStorage.setItem(itemPath, itemMD5);
                                });
                            }
                        })
                    requests.push(p)
                } else {
                    scheduleJob && scheduleJob()
                }

                this._fileInfo.set(itemPath, {
                    used: false,
                    hash: itemMD5,
                    latest: true,
                })
            } else {
                scheduleJob && scheduleJob()
            }
        }

        return Promise.all(requests).then(() => path)
    }

    _recycleRes(wantToRecycleNum) {
        let toRecycleNum = Math.min(
            Math.max(wantToRecycleNum, 0),
            this._fileInfo.size
        )
        if (toRecycleNum <= 0) {
            return
        }
        let keys = Array.from(this._fileInfo.keys())
        for (let key of keys) {
            const item = this._fileInfo.get(key)
            if (!this.persistentPaths.has(key) && item && item.used) {
                this._removeFile(key)
                this._fileInfo.delete(key)
            }
            toRecycleNum--
            if (toRecycleNum === 0) {
                break
            }
        }
    }

    async prepareCustomBundle(url, path) {
        // 需要自行进行缓存存取和有效性检查，此处为只进行网络拉取
        if (url) {
            const buffer = await this.downloadAsset(
                url.startsWith('http')? url : this.sdk?.wasmCtx.locateFile(url)
            )
            this._writeFs(buffer, path)
        }
    }

    _removeFile(path) {
        this.module?.FS.truncate(path, 0)
        this.module?.FS.unlink(path)
    }

    _writeFs(content, path) {
        const index = path.lastIndexOf("/")
        const dir = path.substring(0, index)
        try {
            this.module?.FS.stat(dir)
        } catch (s) {
            this.module?.FS.mkdirTree(dir)
        }
        this.module?.FS.writeFile(path, content, { flags: "w" })
    }

    markFileUsed(path) {
        const item = this._fileInfo.get(path)
        if (item) {
            item.used = true
            this._fileInfo.set(path, item)
        }
    }

    markFilesUsed(paths) {
        const length = paths.length

        for (let i = 0; i < length; i++) {
            this.markFileUsed(paths[i])
        }
    }
}
