<!DOCTYPE html>
<html>

<head>
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<meta name="viewport" content="width=device-width, user-scalable=no">
	<link rel="icon" type="image/png" href="static/3dviewer_net_favicon.ico">

	<title>Online 3D Viewer - EmbeddedViewer</title>

	<link rel="stylesheet" href="static/highlightjs/styles/github.min.css"/>
	<script src="static/highlightjs/highlight.min.js"></script>

    <link rel="stylesheet" type="text/css" href="static/style.css"/>
	<script type="text/javascript" src="static/script.js"></script>
</head>

<body>
<div id="navigation_toggle" class="navigation_toggle"><img id="navigation_icon" src="static/menu.svg"/></div>
<div id="navigation" class="navigation thin_scrollbar">
<div class="navigation_section">
<div class="navigation_title">Pages</div>
<div id="nav-Home" class="navigation_item"><a href="index.html" target="_self">Home</a></div>
<div id="nav-GitHub" class="navigation_item"><a href="https://github.com/kovacsv/Online3DViewer" target="_blank">GitHub</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Engine Usage</div>
<div id="nav-Installation" class="navigation_item"><a href="Page_Installation.html" target="_self">Installation</a></div>
<div id="nav-Usage" class="navigation_item"><a href="Page_Usage.html" target="_self">Usage</a></div>
<div id="nav-Migration Guide" class="navigation_item"><a href="Page_MigrationGuide.html" target="_self">Migration Guide</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Contribution</div>
<div id="nav-Contribution Guidelines" class="navigation_item"><a href="Page_ContributionGuidelines.html" target="_self">Contribution Guidelines</a></div>
<div id="nav-Environment Setup" class="navigation_item"><a href="Page_EnvironmentSetup.html" target="_self">Environment Setup</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Classes</div>
<div id="nav-Camera" class="navigation_item"><a href="Class_Camera.html" target="_self">Camera</a></div>
<div id="nav-EdgeSettings" class="navigation_item"><a href="Class_EdgeSettings.html" target="_self">EdgeSettings</a></div>
<div id="nav-EmbeddedViewer" class="navigation_item"><a href="Class_EmbeddedViewer.html" target="_self">EmbeddedViewer</a></div>
<div id="nav-EnvironmentSettings" class="navigation_item"><a href="Class_EnvironmentSettings.html" target="_self">EnvironmentSettings</a></div>
<div id="nav-InputFile" class="navigation_item"><a href="Class_InputFile.html" target="_self">InputFile</a></div>
<div id="nav-RGBAColor" class="navigation_item"><a href="Class_RGBAColor.html" target="_self">RGBAColor</a></div>
<div id="nav-RGBColor" class="navigation_item"><a href="Class_RGBColor.html" target="_self">RGBColor</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Functions</div>
<div id="nav-Init3DViewerElements" class="navigation_item"><a href="Function_Init3DViewerElements.html" target="_self">Init3DViewerElements</a></div>
<div id="nav-Init3DViewerFromFileList" class="navigation_item"><a href="Function_Init3DViewerFromFileList.html" target="_self">Init3DViewerFromFileList</a></div>
<div id="nav-Init3DViewerFromUrlList" class="navigation_item"><a href="Function_Init3DViewerFromUrlList.html" target="_self">Init3DViewerFromUrlList</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Enums</div>
<div id="nav-FileSource" class="navigation_item"><a href="Enum_FileSource.html" target="_self">FileSource</a></div>
<div id="nav-NavigationMode" class="navigation_item"><a href="Enum_NavigationMode.html" target="_self">NavigationMode</a></div>
<div id="nav-ProjectionMode" class="navigation_item"><a href="Enum_ProjectionMode.html" target="_self">ProjectionMode</a></div>
</div>

</div>
<div id="main" class="main">
<h1>EmbeddedViewer</h1>
<div class="description">This is the main object for embedding the viewer on a website.</div>
<h2>Constructor</h2>
<div class="function_container">
<div id="EmbeddedViewer" class="function_signature">new EmbeddedViewer (parentElement, parameters)</div>
<div class="function_title">Parameters</div>
<div class="parameter_header">
<span class="parameter_name">parentElement</span>
<span class="type parameter_type"><a href="https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement" target="_blank">HTMLElement</a></span>
</div>
<div class="parameter_main">
<div class="parameter_description">The parent element for the viewer canvas. It must be an existing DOM element and it will be the container for the canvas. The size of the viewer will be automatically adjusted to the size of the parent element.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">parameters</span>
<span class="type parameter_type">object</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Parameters for embedding.</div>
<div class="parameter_header">
<span class="parameter_name">camera</span>
<span class="type parameter_type"><a href="Class_Camera.html" target="_self">Camera</a></span>
<span class="parameter_attributes">(optional)</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Camera to use. If not specified, the default camera will be used and the model will be fitted to the window.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">projectionMode</span>
<span class="type parameter_type"><a href="Enum_ProjectionMode.html" target="_self">ProjectionMode</a></span>
<span class="parameter_attributes">(optional)</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Camera projection mode.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">backgroundColor</span>
<span class="type parameter_type"><a href="Class_RGBAColor.html" target="_self">RGBAColor</a></span>
<span class="parameter_attributes">(optional)</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Background color of the canvas.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">defaultColor</span>
<span class="type parameter_type"><a href="Class_RGBColor.html" target="_self">RGBColor</a></span>
<span class="parameter_attributes">(optional)</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Default color of the model. It has effect only if the imported model doesn&#x27;t specify any color.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">defaultLineColor</span>
<span class="type parameter_type"><a href="Class_RGBColor.html" target="_self">RGBColor</a></span>
<span class="parameter_attributes">(optional)</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Default line color of the model. It has effect only if the imported model doesn&#x27;t specify any color.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">edgeSettings</span>
<span class="type parameter_type"><a href="Class_EdgeSettings.html" target="_self">EdgeSettings</a></span>
<span class="parameter_attributes">(optional)</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Edge settings.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">environmentSettings</span>
<span class="type parameter_type"><a href="Class_EnvironmentSettings.html" target="_self">EnvironmentSettings</a></span>
<span class="parameter_attributes">(optional)</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Environment settings.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">onModelLoaded</span>
<span class="type parameter_type">function</span>
<span class="parameter_attributes">(optional)</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Callback that is called when the model with all of the textures is fully loaded.</div>
</div>
</div>
</div>
<h2>Methods</h2>
<div class="function_container">
<div id="LoadModelFromUrlList" class="function_signature">LoadModelFromUrlList (modelUrls)</div>
<div class="function_title">Description</div>
<div class="function_description">Loads the model based on a list of urls. The list must contain the main model file and all of the referenced files. For example in case of an obj file the list must contain the corresponding mtl and texture files, too.</div>
<div class="function_title">Parameters</div>
<div class="parameter_header">
<span class="parameter_name">modelUrls</span>
<span class="type parameter_type">string[]</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Url list of model files.</div>
</div>
</div>
<div class="function_container">
<div id="LoadModelFromFileList" class="function_signature">LoadModelFromFileList (fileList)</div>
<div class="function_title">Description</div>
<div class="function_description">Loads the model based on a list of <a href="https://developer.mozilla.org/en-US/docs/Web/API/File" target="_blank">File</a> objects. The list must contain the main model file and all of the referenced files. You must use this method when you are using a file picker or drag and drop to select files from a computer.</div>
<div class="function_title">Parameters</div>
<div class="parameter_header">
<span class="parameter_name">fileList</span>
<span class="type parameter_type"><a href="https://developer.mozilla.org/en-US/docs/Web/API/File" target="_blank">File</a>[]</span>
</div>
<div class="parameter_main">
<div class="parameter_description">File object list of model files.</div>
</div>
</div>
<div class="function_container">
<div id="LoadModelFromInputFiles" class="function_signature">LoadModelFromInputFiles (inputFiles)</div>
<div class="function_title">Description</div>
<div class="function_description">Loads the model based on a list of <a href="Class_InputFile.html" target="_self">InputFile</a> objects. This method is used internally, you should use LoadModelFromUrlList or LoadModelFromFileList instead.</div>
<div class="function_title">Parameters</div>
<div class="parameter_header">
<span class="parameter_name">inputFiles</span>
<span class="type parameter_type"><a href="Class_InputFile.html" target="_self">InputFile</a>[]</span>
</div>
<div class="parameter_main">
<div class="parameter_description">List of model files.</div>
</div>
</div>
<div class="function_container">
<div id="GetViewer" class="function_signature">GetViewer ()</div>
<div class="function_title">Description</div>
<div class="function_description">Returns the underlying Viewer object.</div>
<div class="function_title">Returns</div>
<div class="function_returns">
<span class="type parameter_type">Viewer</span>
</div>
</div>
<div class="function_container">
<div id="GetModel" class="function_signature">GetModel ()</div>
<div class="function_title">Description</div>
<div class="function_description">Returns the underlying Model object.</div>
<div class="function_title">Returns</div>
<div class="function_returns">
<span class="type parameter_type">Model</span>
</div>
</div>
<div class="function_container">
<div id="Resize" class="function_signature">Resize ()</div>
<div class="function_title">Description</div>
<div class="function_description">This method must be called when the size of the parent element changes to make sure that the context has the same dimensions as the parent element.</div>
</div>
<div class="function_container">
<div id="Destroy" class="function_signature">Destroy ()</div>
<div class="function_title">Description</div>
<div class="function_description">Frees up all the memory that is allocated by the viewer. You should call this function if yo don&#x27;t need the viewer anymore.</div>
</div>

</div>
<script type="text/javascript">Init ('EmbeddedViewer')</script>
</body>

</html>
