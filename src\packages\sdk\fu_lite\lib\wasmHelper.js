import axios from "axios"
import Fulite from "./Fulite"
class FuliteLoader {
    module
    async LoadWasm() {
        if(this.wasmOptions) {
            this.module = await Fulite(this.wasmOptions);
        } else {
            this.module = await Fulite();
        }
        
        return this.module;
    }
}
export const liteWasmLoader = new FuliteLoader();

export class WasmHelper {
    constructor(options) {
        this.wasmCtx = liteWasmLoader;
        this.wasmCtx.wasmOptions = options;
    }

    /**
     * 加载静态文件 返回
     * @param filename
     * @returns { ptr: 指针 size: 指针大小 }
     */
    async LoadAssets(filename) {
        // HACK:
        // let url = filename.includes("http") ? filename : "/assets/" + filename
        let url = filename
        let file_arraybuffer = await fetch(url, { credentials: 'same-origin' }).then(function(response) {
            
            return response['arrayBuffer']();
          })
        // let file_arraybuffer = (await axios.get(url, { responseType: "arraybuffer" })).data
        let file_buffer = new Uint8Array(file_arraybuffer)
        let file_heap8 = this.wasmCtx.module._malloc(file_buffer.length)
        this.wasmCtx.module.HEAP8.set(file_buffer, file_heap8)
        return {
            ptr: file_heap8,
            size: file_buffer.length,
        }
    }

    writeToFile(uint8Arr, path) {
        const index = path.lastIndexOf('/');
        const filename = path.substring(index+1);
        if(index !== -1) {
            const dir = path.substring(0, index);
            try {
                Module.FS.stat(dir);
            } catch(s) {
                Module.FS.mkdirTree(dir);
            }
            Module.FS.writeFile(path, uint8Arr);
        }
    }

    /**
     * 转化函数
     */
    Float64ArrayToHeap(arr) {
        let float_array = new Float64Array(arr.length)
        for (let i = 0; i < arr.length; i++) {
            float_array[i] = arr[i]
        }
        let ret = this.wasmCtx.module._malloc(float_array.length * float_array.BYTES_PER_ELEMENT)
        this.wasmCtx.module.HEAPF64.set(float_array, ret >> 3)
        return {
            ptr: ret,
            size: arr.length,
        }
    }
    Int32ArrayToHeap(arr) {
        let int_array = new Int32Array(arr.length)
        for (let i = 0; i < arr.length; i++) {
            int_array[i] = arr[i]
        }
        let ret = this.wasmCtx.module._malloc(int_array.length * int_array.BYTES_PER_ELEMENT)
        this.wasmCtx.module.HEAP32.set(int_array, ret >> 2)
        return {
            ptr: ret,
            size: arr.length,
        }
    }
    // 释放molloc
    FreeMemory(ptr) {
        this.wasmCtx.module._free(ptr)
    }
}
