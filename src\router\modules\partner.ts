import { Layout } from '@/router/constant';
import { renderIcon } from '@/utils/index';
import { ProjectOutlined } from '@vicons/antd';
import { PERMISSION_KEYS } from './audit';
import PartnerIcon from '@/components/menu-icons/cp/partner.vue';

const routes: Array<any> = [
  {
    path: '/partner',
    redirect: '/partner/list',
    // name: 'partner',
    component: Layout,
    meta: {
      // title: '合作伙伴管理',
      sort: 1,
      isRoot: true,
      activeMenu: 'partner',
      icon: renderIcon(PartnerIcon),
    },
    auth: [PERMISSION_KEYS.operator.provider_read],
    children: [
      {
        path: 'list',
        name: 'partner',
        meta: {
          title: '合作伙伴管理',
          sort: 1,
          activeMenu: 'partner',
        },
        component: () => import('@/views/partner/partner-list.vue'),
      },
      {
        path: 'add',
        name: `add_partner`,
        meta: {
          title: '新增伙伴',
          hidden: true,
          activeMenu: 'partner',
        },
        component: () => import('@/views/partner/AddOrEditPartner.vue'),
      },
      {
        path: 'edit',
        name: `edit_partner`,
        meta: {
          title: '编辑伙伴',
          hidden: true,
          activeMenu: 'partner',
        },
        component: () => import('@/views/partner/AddOrEditPartner.vue'),
      },
      {
        path: 'document',
        name: 'document',
        meta: {
          title: '查看技术规范',
          hidden: true,
          activeMenu: 'partner',
        },
        component: () => import('@/views/partner/document.vue'),
      },
      {
        path: 'tools',
        name: 'tools',
        meta: {
          title: '查看工具',
          hidden: true,
          activeMenu: 'partner',
        },
        component: () => import('@/views/partner/tools.vue'),
      },
    ],
  },
];

export default routes;
