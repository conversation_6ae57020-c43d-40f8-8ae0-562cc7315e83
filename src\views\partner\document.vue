<template>
  <el-card class="box-card">
    <h2>技术规范详情</h2>
    <document-block
      name="模型标准文档"
      :description="config.modelDescription"
      :url="config.modelDocument"
    />
    <document-block
      name="绑定标准文档"
      :description="config.bindingDescription"
      :url="config.bindingDocument"
    />
    <document-block
      name="动画标准文档"
      :description="config.animationDescription"
      :url="config.animationDocument"
    />
  </el-card>
</template>
<script lang="ts" setup>
  import { onMounted, reactive } from 'vue';
  import { useRoute } from 'vue-router';
  import DocumentBlock from './components/DocumentBlock.vue';
  import { getAbilityProviderDocument } from '@/api/ability-provider';

  const route = useRoute();

  const config = reactive({
    modelDocument: '',
    bindingDocument: '',
    modelDescription: '',
    animationDocument: '',
    bindingDescription: '',
    animationDescription: '',
  });

  const fetchDocument = async (id) => {
    const { code, data } = await getAbilityProviderDocument(id);
    const dataConfig = data.config as { [key: string]: string };
    if (code === 0) {
      config.modelDocument = dataConfig.modelDocument;
      config.modelDescription = dataConfig.modelDescription;

      config.bindingDocument = dataConfig.bindingDocument;
      config.bindingDescription = dataConfig.bindingDescription;

      config.animationDocument = dataConfig.animationDocument;
      config.animationDescription = dataConfig.animationDescription;
    }
  };

  onMounted(async () => {
    const id = route.query.id;
    if (id) {
      await fetchDocument(id);
    }
  });
</script>
