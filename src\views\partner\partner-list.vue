<template>
  <el-card class="box-card">
    <div class="flex justify-between mx-0 my-5">
      <el-input
        class="flex-none w-64"
        v-model.trim="queryParams.keyword"
        placeholder="请输入合作伙伴名称"
        clearable
        @keyup.enter="handleQuery"
      />
      <el-button type="primary" :icon="Plus" @click="handleAddPartner"> 新增伙伴</el-button>
    </div>
    <el-table :data="tableData">
      <el-table-column label="合作伙伴" prop="partner" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex ml-2 items-center">
            <el-image
              preview-teleported
              class="w-16 h-16 basis-16"
              :src="row.icon_url"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :z-index="10000"
              :preview-src-list="[row.icon_url]"
              fit="contain"
            >
              <template #error>
                <div class="flex justify-center items-center w-full h-full text-2xl">
                  <el-icon class="text-2xl"><icon-picture /></el-icon>
                </div>
              </template>
            </el-image>
            <div class="truncate block pl-1 flex-1">{{ row.title }}</div>
          </div>
        </template>
      </el-table-column>
      <template v-for="(item, index) in tableColumns">
        <el-table-column
          v-if="item.show"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :formatter="item.formatter"
          show-overflow-tooltip
        />
      </template>
      <el-table-column label="操作" width="260">
        <template #default="scope">
          <div class="flex">
            <el-button
              type="danger"
              class="m-0 p-0 mr-2"
              :text="true"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
            <el-button
              type="primary"
              class="m-0 p-0 mr-2"
              :text="true"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="primary"
              class="m-0 p-0 mr-2"
              :text="true"
              @click="handleViewDocument(scope.row)"
              >查看技术规范</el-button
            >
            <el-button
              type="primary"
              class="m-0 p-0"
              :text="true"
              @click="handleViewTool(scope.row)"
              >查看工具</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="flex my-4 justify-end"
      background
      layout="total, prev, pager, next, sizes, jumper"
      v-model:page-size="queryParams.limit"
      v-model:current-page="queryParams.page"
      :total="totalRef"
    />
  </el-card>
</template>

<script lang="ts" setup>
  import { getAbilityProviderList, deleteAbilityProvider } from '@/api/ability-provider';
  import { Plus } from '@element-plus/icons-vue';
  import { onMounted, reactive, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { dateTimeFormatter } from '@/utils/format-table-datetime';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import { useUserStore } from '@/store/modules/user';
  import { Picture as IconPicture } from '@element-plus/icons-vue';

  const userStore = useUserStore();

  const router = useRouter();

  interface QueryParams {
    keyword: string;
    page: number;
    limit: number;
  }

  const queryParams = reactive<QueryParams>({
    keyword: '',
    page: 1,
    limit: 10,
  });

  const totalRef = ref(0);

  onMounted(async () => {
    await getList();
  });

  const tableData = ref([]);

  const tableColumns = [
    { prop: 'address', label: '地址', show: true },
    { prop: 'phone', label: '联系电话', show: true },
    { prop: 'created_at', label: '创建时间', show: true, formatter: dateTimeFormatter, width: 180 },
    { prop: 'updated_at', label: '更新时间', show: true, formatter: dateTimeFormatter, width: 180 },
  ];

  watch(
    () => queryParams,
    async () => {
      await getList();
    },
    { deep: true }
  );

  //获取列表数据
  const getList = async () => {
    const {
      code,
      data: { rows: list, total },
    } = await getAbilityProviderList(queryParams);
    if (0 == code) {
      tableData.value = list;
      totalRef.value = total;
    }
  };
  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.page = 1;
    // getList()
  };

  //新增伙伴
  const handleAddPartner = () => {
    router.push({
      path: '/partner/add',
    });
  };

  //编辑伙伴
  const handleEdit = (row) => {
    router.push({
      path: '/partner/edit',
      query: {
        id: row?.id,
      },
    });
  };

  const handleDelete = async (row) => {
    try {
      const action = await ElMessageBox.confirm(
        `确认要继续删除合作伙伴“${row.title}”吗?`,
        '确认删除'
      );
      if (action == 'confirm') {
        const { code } = await deleteAbilityProvider(row.id);
        if (code === 0) {
          ElMessage({
            type: 'success',
            message: '成功删除',
          });
          getList();
          userStore.getProviders();
        }
      }
    } catch (e) {console.log(e)}
  };

  const handleViewDocument = (row) => {
    router.push({
      path: '/partner/document',
      query: {
        id: row.id,
      },
    });
  };

  const handleViewTool = (row) => {
    router.push({
      path: '/partner/tools',
      query: {
        id: row.id,
      },
    });
  };
</script>

<style lang="less" scoped>
  .box-card {
    min-height: 800px;

    .img {
      width: 60px;
      height: 60px;
    }
  }
</style>
