<template>
  <el-card :bordered="false" class="proCard" style="margin-bottom: 15px">
    <el-row gutter="20">
      <el-col :span="8">
        <el-form-item label="模板风格">
          <el-select v-model="formParams.style_id" placeholder="请选择模板风格" v-if="isCpUser">
            <el-option v-for="item in [{ id: '', title: '全部' }, ...styles]" :key="item.id" :label="item.title"
              :value="item.id" clearable />
          </el-select>
          <StyleCascader v-else ref="styleCascaderRef" v-model="formParams.style_id" :disabledCheckedAll="true" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-card>
  <el-card :bordered="false" class="proCard">
    <el-form>
      <el-row gutter="20">
        <el-col :span="6" v-if="hasPermission([PERMISSION_KEYS.auditor.audit_template])">
          <el-form-item label="制作CP">
            <el-select v-model="formParams.user_id" placeholder="请选择制作CP">
              <el-option label="全部" value="" />
              <template v-for="item in allCpUsers" :key="item.id">
                <el-option :label="item.name" :value="item.id" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6" v-if="hasPermission([PERMISSION_KEYS.operator.template_sync])">
          <el-form-item label="同步状态">
            <el-select v-model="formParams.sync_status" placeholder="请选择模板状态">
              <el-option label="全部" value="" />
              <template v-for="item in syncStatusOptions" :key="item.value">
                <el-option :label="item.label" :value="item.value" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-else>
          <el-form-item label="模板状态">
            <el-select v-model="formParams.status" placeholder="请选择模板状态">
              <el-option label="全部" value="" />
              <template v-for="item in (assetsSatusOptions.filter((item) => isCpUser || item.value !== 1))"
                :key="item.value">
                <el-option :label="item.label" :value="item.value" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="创建日期">
            <el-date-picker v-model="formParams.dates" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
              value-format="YYYY-MM-DD" format="YYYY-MM-DD" date-format="YYYY/MM/DD" clearable :disabled-date="(date) => {
    return date.getTime() > new Date().getTime()
  }" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="" label-width="0">
            <el-input placeholder="请输入模板名称" clearable v-model="formParams.keyword" :prefix-icon="Search" />
          </el-form-item>
        </el-col>
      </el-row>
      <div :style="{ float: 'right', textAlign: 'right', position: 'relative', bottom: '50px' }">
        <el-button type="primary" @click="addTable" :icon="PlusOutlined"
          v-if="hasPermission([PERMISSION_KEYS.cpuser.template_create])">
          新建
        </el-button>
        <el-button type="primary" @click="handleExport" v-if="hasPermission([
    PERMISSION_KEYS.cpuser.template_read,
    PERMISSION_KEYS.auditor.audit_template
  ])">
          导出
        </el-button>
        <el-button type="danger" @click="handleBatchDelete" v-if="isCpUser">批量删除</el-button>
        <el-button @click="handleHistory"
          v-if="hasPermission([PERMISSION_KEYS.operator.template_sync])">操作记录</el-button>
        <el-button type="primary" @click="handleBatchRemove"
          v-if="hasPermission([PERMISSION_KEYS.operator.template_sync])" :disabled="batchRemoveDisabled">
          批量下架
        </el-button>
        <el-button type="primary" @click="handleBatchSync"
          v-if="hasPermission([PERMISSION_KEYS.operator.template_sync])" :disabled="batchSyncDisabled">
          批量上架
        </el-button>
      </div>
    </el-form>
    <div class="material-table">
      <BasicTable :autoFetch="isCpUser" :columns="templateColumns" :request="loadDataTable"
        :row-key="(row: ListData) => row" ref="actionRef" :actionColumn="actionColumn" :scroll-x="1090" @onCheckedRows="(v) => {
    if (v.length > 0) {
      if (v.every(i => i.sync_status === SyncStatus['已同步'])) {
        batchSyncDisabled = true
        batchRemoveDisabled = false
      } else if (v.every(i => i.sync_status === SyncStatus['未同步'])) {
        batchSyncDisabled = false
        batchRemoveDisabled = true
      } else {
        batchSyncDisabled = true
        batchRemoveDisabled = true
      }
    } else {
      batchSyncDisabled = true
      batchRemoveDisabled = true
    }
  }" />
    </div>
  </el-card>

  <el-dialog v-model="dialogVisible" width="30%" :closable="false" title="选择风格">
    <el-form ref="formRef" label-width="80px">
      <el-form-item label="素材风格">
        <el-select v-model="styleId" placeholder="请选择素材风格">
          <el-option v-for="item in styles" :key="item.id" :label="item.title" :value="item.id" clearable />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="() => {
    dialogVisible = false
    router.push({
      name: 'template-add',
      query: {
        mode: 'template-add',
        styleId: styleId
      }
    })
  }">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <AsyncActionTable @register="register" :mode="'template'" />

  <el-dialog v-model="VFrameVisible" title="预览" width="900px" destroy-on-close>
    <VFrame :flowSrc="flowSrc" />
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  getTemplateList,
  applyTemplatePublish,
  deleteTemplate,
  revokeTemplatePublish,
  revisingTemplatePublish,
  batchDeleteTemplate,
  getAuditTemplateList,
  getOperateTemplateList,
  syncTemplate,
  getSyncResult
} from '@/api/material/template';
import { BasicTable, TableAction } from '@/components/Table';
import { Search } from '@element-plus/icons-vue';
import { PlusOutlined } from '@vicons/antd';
import moment from 'moment';
import { h, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { EAssetStatus, ListData } from './columns';
import { usePermission } from '@/hooks/web/usePermission';
import { PERMISSION_KEYS, useUser } from '@/store/modules/user';
import { ElImage, ElMessage, ElMessageBox, ElTooltip } from 'element-plus';
import { storeToRefs } from 'pinia';
import { watch } from 'vue';
import { AssetStatus, AssetStatusColor, SyncStatus, SyncStatusColor, assetsSatusOptions, syncStatusOptions } from '../material/columns';
import { useAllCpUsers, useExport } from '@/views/hooks';
import { useIntervalFn } from '@vueuse/core';
import { useModalTable } from '@/components/ModalTable';
import AsyncActionTable from '@/views/operator/AsyncActionTable.vue';

// 风格
const userStore = useUser();
const { styles, currentProviderId, isCpUser } = storeToRefs(userStore);
const { hasPermission } = usePermission();
const { exportFile } = useExport();
const allCpUsers = useAllCpUsers(isCpUser.value)
const dialogVisible = ref(false)
const VFrameVisible = ref(false)
const styleId = ref('')
const router = useRouter();
const formRef: any = ref(null);
const actionRef = ref();
const batchRemoveDisabled = ref(true)
const batchSyncDisabled = ref(true)

const formParams = reactive<any>({
  style_id: '',
  keyword: '',
  gender: '',
  sync_status: '',
  status: '',
  dates: [null, null],
  user_id: ''
});

const [register, { openModal }] = useModalTable({
  title: '查看同步记录',
});


const provider_id = ref<any>()

watch(styles, (v) => {
  styleId.value = v?.[0]?.id || v?.[1]?.id
}, { immediate: true })

const templateColumns = [
  isCpUser.value || hasPermission([PERMISSION_KEYS.operator.template_sync]) ? {
    type: 'selection',
    disabled(row) {
      console.log('isCpUser.value', isCpUser.value)
      if (isCpUser.value) {
        return row.status === EAssetStatus.Reviewing || row.status === EAssetStatus.Published;
      }
      return row.status !== EAssetStatus.Published
    }
  } : null,
  {
    title: '模板名称',
    key: 'originalname',
    width: 250,
    render(row) {
      return h('div', [
        h(ElImage, {
          style: { width: '140px', height: '100px' },
          zoomRate: 1.2,
          maxScale: 6,
          minScale: 0.2,
          previewSrcList: [row.url_template_icon || row.cover],
          fit: 'contain',
          src: row.url_template_icon || row.cover,
          title: '点击放大',
          hideOnClickModal: true,
        }),
        h(
          ElTooltip,
          {
            trigger: 'hover',
            placement: 'top'
          },
          {
            content: () => [
              h('div', { style: 'text-align: center' }, row.name),
            ],
            default: () => [
              h('div', row.name),
            ]
          },
        )
      ]);
    },
  },
  isCpUser.value ? null : {
    title: '制作CP',
    key: 'userId',
    render(row) {
      return row.user?.name
    }
  },
  {
    title: '所属合作伙伴',
    key: 'address',
    render(row) {
      return row.provider?.title;
    },
  },
  {
    title: '风格类型',
    key: 'style',
    render(row) {
      return row.style?.title
    }
  },
  {
    title: '模板状态',
    key: 'status',
    render(row) {
      return h(
        'span',
        {
          style: { color: AssetStatusColor[row.status] },
        },
        AssetStatus[row.status]
      )
    }
  },
  {
    title: '同步状态',
    key: 'sync_status',
    render(row) {
      return h(
        'span',
        {
          style: { color: SyncStatusColor[row.sync_status] },
        },
        SyncStatus[row.sync_status]
      )
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    render(row) {
      return moment(row['created_at']).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    title: '更新时间',
    key: 'updated_at',
    render(row) {
      return moment(row['updated_at']).format('YYYY-MM-DD HH:mm:ss')
    }
  },
].filter(i => i).map(i => {
  return { ...i, align: 'center' }
})

const actionColumn = reactive({
  title: '操作',
  key: 'action',
  width: '300px',
  render(record) {
    return h(TableAction as any, {
      style: 'button',
      actions: [
        {
          label: '详情',
          onClick: handleDetail.bind(null, record),
        },
        {
          label: '预览',
          onClick: handlePreview.bind(null, record),
        },
        {
          label: '下架',
          type: 'warning',
          ifShow: () => {
            return record.status === EAssetStatus.Published && record.sync_status === 1;
          },
          popConfirm: {
            title: '确定要下架吗？',
            confirm: handleRemove.bind(null, record)
          },
          loading: syncIds.value.includes(record.id),
          auth: [PERMISSION_KEYS.operator.template_sync]
        },
        {
          label: '上架',
          confirmed: true,
          ifShow: () => {
            return record.status === EAssetStatus.Published && record.sync_status === 0;
          },
          popConfirm: {
            title: '确定要上架吗？',
            confirm: handleSync.bind(null, record)
          },
          loading: syncIds.value.includes(record.id),
          auth: [PERMISSION_KEYS.operator.template_sync]
        },
        {
          label: '审核',
          confirmed: true,
          onClick: handleAudit.bind(null, record),
          ifShow: () => {
            return record.status === 2;
          },
          auth: [PERMISSION_KEYS.auditor.audit_template]
        },
        {
          label: '撤回',
          type: 'warning',
          // 根据业务控制是否显示 isShow 和 auth 是并且关系
          ifShow: () => {
            return record.status === 2;
          },
          popConfirm: {
            title: '确定要撤回吗？',
            confirm: handleCancelPublish.bind(null, record)
          },
          auth: [
            PERMISSION_KEYS.cpuser.template_unpublish
          ]
        },
        {
          label: '删除',
          type: 'error',
          // 根据业务控制是否显示 isShow 和 auth 是并且关系
          ifShow: () => {
            return record.status !== EAssetStatus.Reviewing && record.status !== EAssetStatus.Published;
          },
          popConfirm: {
            title: '确定要删除吗？',
            confirm: handleDelete.bind(null, record)
          },
          auth: [
            PERMISSION_KEYS.cpuser.template_remove
          ]
        },
        {
          label: '编辑',
          onClick: handleEdit.bind(null, record),
          ifShow: () => {
            return [
              EAssetStatus.Draft,
              EAssetStatus.Rejected,
              EAssetStatus.Unshelve
            ].includes(record.status)
          },
          auth: [
            PERMISSION_KEYS.cpuser.template_edit
          ]
        },
        {
          label: '申请发布',
          // 根据业务控制是否显示 isShow 和 auth 是并且关系
          ifShow: () => {
            return [
              EAssetStatus.Draft,
              EAssetStatus.Rejected,
              EAssetStatus.Unshelve
            ].includes(record.status)
          },
          popConfirm: {
            title: '确定要申请发布吗？',
            confirm: handleApplyPublish.bind(null, record)
          },
          auth: [
            PERMISSION_KEYS.cpuser.template_publish
          ]
        },
        {
          label: '驳回',
          // 根据业务控制是否显示 isShow 和 auth 是并且关系
          ifShow: () => {
            return record.status === EAssetStatus.Published && record.sync_status === 0;
          },
          type: 'error',
          popConfirm: {
            title: '确定要下架吗？',
            confirm: handleUnpublish.bind(null, record)
          },
          auth: [
            PERMISSION_KEYS.auditor.audit_template
          ]
        },
      ],
    });
  },
});

async function handleAudit(record: ListData) {
  const { id } = record
  router.push({
    name: 'template-audit-preview',
    query: {
      templateId: id,
      mode: 'audit',
      styleId: record.style_id
    }
  })
}

const syncIds = ref<any[]>([])
async function handleSync(record: ListData) {
  if (taskIdRef.value) {
    ElMessage({
      type: 'warning',
      message: '正在同步中, 请稍后再试',
    })
    return
  }
  const { id } = record
  syncIds.value.push(id)
  const {
    code,
    data: { taskid },
  } = await syncTemplate({
    "provider_id": `${record.provider_id}`,
    "style_id": `${record.style_id}`,
    "seletedTemplateIds": [
      id
    ],
    "action": "up"
  })
  if (code === 0) {
    taskIdRef.value = taskid;
    resume();
  } else {
    loading.value = false;
  }
}

// const removeIds = ref<any[]>([])
async function handleRemove(record: ListData) {
  if (taskIdRef.value) {
    ElMessage({
      type: 'warning',
      message: '正在同步中, 请稍后再试',
    })
    return
  }
  const { id } = record
  syncIds.value.push(id)
  const {
    code,
    data: { taskid },
  } = await syncTemplate({
    "provider_id": `${record.provider_id}`,
    "style_id": `${record.style_id}`,
    "seletedTemplateIds": [
      id
    ],
    "action": "down"
  })
  if (code === 0) {
    taskIdRef.value = taskid;
    resume();
  } else {
    loading.value = false;
  }
}

async function handleBatchSync() {
  if (taskIdRef.value) {
    ElMessage({
      type: 'warning',
      message: '正在同步中, 请稍后再试',
    })
    return
  }
  const ids = actionRef.value?.getSelectionRowKeys();
  if (ids.length <= 0) {
    ElMessage({
      type: 'warning',
      message: '请选择要批量上架的模板',
    });
    return;
  }
  const action = await ElMessageBox.confirm(`确认要批量上架所选模板吗?`, '批量上架模板');
  syncIds.value.push(...ids.map(i => i.id))
  if (action == 'confirm') {
    const {
      code,
      data: { taskid },
    } = await syncTemplate({
      "provider_id": `${actionRef.value.tableElRef.data[0].provider_id}`,
      "style_id": `${formParams.style_id}`,
      "seletedTemplateIds": ids.map(i => i.id),
      "action": "up"
    })
    if (code === 0) {
      taskIdRef.value = taskid;
      resume();
    } else {
      loading.value = false;
    }
  }
}
async function handleBatchRemove() {
  if (taskIdRef.value) {
    ElMessage({
      type: 'warning',
      message: '正在同步中, 请稍后再试',
    })
    return
  }
  const ids = actionRef.value?.getSelectionRowKeys();
  if (ids.length <= 0) {
    ElMessage({
      type: 'warning',
      message: '请选择要批量下架的模板',
    });
    return;
  }
  const action = await ElMessageBox.confirm(`确认要批量下架所选模板吗?`, '批量下架模板');
  syncIds.value.push(...ids.map(i => i.id))
  if (action == 'confirm') {
    const {
      code,
      data: { taskid },
    } = await syncTemplate({
      "provider_id": `${actionRef.value.tableElRef.data[0].provider_id}`,
      "style_id": `${formParams.style_id}`,
      "seletedTemplateIds": ids.map(i => i.id),
      "action": "down"
    })
    if (code === 0) {
      taskIdRef.value = taskid;
      resume();
    } else {
      loading.value = false;
    }
  }
}

const styleCascaderRef = ref<any>(null)
async function handleHistory() {
  if (formParams.style_id) {
    openModal({
      style_id: formParams.style_id,
    });
  } else {
    ElMessage({
      type: 'warning',
      message: '请选择合作伙伴和素材风格后再查看！',
    });
  }
}

const taskIdRef = ref(null); //异步任务Id
const loading = ref(false);
enum TemplateSyncStatus {
  syncing = 'syncing',
  success = 'success',
  fail = 'fail',
}
const fetchConfigStatus = async (taskId) => {
  const {
    data: { status, error },
  } = await getSyncResult(taskId);
  if (status !== TemplateSyncStatus.syncing) {
    pause();
    loading.value = false;
  }
  if (status === TemplateSyncStatus.success) {
    ElMessage({ message: '操作成功', type: 'success' });
    syncIds.value = []
    taskIdRef.value = null
    reloadTable()
  }
  if (status === TemplateSyncStatus.fail) {
    ElMessage({ type: 'error', message: error || '操作失败' });
    syncIds.value = []
    taskIdRef.value = null
  }
};

const { pause, resume } = useIntervalFn(
  () => {
    fetchConfigStatus(taskIdRef.value);
  },
  2000,
  { immediate: false }
);

const flowSrc = ref('')
function handlePreview(record: ListData) {
  flowSrc.value = router.resolve({
    name: 'template-preview',
    query: {
      mode: 'template-preview',
      templateId: record.id,
      styleId: record.style_id
    }
  }).href
  VFrameVisible.value = true
}

function handleDetail(record: ListData) {
  router.push({
    name: 'template-preview',
    query: {
      templateId: record.id,
      mode: 'detail',
      styleId: record.style_id
    }
  })
}

async function handleUnpublish(record) {
  const { id } = record
  await revisingTemplatePublish(id)
  ElMessage({
    message: '下架成功',
    type: 'success'
  })
  reloadTable()
}

function addTable() {
  dialogVisible.value = true
}

let paramsDraft = null
// 导出
const handleExport = () => {
  const ids = actionRef.value?.getSelectionRowKeys();

  console.log(ids)
  if (hasPermission([PERMISSION_KEYS.auditor.audit_template])) {
    exportFile('/business/export-query/template/list/audit/export', paramsDraft, { ids: ids.map(i => i.id) });
  } else {
    exportFile('/business/export-query/template/list/export', paramsDraft, { ids: ids.map(i => i.id) });
  }
};

const loadDataTable = async (res) => {
  const params = {
    ...formParams,
    ...res,
    provider_id: provider_id.value || currentProviderId.value,
  }
  params.create_start = params.dates?.[0]
  params.create_end = params.dates?.[1]
  delete params.dates
  paramsDraft = params
  let data
  if (hasPermission([PERMISSION_KEYS.operator.template_sync])) {
    data = await getOperateTemplateList(params);
  } else if (hasPermission([PERMISSION_KEYS.auditor.audit_template])) {
    data = await getAuditTemplateList(params);
  } else {
    data = await getTemplateList(params);
  }
  if (data.code === 0) {
    actionRef.value.setSelectionRowKeys([])
    return data.data
  }
  return;
};

watch(formParams, () => {
  reloadTable();
}, { deep: true })

function reloadTable() {
  actionRef.value.updatePage(1)
  batchSyncDisabled.value = true
  batchRemoveDisabled.value = true
}

function handleEdit(record: Recordable) {
  router.push({
    name: 'template-edit',
    query: {
      mode: 'template-edit',
      templateId: record.id,
      styleId: record.style_id
    }
  })
}

async function handleCancelPublish(record) {
  await revokeTemplatePublish(record.id)
  ElMessage({
    message: '撤回成功',
    type: 'success'
  })
  reloadTable();
}

async function handleDelete(record: Recordable) {
  await deleteTemplate(record.id)
  ElMessage({
    message: '删除成功',
    type: 'success'
  })
  reloadTable()
}

async function handleApplyPublish(record) {
  await applyTemplatePublish(record.id)
  ElMessage({
    message: '申请已提交',
    type: 'success'
  })
  reloadTable()
}

const handleBatchDelete = async () => {
  try {
    const ids = actionRef.value?.getSelectionRowKeys();
    if (ids.length <= 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要批量删除的模板',
      });
      return;
    }
    const action = await ElMessageBox.confirm(`确认要批量删除所选模板吗?`, '批量删除模板');
    if (action == 'confirm') {
      const { code } = await batchDeleteTemplate({ ids: ids.map(i => i.id) });
      if (code === 0) {
        ElMessage({
          type: 'success',
          message: '批量删除成功！',
        });
        reloadTable();
      }
    }
  } catch (e) { console.log(e) }
};

</script>

<style lang="less" scoped>
.material-table {
  :deep(.n-data-table-base-table-body) {
    max-height: unset !important;
  }
}
</style>