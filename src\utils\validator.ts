export const validatePhone = (rule, value, callback) => {
  if (!value && rule.required) {
    return callback(new Error('请输入手机号'));
  } else if (value && !/^1[3456789]\d{9}$/.test(value)) {
    return callback(new Error('请输入11位手机号码!'));
  } else {
    callback();
  }
};

export const validateImgCode = (rule, value, callback) => {
  if (!value && rule.required) {
    return callback(new Error('请输入图片验证码'));
  } else if (!/^[\d\w]{4}$/.test(value)) {
    return callback(new Error('图片验证码输入有误,请重新输入!'));
  } else {
    callback();
  }
};

export const validateSmsCode = (rule, value, callback) => {
  if (!value && rule.required) {
    return callback(new Error('请输入短信验证码'));
  } else if (!/^[\d]{6}$/.test(value)) {
    return callback(new Error('短信验证码输入有误,请重新输入!'));
  } else {
    callback();
  }
};
