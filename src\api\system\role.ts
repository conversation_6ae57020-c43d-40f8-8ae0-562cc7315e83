import { http } from '@/utils/http/axios';

/**
 * @description: 角色列表
 */
export function getRoleList(params?) {
  return http.request({
    url: '/business/role',
    method: 'GET',
    params,
  });
}

// 新增角色
export function addRole(params) {
  return http.request({
    url: '/business/role',
    method: 'POST',
    params,
  });
}

// 获取角色详情
export function getRoleDetail(id) {
  return http.request({
    url: `/business/role/${id}`,
    method: 'GET',
  });
}

// 编辑角色
export function editRole(id, params) {
  return http.request({
    url: `/business/role/${id}`,
    method: 'POST',
    params,
  });
}

// 删除角色
export function deleteRole(id) {
  return http.request({
    url: `/business/role/${id}/delete`,
    method: 'POST',
  });
}

// 修改角色状态
export function changeRoleStatus(id, params) {
  return http.request({
    url: `/business/role/${id}/status`,
    method: 'POST',
    params,
  });
}

// 获取权限列表
export function getPermissionList() {
  return http.request({
    url: '/business/permission/template/v3',
    method: 'GET',
  });
}
