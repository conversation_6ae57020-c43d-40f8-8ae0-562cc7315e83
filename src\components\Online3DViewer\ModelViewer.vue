<template>
  <div class="model-viewer-container">
    <canvas ref="canvasRef" class="viewer-canvas"></canvas>
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载模型...</div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { loadModelFromUrl, loadModelFromBuffer } from './engine/modelLoader.js';
import { ElMessage } from 'element-plus';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { TGALoader } from 'three/examples/jsm/loaders/TGALoader.js';

export default {
  props: {
    modelUrl: {
      type: String,
      required: false,
      default: ''
    },
    modelData: {
      type: [ArrayBuffer, null],
      required: false,
      default: null
    },
    modelFormat: {
      type: String,
      required: false,
      default: ''
    },
    autoRotate: {
      type: Boolean,
      default: false
    },
    backgroundColor: {
      type: String,
      default: '#ffffff'
    }
  },
  
  setup(props) {
    const canvasRef = ref(null);
    const loading = ref(true);
    
    // Three.js 对象
    let scene = null;
    let camera = null;
    let renderer = null;
    let controls = null;
    let currentModel = null;
    let animationFrameId = null;
    
    // 渲染场景
    const renderScene = () => {
      if (!renderer || !scene || !camera) return;
      renderer.render(scene, camera);
    };
    
    // 创建默认模型
    const createDefaultModel = (extension) => {
      // 创建一个立方体几何体
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      
      // 创建材质
      const material = new THREE.MeshStandardMaterial({
        color: 0x3366ff,
        metalness: 0.3,
        roughness: 0.5,
      });
      
      // 创建网格
      const cube = new THREE.Mesh(geometry, material);
      
      // 添加一个显示文本的平面
      const canvas = document.createElement('canvas');
      canvas.width = 256;
      canvas.height = 256;
      const context = canvas.getContext('2d');
      context.fillStyle = '#ffffff';
      context.fillRect(0, 0, 256, 256);
      context.font = 'bold 20px Arial';
      context.fillStyle = '#000000';
      context.textAlign = 'center';
      context.fillText(`此 ${extension} 文件加载失败`, 128, 128);
      
      const texture = new THREE.CanvasTexture(canvas);
      const textMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        side: THREE.DoubleSide
      });
      
      const plane = new THREE.Mesh(
        new THREE.PlaneGeometry(2, 0.5),
        textMaterial
      );
      plane.position.set(0, 1.5, 0);
      
      // 创建组合对象
      const group = new THREE.Group();
      group.add(cube);
      group.add(plane);
      
      return group;
    };
    
    // 处理窗口大小调整
    const handleResize = () => {
      if (!canvasRef.value || !camera || !renderer) return;
      
      const canvas = canvasRef.value;
      const width = canvas.clientWidth;
      const height = canvas.clientHeight;
      
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      
      renderer.setSize(width, height, false);
      renderScene();
    };
    
    // 初始化3D查看器
    const initViewer = () => {
      if (!canvasRef.value) return;
      
      try {
        console.log('开始初始化3D查看器');
        
        // 创建场景
        scene = new THREE.Scene();
        
        // 创建相机
        const canvas = canvasRef.value;
        const width = canvas.clientWidth;
        const height = canvas.clientHeight;
        const aspect = width / height || 1;
        
        camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
        camera.position.set(0, 0, 5);
        camera.lookAt(0, 0, 0);
        
        // 创建渲染器
        renderer = new THREE.WebGLRenderer({
          canvas: canvas,
          antialias: true,
          alpha: true
        });
        
        // 设置渲染器大小和背景色
        renderer.setSize(width, height, false);
        renderer.setClearColor(new THREE.Color(props.backgroundColor));
        
        // 创建控制器
        controls = new OrbitControls(camera, canvas);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        controls.addEventListener('change', renderScene);
        
        // 添加灯光
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);
        
        // 初始渲染
        renderScene();
        
        // 添加窗口大小调整事件
        window.addEventListener('resize', handleResize);
        
        console.log('3D查看器初始化成功');
        
        // 加载模型
        if (props.modelUrl) {
          console.log('开始加载模型URL:', props.modelUrl);
          loadModelFromUrlHandler(props.modelUrl);
        } else if (props.modelData && props.modelFormat) {
          console.log('开始从Buffer加载模型');
          loadModelFromData(props.modelData, props.modelFormat);
        } else {
          console.log('没有提供模型数据');
          loading.value = false;
        }
        
        // 如果需要自动旋转，启动动画循环
        if (props.autoRotate) {
          startAutoRotate();
        }
      } catch (err) {
        console.error('初始化3D查看器失败:', err);
        ElMessage.error('初始化3D查看器失败，请刷新页面重试');
        loading.value = false;
      }
    };
    
    // 自动旋转
    const startAutoRotate = () => {
      const rotate = () => {
        if (!scene || !camera || !renderer || !currentModel) {
          animationFrameId = requestAnimationFrame(rotate);
          return;
        }
        
        currentModel.rotation.y += 0.01;
        renderScene();
        
        animationFrameId = requestAnimationFrame(rotate);
      };
      
      animationFrameId = requestAnimationFrame(rotate);
    };
    
    // 从URL加载模型
    const loadModelFromUrlHandler = async (url) => {
      if (!scene) return;
      
      try {
        loading.value = true;
        
        if (!url) {
          throw new Error('模型URL为空');
        }
        
        console.log('原始URL:', url);
        
        // 处理URL，确保它是完整的URL
        let fullUrl = url;
        
        // 如果是相对路径（不以http开头），转换为完整URL
        if (!url.startsWith('http') && !url.startsWith('https')) {
          // 如果以/开头，则使用当前域名
          if (url.startsWith('/')) {
            fullUrl = `${window.location.origin}${url}`;
          } else {
            // 否则视为相对于当前路径的URL
            fullUrl = `${window.location.origin}/${url}`;
          }
        }
        
        console.log('处理后的完整URL:', fullUrl);
        
        // 移除URL中的查询参数，获取纯净的URL
        const cleanUrl = url.split('?')[0];
        const extension = cleanUrl.split('.').pop().toLowerCase();
        
        console.log('检测到文件扩展名:', extension);
        
        // 捕获404错误，尝试修复URL
        const checkUrl = async (testUrl) => {
          try {
            const response = await fetch(testUrl, { method: 'HEAD' });
            console.log('URL检查结果:', response.status, response.statusText);
            return response.ok;
          } catch (error) {
            console.error('URL检查失败:', error);
            return false;
          }
        };
        
        // 如果文件不存在，尝试不同的URL格式
        let fileExists = await checkUrl(fullUrl);
        if (!fileExists) {
          console.warn('直接访问URL失败，尝试不同的URL格式');
          
          // 尝试替代方案1: 移除URL中的assets前缀
          let alternativeUrl = fullUrl;
          if (alternativeUrl.includes('/assets/')) {
            alternativeUrl = alternativeUrl.replace('/assets/', '/');
            console.log('尝试替代URL 1:', alternativeUrl);
            fileExists = await checkUrl(alternativeUrl);
            
            if (fileExists) {
              fullUrl = alternativeUrl;
              console.log('替代URL 1可访问');
            }
          }
          
          // 尝试替代方案2: 如果还是不行，检查是否有域名部分，尝试移除
          if (!fileExists && fullUrl.includes('://')) {
            const urlParts = fullUrl.split('/');
            const domainless = urlParts.slice(3).join('/');
            alternativeUrl = `${window.location.origin}/${domainless}`;
            console.log('尝试替代URL 2:', alternativeUrl);
            fileExists = await checkUrl(alternativeUrl);
            
            if (fileExists) {
              fullUrl = alternativeUrl;
              console.log('替代URL 2可访问');
            }
          }
          
          // 如果所有尝试都失败，使用测试模型
          if (!fileExists) {
            console.warn('所有URL尝试都失败，使用测试模型替代');
            loadTestModel(extension);
            return;
          }
        }
        
        // 对于bundle或fbx文件，使用FBXLoader
        if (extension === 'bundle' || extension === 'fbx') {
          console.log('检测到FBX/Bundle文件，使用FBXLoader加载:', fullUrl);
          
          // 创建加载管理器，处理纹理加载错误
          const loadingManager = new THREE.LoadingManager();
          loadingManager.onError = function(url) {
            console.warn('资源加载失败:', url);
            // 不显示错误消息，只记录日志
          };
          
          // 添加TGA加载器支持
          const tgaLoader = new TGALoader(loadingManager);
          THREE.DefaultLoadingManager.addHandler(/\.tga$/i, tgaLoader);
          
          // 创建一个新的FBXLoader实例
          const fbxLoader = new FBXLoader(loadingManager);
          
          try {
            // 直接加载模型
            fbxLoader.load(
              fullUrl,
              (object) => {
                console.log('FBX加载成功:', object);
                
                // 移除当前模型
                if (currentModel && scene) {
                  scene.remove(currentModel);
                  clearModelResources(currentModel);
                }
                
                // 处理模型比例和位置
                if (object) {
                  // 替换所有材质为基础材质，避免纹理问题
                  object.traverse((child) => {
                    if (child.isMesh) {
                      console.log('处理网格:', child.name);
                      
                      // 保存原始材质
                      const originalMaterial = child.material;
                      
                      // 创建新的基础材质
                      const newMaterial = new THREE.MeshStandardMaterial({
                        color: 0x808080,
                        metalness: 0.3,
                        roughness: 0.7,
                        side: THREE.DoubleSide
                      });
                      
                      // 如果原始材质有颜色，则使用原始颜色
                      if (originalMaterial && originalMaterial.color) {
                        newMaterial.color = originalMaterial.color;
                      }
                      
                      // 应用新材质
                      child.material = newMaterial;
                    }
                  });
                  
                  // 计算模型的边界框
                  const box = new THREE.Box3().setFromObject(object);
                  const size = box.getSize(new THREE.Vector3());
                  const center = box.getCenter(new THREE.Vector3());
                  
                  console.log('模型尺寸:', size);
                  console.log('模型中心:', center);
                  
                  // 计算合适的缩放比例
                  const maxDim = Math.max(size.x, size.y, size.z);
                  const scale = 3 / maxDim; // 增大缩放比例
                  object.scale.set(scale, scale, scale);
                  
                  // 居中模型
                  object.position.set(-center.x * scale, -center.y * scale, -center.z * scale);
                  
                  // 保存模型引用
                  currentModel = object;
                  
                  // 添加模型到场景
                  scene.add(object);
                  console.log('模型已添加到场景');
                  
                  // 调整相机位置
                  camera.position.set(0, 0, 5);
                  camera.lookAt(0, 0, 0);
                  
                  // 重置控制器
                  controls.reset();
                  
                  // 渲染场景
                  renderScene();
                }
                
                loading.value = false;
              },
              (progress) => {
                const percent = progress.loaded / progress.total * 100;
                console.log(`加载进度: ${Math.floor(percent)}%`);
              },
              (error) => {
                console.error('FBX加载失败，详细错误:', error);
                ElMessage.error(`加载失败: ${error.message || '未知错误'}`);
                
                // 创建默认模型
                loadTestModel(extension);
              }
            );
            return;
          } catch (error) {
            console.error('FBX加载器错误:', error);
            ElMessage.error(`FBX加载器错误: ${error.message || '未知错误'}`);
            // 继续尝试其他加载器
          }
        }
        
        // 如果不是bundle或fbx文件，或者FBX加载失败，使用常规加载器
        const model = await loadModelFromUrl(url, (progress) => {
          console.log(`加载进度: ${Math.floor(progress * 100)}%`);
        });
        
        // 移除当前模型
        if (currentModel && scene) {
          scene.remove(currentModel);
          
          // 清理旧模型资源
          if (currentModel.geometry) {
            currentModel.geometry.dispose();
          }
          
          if (currentModel.material) {
            if (Array.isArray(currentModel.material)) {
              currentModel.material.forEach(m => m.dispose());
            } else {
              currentModel.material.dispose();
            }
          }
        }
        
        // 添加新模型
        if (model) {
          currentModel = model;
          scene.add(model);
          console.log('模型加载成功');
          
          // 渲染场景
          renderScene();
        } else {
          console.warn('模型对象为空');
        }
        
        loading.value = false;
      } catch (err) {
        console.error('加载模型失败:', err);
        ElMessage.error(`加载模型失败: ${err?.message || '未知错误'}`);
        loading.value = false;
      }
    };
    
    // 从数据加载模型
    const loadModelFromData = async (data, format) => {
      if (!scene) return;
      
      try {
        loading.value = true;
        
        // 加载模型
        const model = await loadModelFromBuffer(data, format);
        
        // 移除当前模型
        if (currentModel && scene) {
          scene.remove(currentModel);
          
          // 清理旧模型资源
          if (currentModel.geometry) {
            currentModel.geometry.dispose();
          }
          
          if (currentModel.material) {
            if (Array.isArray(currentModel.material)) {
              currentModel.material.forEach(m => m.dispose());
            } else {
              currentModel.material.dispose();
            }
          }
        }
        
        // 添加新模型
        if (model) {
          currentModel = model;
          scene.add(model);
          
          // 渲染场景
          renderScene();
        }
        
        loading.value = false;
      } catch (err) {
        console.error('加载模型数据失败:', err);
        ElMessage.error(`加载模型数据失败: ${err?.message || '未知错误'}`);
        loading.value = false;
      }
    };
    
    // 清理模型资源
    const clearModelResources = (model) => {
      if (!model) return;
      
      model.traverse((child) => {
        if (child.geometry) {
          child.geometry.dispose();
        }
        
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(m => {
              if (m.map) m.map.dispose();
              if (m.normalMap) m.normalMap.dispose();
              if (m.specularMap) m.specularMap.dispose();
              if (m.envMap) m.envMap.dispose();
              m.dispose();
            });
          } else {
            if (child.material.map) child.material.map.dispose();
            if (child.material.normalMap) child.material.normalMap.dispose();
            if (child.material.specularMap) child.material.specularMap.dispose();
            if (child.material.envMap) child.material.envMap.dispose();
            child.material.dispose();
          }
        }
      });
    };
    
    // 加载测试模型
    const loadTestModel = (extension) => {
      console.warn('加载测试模型');
      
      // 使用测试模型
      const testModelUrl = 'https://threejs.org/examples/models/fbx/Samba%20Dancing.fbx';
      console.log('加载测试模型:', testModelUrl);
      
      // 创建加载管理器，处理纹理加载错误
      const loadingManager = new THREE.LoadingManager();
      loadingManager.onError = function(url) {
        console.warn('测试模型资源加载失败:', url);
      };
      
      // 对于bundle或fbx文件，使用FBXLoader
      const fbxLoader = new FBXLoader(loadingManager);
      
      fbxLoader.load(
        testModelUrl,
        (object) => {
          console.log('测试模型加载成功:', object);
          
          // 移除当前模型
          if (currentModel && scene) {
            scene.remove(currentModel);
            clearModelResources(currentModel);
          }
          
          // 处理模型
          if (object) {
            // 计算边界
            const box = new THREE.Box3().setFromObject(object);
            const size = box.getSize(new THREE.Vector3());
            const center = box.getCenter(new THREE.Vector3());
            
            // 缩放
            const maxDim = Math.max(size.x, size.y, size.z);
            const scale = 2 / maxDim;
            object.scale.set(scale, scale, scale);
            
            // 居中
            object.position.set(-center.x * scale, -center.y * scale, -center.z * scale);
            
            // 保存引用
            currentModel = object;
            
            // 添加到场景
            scene.add(object);
            console.log('测试模型已添加到场景');
            
            // 调整相机位置
            camera.position.set(0, 0, 5);
            camera.lookAt(0, 0, 0);
            
            // 重置控制器
            controls.reset();
            
            // 渲染
            renderScene();
          }
          
          loading.value = false;
        },
        (progress) => {
          const percent = progress.loaded / progress.total * 100;
          console.log(`测试模型加载进度: ${Math.floor(percent)}%`);
        },
        (error) => {
          console.error('测试模型加载失败:', error);
          
          // 创建默认模型
          const defaultModel = createDefaultModel(extension);
          if (currentModel && scene) {
            scene.remove(currentModel);
          }
          currentModel = defaultModel;
          scene.add(defaultModel);
          renderScene();
          loading.value = false;
        }
      );
    };
    
    // 重置相机
    const resetCamera = () => {
      if (!camera || !controls) return;
      
      camera.position.set(0, 0, 5);
      camera.lookAt(0, 0, 0);
      controls.reset();
      renderScene();
    };
    
    // 监听属性变化
    watch(() => props.backgroundColor, (newColor) => {
      if (renderer) {
        renderer.setClearColor(new THREE.Color(newColor));
        renderScene();
      }
    });
    
    watch(() => props.autoRotate, (newValue) => {
      if (newValue) {
        if (!animationFrameId) {
          startAutoRotate();
        }
      } else {
        if (animationFrameId) {
          cancelAnimationFrame(animationFrameId);
          animationFrameId = null;
        }
      }
    });
    
    watch(() => props.modelUrl, (newUrl) => {
      if (newUrl) {
        loadModelFromUrlHandler(newUrl);
      }
    });
    
    // 清理资源
    const cleanup = () => {
      // 停止动画循环
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }
      
      // 清理模型资源
      if (currentModel) {
        if (currentModel.geometry) {
          currentModel.geometry.dispose();
        }
        
        if (currentModel.material) {
          if (Array.isArray(currentModel.material)) {
            currentModel.material.forEach(m => m.dispose());
          } else {
            currentModel.material.dispose();
          }
        }
        
        currentModel = null;
      }
      
      // 清空场景
      if (scene) {
        while (scene.children.length > 0) {
          const child = scene.children[0];
          scene.remove(child);
        }
        scene = null;
      }
      
      // 清理渲染器
      if (renderer) {
        renderer.dispose();
        renderer = null;
      }
      
      // 清理控制器
      if (controls) {
        controls.dispose();
        controls = null;
      }
      
      // 移除事件监听器
      window.removeEventListener('resize', handleResize);
      
      camera = null;
    };
    
    // 生命周期钩子
    onMounted(() => {
      // 延迟初始化，确保DOM已完全渲染
      setTimeout(() => {
        initViewer();
      }, 200);
    });
    
    onBeforeUnmount(() => {
      cleanup();
    });
    
    // 对外暴露方法
    return {
      canvasRef,
      loading,
      resetCamera,
      renderScene
    };
  }
};
</script>

<style scoped>
.model-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px;
  overflow: hidden;
}

.viewer-canvas {
  width: 100%;
  height: 100%;
  display: block;
  background-color: transparent;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 10;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #333;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 