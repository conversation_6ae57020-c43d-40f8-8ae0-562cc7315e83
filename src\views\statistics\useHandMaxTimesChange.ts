import { ElMessage } from 'element-plus';

export function useHandleMaxTimeChange(formValue) {
  const handleMaxTimesChange = (value) => {
    console.log(formValue.min_times && value >= 0 && value < formValue.min_times)
    if (formValue.min_times && value >= 0 && value < formValue.min_times) {
      ElMessage({
        type: 'warning',
        message: '范围结束值应该不小于起始值！',
      });
      return false
    }
    return true
  };
  const handleMinTimesChange = (value) => {
    if (formValue.max_times >= 0 && value >= 0 && value > formValue.max_times) {
      ElMessage({
        type: 'warning',
        message: '范围结束值应该不小于起始值！',
      });
      return false
    }
    return true
  };
  return [handleMaxTimesChange, handleMinTimesChange];
}
