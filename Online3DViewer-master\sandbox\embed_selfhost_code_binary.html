<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no">

	<title>Online 3D Viewer</title>

	<script type="text/javascript" src="../build/engine_dev/o3dv.min.js"></script>

    <style>
        div.online_3d_viewer
        {
            float: left;
            border: 1px solid #eeeeee;
            width: 640px;
            height: 480px;
        }
    </style>

    <script type='text/javascript'>
        window.addEventListener ('load', () => {
            // get the parent element of the viewer
            let parentDiv = document.getElementById ('viewer');

            // initialize the viewer with the parent element and some parameters
            let viewer = new OV.EmbeddedViewer (parentDiv);

            // download the file and load it's content as an ArrayBuffer
            fetch ('../../test/testfiles/obj/hundred_cubes.obj')
            .then (response => response.arrayBuffer ())
            .then ((arrayBuffer) => {
                let fileList = [
                    new File ([arrayBuffer], 'file.obj')
                ];
                viewer.LoadModelFromFileList (fileList);
            });
        });
    </script>
</head>

<body>
    <div class="online_3d_viewer" id="viewer">
    </div>
</body>

</html>
