<template>
    <el-card class="n-layout-page-header">
      <el-form :model="form" ref="formRef" label-width="120px" :rules="rules">
        <el-form-item label="风格名称" required prop="title">
          <el-input
            v-model="form.title"
            maxlength="100"
            placeholder="请输入风格名称"
            @input="(value) => handleInput('title', value)"
          />
        </el-form-item>
        <el-form-item label="缩略图" required prop="icon_file_id">
          <CustomUpload
            v-model="fileInfo"
            scope="asset_icon"
            :show-file-list="false"
            list-type="picture-card"
            accept=".jpeg,.png,.jpg"
            :maxSize="4"
          >
            <template v-if="fileInfo.url">
                <img alt="图片" :src="fileInfo.url" class="avatar">
            </template>
            <template v-else>
              <el-icon size="26"><Plus /></el-icon>
              <div class="el-upload__text">
                点击或拖拽文件上传
              </div>
            </template>
            <template #tip>
                <div class="el-upload__tip">
                建议尺寸：640*320，小于4M的jpg、jpeg、png格式
                </div>
            </template>
          </CustomUpload>
        </el-form-item>
        <el-form-item label="所属合作伙伴" required prop="provider_id">
          <el-select v-model="form.provider_id">
            <el-option
              v-for="item in providers"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="风格编码" required prop="style_code">
          <el-input
            maxlength="100"
            v-model="form.style_code" 
            @input="(value) => handleInput('style_code', value)"
          />
        </el-form-item>
        <el-form-item label="备注说明" >
          <el-input
            v-model="form.description"
            type="textarea"
            rows="6"
            maxlength="100"
            show-word-limit
            @input="(value) => handleInput('description', value)"
          />
        </el-form-item>
      </el-form>
      <el-button @click="submit">提交</el-button>
      <el-button @click="goback">返回</el-button>
    </el-card>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Plus } from '@element-plus/icons-vue'
  import { addProviderStyle, editProviderStyle, getProviderStyle} from '@/api/provider-style'
  import { storeToRefs } from 'pinia';
  import { useUserStore } from '@/store/modules/user';
  import CustomUpload from './components/CustomUpload.vue'
  import { ElMessage } from 'element-plus';
  import type {FormInstance} from 'element-plus'
  import { useFilterInputHander } from '../hooks';

  const userStore = useUserStore();
  const { providers, currentProviderId } = storeToRefs(userStore);
  const router = useRouter()
  const {query} = useRoute();
  const formRef = ref<FormInstance>()
  const fileInfo = ref<any>({
    url: '',
  })
  const form = reactive({
      title: '',
      style_code: '',
      description: '',
      provider_id: currentProviderId.value,
      icon_file_id: '',
      icon_url: '',
      icon: {
        originalname: '',
      },
  })
  const handleInput = useFilterInputHander(form);

  watch(fileInfo, () =>{
    form.icon_file_id = fileInfo.value.id
    form.icon_url = fileInfo.value.url
    formRef.value?.validateField('icon_file_id')
  })

  const rules = {
    title: [
      { required: true, message: '请输入风格名称', trigger: 'blur' },
    ],
    style_code: [
      { required: true, message: '请输入风格编码', trigger: 'blur' },
    ],
    provider_id: [
      { required: true, message: '请选择所属合作伙伴', trigger: 'change' },
    ],
    icon_file_id: [
      {
        validator: (_, __, callback) => {
          if (!fileInfo.value.id) {
            callback(new Error('请上传缩略图'))
          } else {
            callback()
          }
        }
      }
    ]
  }
  const {id} = query
  onMounted(async () => {
    if (id) {
      const res = await getProviderStyle(id)
      for (let k in form) {
        form[k] = res.data[k]
      }
      fileInfo.value.url = form.icon_url;
      fileInfo.value.id = form.icon_file_id;
    }
  })

  async function submit() {
    if (!formRef.value) return
    formRef.value.validate(async (valid) => {
      if (valid) {
        if (id) {
          await editProviderStyle(id, form)
          ElMessage({
            message: '修改成功！',
            type: 'success'
          })
        } else {
          await addProviderStyle(form)
          ElMessage({
            message: '新增成功！',
            type: 'success'
          })
        }
        setTimeout(() => {
          goback()
        }, 1000)
      }
    })
  }

  function goback() {
    router.push({ path: '/style/list'});
  }

</script>

<style lang="less" scoped>
  .upload-demo {
    :deep(.el-upload--picture-card) {
      --el-upload-picture-card-size: unset;
      .el-upload-dragger {
        height: 100%;
        .avatar {
          max-width: 250px;
          max-height: 250px;
        }
      }
    }
    :deep(.el-upload-list--picture-card) {
      min-height: 156px;
      .el-upload--picture-card:nth-child(2) {
        display: none;
      }
    }
  }
</style>
