<template>
  <el-card class="box-card" body-style="padding-bottom: 0">
    <el-form :inline="true" :model="topQueryParams" class="demo-form-inline flex flex-1">
      <el-form-item label="合作伙伴：">
        <el-select v-model="topQueryParams.provider_id" placeholder="请选择合作伙伴">
          <el-option
            v-for="item in providerOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            clearable
          />
        </el-select>
      </el-form-item>
      <el-form-item label="素材风格：">
        <el-select v-model="topQueryParams.style_id" placeholder="请选择素材风格">
          <el-option
            v-for="item in styleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            clearable
          />
        </el-select>
      </el-form-item>
      <el-button class="ml-auto" @click="handleOpenModelTable">查看同步记录</el-button>
    </el-form>
    <el-tabs v-model="lQueryParams.tab" @tab-change="handleTabsChange" class="material-tabs">
      <el-tab-pane v-for="item in tabs" :key="item.name" :label="item.title" :name="item.name" />
    </el-tabs>
    <el-row>
      <el-col :span="4" class="tree-wrap pt-4">
        <el-tree
          :data="showTreeData"
          :props="defaultProps"
          node-key="key"
          :default-expand-all="true"
          @node-click="handleNodeClick"
        />
      </el-col>
      <el-col :span="20" class="pl-4">
        <el-row :gutter="20" class="h-full">
          <el-col :span="12" class="left-table-wrap pb-8">
            <div class="flex justify-between items-center">
              <h4 class="mr-2">可选素材{{ leftTableData.length }}个</h4>
              <div class="flex">
                <el-form-item label="制作CP：" class="mr-2">
                  <el-select v-model="lQueryParams.user_id" clearable placeholder="请选择制作CP">
                    <el-option key="" label="全部" value="" />
                    <el-option
                      v-for="item in cpUersOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      clearable
                    />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-input
                    :prefix-icon="Search"
                    v-model.trim="lQueryParams.name"
                    placeholder="请输入素材名称或bundle路径"
                    clearable
                  />
                </el-form-item>
              </div>
            </div>
            <el-table
              :data="leftTableData"
              table-layout="fixed"
              ref="leftTableRef"
              style="width: 100%"
              row-key="id"
              @select="handleSelect"
              @select-all="handleSelectAll"
            >
              <el-table-column type="selection" width="40" />
              <el-table-column label="可选素材" :show-overflow-tooltip="true">
                <template #default="scope">
                  <div class="flex">
                    <img alt="图片" :src="scope.row.icon_url" class="img" />
                    <div class="title">
                      <div>{{ scope.row.name }}</div>
                      <div>{{ scope.row.file_path }}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="制作CP" prop="user.name" width="100" />
              <el-table-column label="更新时间" prop="updated_at" :formatter="formateDateTime" />
            </el-table>
          </el-col>
          <el-col :span="12" class="right-table-wrap">
            <div class="flex flex-nowrap justify-between items-center">
              <h4 class="">已选素材{{ allSelected.length }}个</h4>
              <div class="flex">
                <el-input
                  class="mr-3"
                  :prefix-icon="Search"
                  v-model.trim="rQueryParams.name"
                  placeholder="请输入素材名称或bundle路径"
                  clearable
                />
                <el-button type="danger" @click="removeAll">全部删除</el-button>
                <el-button :loading="loading" type="primary" @click="handleSubmit"
                  >确认提交</el-button
                >
              </div>
            </div>
            <el-table
              :data="rightTableData"
              table-layout="fixed"
              row-key="id"
              id="dragTable"
              ref="rightTableRef"
              style="width: 100%"
              :header-row-style="{ background: '#e5e7eb' }"
            >
              <el-table-column label="已选素材" :show-overflow-tooltip="true">
                <template #default="scope">
                  <div class="flex items-center">
                    <el-icon class="mr-2">
                      <Operation />
                    </el-icon>
                    <img alt="图片" :src="scope.row.icon_url" class="img" />
                    <div class="title">
                      <div>{{ scope.row.name }}</div>
                      <div>{{ scope.row.file_path }}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="制作CP" prop="user.name" />
              <el-table-column label="更新时间" prop="updated_at" :formatter="formateDateTime" />
              <el-table-column label="操作" width="160" class-name="option-group">
                <template #default="{ $index, row }">
                  <el-button type="danger" :text="true" @click="handleDelete(row)">删除</el-button>
                  <el-button
                    type="primary"
                    :text="true"
                    :disabled="$index == 0"
                    @click="handleTopping($index, row)"
                    >置顶</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </el-card>
  <AsyncActionTable @register="register" ref="modalRef" />
</template>

<script lang="ts" setup>
  import { getAssetTypeList } from '@/api/material/material';
  import {
    getAssetsList,
    getConfigTapOptionList,
    getSyncStatus,
    syncOperationConfig,
  } from '@/api/operator';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { Operation, Search } from '@element-plus/icons-vue';
  import { useIntervalFn } from '@vueuse/core';
  import { ElMessage } from 'element-plus';
  import Sortable from 'sortablejs';
  import { computed, nextTick, onMounted, reactive, ref, toRaw, watch } from 'vue';
  import AsyncActionTable from './AsyncActionTable.vue';
  import useModalTable from './hooks/useModalTable';

  type OptionType = Array<{ value: number; label: string; pid: string }>;

  const providerOptions = ref<OptionType>([]);
  const cpUersOptions = ref<OptionType>([]);
  const allStyleOptions = ref<OptionType>([]);
  const treeData = ref([]);
  const leftTableRef = ref();
  const rightTableRef = ref();
  let allData = []; // 存原始数据不需要响应式
  const allSelected = ref<any>([]); // 已选数据
  const allSelectedSet = new Set(); // 所以已选数据的id,辅助查询
  const leftTableData = ref<any>([]);
  const rightTableData = ref<any>([]);
  const taskIdRef = ref(null); //异步任务Id

  const loading = ref(false);

  const tabs = [
    {
      title: '男生',
      name: 'male',
    },
    {
      title: '女生',
      name: 'female',
    },
    {
      title: '通用',
      name: 'others',
    },
  ];

  const [register, { openModal }] = useModalTable({
    title: '查看同步记录',
  });

  const handleOpenModelTable = () => {
    if (topQueryParams.provider_id && topQueryParams.style_id) {
      openModal({
        provider_id: topQueryParams.provider_id,
        style_id: topQueryParams.style_id,
      });
    } else {
      ElMessage({
        type: 'warning',
        message: '请选择合作伙伴和素材风格后再查看！',
      });
    }
  };
  const handleTabsChange = (name) => {
    lQueryParams.tab = name;
    lQueryParams.keys = [];
  };

  const handleNodeClick = (data) => {
    const node = toRaw(data);
    let keys: any[] = [];
    if (node.children) {
      let firstNodeKey = node.children[0]?.key ?? '';
      keys = firstNodeKey.split(/\-/);
    } else if (node.title == '全部') {
      let firstNodeKey = node?.key ?? '';
      keys = firstNodeKey.split(/\-/);
    } else if (node.key) {
      keys = [node.key];
    }
    lQueryParams.keys = keys;
  };

  const defaultProps = {
    children: 'children',
    label: 'title',
  };

  const styleOptions = computed(() => {
    return allStyleOptions.value.filter(({ pid }) => pid == topQueryParams.provider_id);
  });

  const formatTreeData = (data) => {
    const result = data.reduce((prev, item) => {
      const newNode: any = {
        title: item.chinese,
        key: item.name,
      };
      if (item.children) {
        newNode.children = [
          {
            title: `全部`,
            key: `${item.children.map((it) => it.name).join('-')}-all`,
          },
          ...formatTreeData(item.children),
        ];
      }
      prev.push(newNode);
      return prev;
    }, []);
    return result;
  };

  const lQueryParams = reactive<any>({
    user_id: '',
    name: '',
    tab: 'male',
    keys: [],
  });

  const rQueryParams = reactive({
    name: '',
  });

  const formateDateTime = (_row, _col, value) => {
    return formatToDateTime(value);
  };

  const fetchOptionList = async () => {
    const { data } = await getConfigTapOptionList();
    const { abilityProviderRows, cpUserRows, providerStyleRows } = data;
    providerOptions.value = abilityProviderRows.map(({ id: value, title: label }) => ({
      label,
      value,
    }));
    cpUersOptions.value = cpUserRows.map(({ name: label, user_id: value }) => ({
      label,
      value,
    }));
    allStyleOptions.value = providerStyleRows.map(
      ({ id: value, provider_id: pid, title: label }) => ({ label, value, pid })
    );

    topQueryParams.provider_id = providerOptions.value?.[0]?.value;
    topQueryParams.style_id = styleOptions.value?.[0]?.value;
  };

  const fetchTreedata = async () => {
    const { data } = await getAssetTypeList();
    const result = formatTreeData(data);
    treeData.value = result.map((item) => ({
      ...item,
      key: item.key + 'a',
    }));
  };

  type TreeNode = { key: string; children: []; title: string };

  const showTreeData = computed(() => {
    const target = lQueryParams.tab;
    return treeData.value.reduce((prev: TreeNode[], item: TreeNode) => {
      if (target === 'others') {
        if (item.key === 'othersa') {
          prev.push(
            ...item.children.filter(
              (child: TreeNode) =>
                // 除了这些其他的展示在“其他分类”
                !['animation', 'body', 'ARmask'].includes(child.key)
            )
          );
        }
      } else {
        if (item.key === 'othersa') {
          prev.push({
            title: '其他',
            key: 'others',
            children: item.children.filter((child: TreeNode) =>
              // 这些展示在男/女的其他分类里
              ['animation', 'body', 'ARmask', 'camera'].includes(child.key)
            ),
          } as TreeNode);
        } else {
          prev.push(item);
        }
      }
      return prev;
    }, []);
  });

  const filterByQuery = (list: any[] = [], query: any = { tab: 'male', name: '', user_id: '' }) => {
    const keys = Object.keys(query);
    let result = list;
    if (keys.includes('tab')) {
      result = result.filter(({ gender }) => {
        if (gender == 'all' && query.tab == 'others') {
          return true;
        } else if (gender == query.tab) {
          return true;
        } else {
          return false;
        }
      });
    }

    if (keys.includes('keys') && query.keys.length > 0) {
      result = result.filter(({ type }) => {
        return query.keys.includes(type);
      });
    }

    if (keys.includes('user_id') && query.user_id) {
      result = result.filter(({ user_id }) => {
        if (!query.user_id) {
          return true;
        } else if (user_id == query.user_id) {
          return true;
        } else {
          return false;
        }
      });
    }

    if (keys.includes('name')) {
      result = result.filter(({ name = '', file_path = '' }) => {
        if (!query.name) {
          return true;
        } else {
          return name?.includes(query.name) || file_path?.includes(query.name);
        }
      });
    }
    return result;
  };
  // 监听右表搜索
  watch(
    () => rQueryParams.name,
    () => {
      getRightTableData();
    }
  );

  const getRightTableData = () => {
    const finalParams = {
      ...toRaw(lQueryParams),
      ...toRaw(rQueryParams),
    };
    rightTableData.value = filterByQuery(allSelected.value, finalParams);
    finalParams.name = ''
    selected.value = filterByQuery(allSelected.value, finalParams);
  };

  const selected = ref<any[]>([])

  const fetchAssetList = async () => {
    const { provider_id, style_id } = topQueryParams;
    if (provider_id && style_id) {
      const params = { provider_id, style_id };
      const {
        code,
        data: { rows: list },
      } = await getAssetsList(params);
      if (code === 0) {
        allData = list;
        leftTableData.value = filterByQuery(allData, toRaw(lQueryParams)).filter(
          ({ deleted_cp_user }) => !deleted_cp_user
        );
        allSelected.value = list
          .filter(({ selected }) => selected)
          .sort((a, b) => a.order - b.order);
        allSelected.value.forEach(({ id }) => allSelectedSet.add(id)); // 向allSelectedSet添加已选数据
        nextTick(() => {
          leftTableData.value.forEach((row: any) => {
            if (allSelectedSet.has(row.id)) {
              leftTableRef.value?.toggleRowSelection(row, true);
            }
          });
        });
        getRightTableData();
      }
    }
  };

  const topQueryParams = reactive<{ style_id: string | number; provider_id: string | number }>({
    provider_id: '',
    style_id: '',
  });
  // 左表变化
  watch(
    [
      () => lQueryParams.tab,
      () => lQueryParams.keys,
      () => lQueryParams.user_id,
      () => lQueryParams.name,
    ],
    () => {
      const rows = filterByQuery(allData, toRaw(lQueryParams));
      leftTableData.value = rows.filter(({ deleted_cp_user }) => !deleted_cp_user);
      nextTick(() => {
        rows.forEach((row: any) => {
          if (allSelectedSet.has(row.id)) {
            leftTableRef.value?.toggleRowSelection(row, true);
          }
        });
        getRightTableData();
      });
    }
  );

  watch(
    () => topQueryParams.provider_id,
    () => {
      topQueryParams.style_id = styleOptions?.value?.[0]?.value ?? '';
    }
  );

  watch(
    [() => topQueryParams.provider_id, () => topQueryParams.style_id],
    ([newProviderId, newStyleId], [oldProviderId, oldStyleId]) => {
      // 清空列表
      allData = [];
      leftTableData.value = [];
      allSelected.value = [];
      allSelectedSet.clear();
      if (
        newProviderId &&
        newStyleId &&
        (newProviderId !== oldProviderId || newStyleId !== oldStyleId)
      ) {
        fetchAssetList();
      }
    }
  );

  // 左表全选
  const handleSelectAll = (selection) => {
    if (selection.length > 0) {
      const selectionIds = selection.map(({ id }) => id);
      selectionIds.forEach((id) => allSelectedSet.add(id)); // 添加选择中的id
      const notInSelection = allSelected.value.filter(({ id }) => !selectionIds.includes(id));
      allSelected.value = [...notInSelection, ...selection];
    } else {
      const leftTableIds = leftTableData.value.map(({ id }) => id);
      leftTableIds.forEach((id) => allSelectedSet.delete(id)); // 更新Set
      allSelected.value = allSelected.value.filter(({ id }) => allSelectedSet.has(id));
    }
    getRightTableData();
  };

  // 左侧表格单选
  const handleSelect = (selection, row) => {
    const selectedIds = selection?.map(({ id }) => id);
    const isSelected = selectedIds?.includes(row.id);
    const currentRow: any = leftTableData.value?.find(({ id }) => id == row.id);
    if (currentRow) {
      currentRow.selected = isSelected;
    }
    const indexInAllSelected = allSelected.value.findIndex(({ id }) => id == row.id);
    if (indexInAllSelected > -1) {
      // 存在
      if (!isSelected) {
        // 未选中
        allSelected.value.splice(indexInAllSelected, 1); // 删除
        allSelectedSet.delete(row.id);
      }
    } else {
      if (isSelected && currentRow) {
        allSelected.value.push(currentRow);
        allSelectedSet.add(row.id);
      }
    }
    getRightTableData();
  };
  // 右表删除
  const removeAll = () => {
    const willRemoveIds: string[] = rightTableData.value.map(({ id }) => id);
    willRemoveIds.forEach((id) => allSelectedSet.delete(id));
    allSelected.value = allSelected.value.filter(({ id }) => !willRemoveIds.includes(id));
    leftTableRef.value?.clearSelection?.();
    rightTableData.value = [];
    selected.value = [];
  };

  //右表删除
  const handleDelete = (row) => {
    const currentRow = leftTableData.value.find(({ id }) => id == row.id);
    if (currentRow) {
      leftTableRef.value.toggleRowSelection(currentRow);
    }
    allSelectedSet.delete(row.id);
    const indexInAllSelected = allSelected.value.findIndex(({ id }) => id == row.id);
    if (indexInAllSelected > -1) {
      allSelected.value.splice(indexInAllSelected, 1);
    }
    getRightTableData();
  };

  //置顶
  const handleTopping = (index, row) => {
    const targetRow = rightTableData.value?.splice(index, 1)[0];
    rightTableData.value.splice(0, 0, targetRow);
    const indexInAllSelected = allSelected.value.findIndex(({ id }) => id == row.id);
    if (indexInAllSelected > -1) {
      const [item] = allSelected.value.splice(indexInAllSelected, 1);
      allSelected.value.splice(0, 0, item);
    }
    getRightTableData();
  };

  const setSort = () => {
    const el = document.querySelector('#dragTable table tbody');
    new Sortable(el, {
      swap: true,
      ghostClass: 'sortable-ghost',
      onEnd: ({ newIndex, oldIndex }) => {
        if (newIndex !== oldIndex) {
          const targetRow = rightTableData.value.splice(oldIndex, 1)[0];
          rightTableData.value.splice(newIndex, 0, targetRow);
        }
        const selectionIds = rightTableData.value.map(({ id }) => id);
        const notInSelection = allSelected.value.filter(({ id }) => !selectionIds.includes(id));
        allSelected.value = [...notInSelection, ...rightTableData.value];
        getRightTableData();
      },
    });
  };

  enum SyncStatus {
    syncing = 'syncing',
    success = 'success',
    fail = 'fail',
  }

  const fetchConfigStatus = async (taskId) => {
    const {
      data: { status, error },
    } = await getSyncStatus({ taskid: taskId });
    if (status !== SyncStatus.syncing) {
      pause();
      loading.value = false;
    }
    if (status === SyncStatus.success) {
      ElMessage({ message: '素材同步成功', type: 'success' });
    }
    if (status === SyncStatus.fail) {
      ElMessage({ type: 'error', message: error || '素材同步失败' });
    }
  };

  const { pause, resume } = useIntervalFn(
    () => {
      fetchConfigStatus(taskIdRef.value);
    },
    2000,
    { immediate: false }
  );

  const handleSubmit = async () => {
    const { provider_id, style_id } = topQueryParams;
    const seletedAssetIds = allSelected.value.map(({ id }) => id);
    // if (seletedAssetIds.length == 0) {
    //   ElMessage({
    //     type: 'warning',
    //     message: '请选择需要同步的素材！',
    //   });
    //   return;
    // }
    loading.value = true;
    const params = {
      provider_id: String(provider_id),
      style_id: String(style_id),
      seletedAssetIds,
    };
    const {
      code,
      data: { taskid },
    } = await syncOperationConfig(params);
    if (code === 0) {
      taskIdRef.value = taskid;
      resume();
    } else {
      loading.value = false;
    }
  };

  onMounted(() => {
    fetchOptionList();
    fetchTreedata();
    fetchAssetList();
    setSort();
  });
</script>
<style lang="less" scoped>
  :deep(.el-tabs--border-card > .el-tabs__content) {
    padding: 0;
  }

  :deep(.el-tree-node.is-current) {
    background-color: rgba(64, 126, 255, 0.1);
    color: #407eff;
    &:focus {
      > .el-tree-node__content {
        background: none;
      }
    }
  }
  :deep(.el-tree-node:focus .material-tabs) {
    :deep(.el-tabs__header) {
      margin-bottom: 0;
      .el-tabs__nav-wrap::after {
        background-color: #f2f2f2;
        height: 1px;
      }
    }
  }
  .tree-wrap {
    border-right: solid 1px #f2f2f2;
  }

  .left-table-wrap {
    border-right: solid 1px #f2f2f2;
  }
  .box-card {
    min-height: 800px;
    .img {
      width: 60px;
      height: 60px;
    }

    .option-group .cell {
      display: flex;
    }

    .title {
      flex: 1;
      margin-left: 10px;
    }
  }
</style>
