import { LiteSDK } from './lib/LiteSDK';

const endTimeReg = /\d+(\.\d+)?(?=[^0-9]*$)/;
const startTimeReg = /\d+\.\d+|0/;
const tagMatchReg = /(\d+\.\d+|0)\s+(\d+\.\d+|0)\s+(\S*#%\S+#%\S*)\n/g;
let currentStartTime = 0;
let currentEndTime = 0;
let appendEndTime = 0;

class TagMatcher {
    constructor(tag) {
        this.tag = tag;
        this.triggers = [];
    }
    match(text, startTime, endTime) {
        const matchIndex = text.indexOf(`#%${this.tag}#%`);
        if (matchIndex === 0) {
            this.triggers.push(startTime);
        }
        if (matchIndex > 0) {
            this.triggers.push(endTime);
        }
    }
    tryTrigger(time) {
        if (this.triggers.length) {
            let index = 0;
            // 超过 starttime 或者 endtime 时触发
            while (this.triggers[index] < time) {
                index++;
            }
            if (index > 0) {
                this.handler(time);
                this.triggers = this.triggers.slice(index);
            }
        }
    }
}

export class FULite extends LiteSDK {
    // frontTimestamps = []
    backTimestamps = []
    tagMatchers = []

    constructor(options = {}) {
        super(options);
        const { ahead = 0 } = options;  // 表情领先的时间
        this.ahead = ahead;
    }

    feed(data) {
        const ttsStartTime = parseFloat(startTimeReg.exec(data)[0]);
        if (appendEndTime < ttsStartTime) {
            // this.backTimestamps.push(`${appendEndTime} ${ttsStartTime} sil\n`);
        }
        appendEndTime = parseFloat(endTimeReg.exec(data));
        this.tryMatch(data);
        console.log('this.backTimestamps', data)
        console.log('this.backTimestamps', data.replace(/#%\S*#%/g, ''))
        this.backTimestamps.push(data.replace(/#%\S*#%/g, ''));
    }

    get blankExpression() {
        if (!this._blankExpression) {
            this._blankExpression = new Float32Array(this.length);
        }
        return this._blankExpression;
    }

    tryMatch(data) {
        let regResult = tagMatchReg.exec(data);
        while (regResult) {
            const startTime = parseFloat(regResult[1]);
            const endTime = parseFloat(regResult[2]);
            const text = regResult[3]
            for (let tagMatcher of this.tagMatchers) {
                tagMatcher.match(text, startTime, endTime);
            }
            regResult = tagMatchReg.exec(data);
        }
    }

    registerTag(tag, handler) {
        let tagMacher = this.tagMatchers.find(i => i.tag === tag);
        if (!tagMacher) {
            tagMacher = new TagMatcher(tag, handler);
            this.tagMatchers.push(tagMacher);
        }
        tagMacher.handler = handler;
    }

    unregisterTag(tag) {
        let index = this.tagMatchers.findIndex(i => i.tag === tag);
        if (index !== -1) {
            this.tagMatchers.splice(index, 1);
        }
    }

    getExpressionByTime(time, isTextStamp = true) {
        const timePreceding = time + this.ahead;
        console.log('getExpressionByTime timePreceding', timePreceding)
        console.log('getExpressionByTime currentEndTime', currentEndTime)
        if (timePreceding > currentEndTime) {
            currentStartTime = currentEndTime;
            const data = this.backTimestamps.join('');
            if (data) {
                this.backTimestamps.length = 0;
                console.log('getExpressionByTime', endTimeReg.exec(data))
                currentEndTime = parseFloat(endTimeReg.exec(data)[0]);
                this.parseExpressionData(data, isTextStamp);
            } else {
                // 结束
                console.log('currentEndTime 结束')
                currentStartTime = 0;
                currentEndTime = 0;
                return this.blankExpression;
            }
        }
        for (let tagMatcher of this.tagMatchers) {
            tagMatcher.tryTrigger(time);
        }
        return this.getCurExpression(timePreceding - currentStartTime);
    }

    resetAll() {
        this.backTimestamps.length = 0;
        this.tagMatchers.length = 0;
        this.clearCache();
        currentStartTime = appendEndTime = currentEndTime = 0;
    }

    interrupt() {
        this.resetAll();
    }
}
