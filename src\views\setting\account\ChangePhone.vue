<template>
  <wrap-form submitText="确定" title="修改手机号" @submit="handleSubmit" class="update-pwd">
    <el-form ref="formRef" label-placement="left" size="large" :model="formInline" :rules="rules">
      <el-form-item prop="password">
        <el-input
          maxlength="16"
          @input="(value) => handleInput('password', value, 'no-zh-char')"
          :show-password="true"
          v-model="formInline.password"
          type="password"
          placeholder="请输入当前账户密码"
        />
      </el-form-item>
      <el-form-item prop="image_code">
        <div class="flex flex-1 items-center">
          <el-input
            @input="(value) => handleInput('image_code', value, 'number')"
            maxlength="4"
            class="flex-1"
            v-model="formInline.image_code"
            placeholder="请输入图形验证码"
          />
          <div class="w-28 ml-2" @click="handleGetCaptch">
            <img class="w-full" :src="captchaRef" alt="图片验证码" />
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="phone">
        <el-input
          maxlength="11"
          @input="(value) => handleInput('phone', value, 'number')"
          v-model="formInline.phone"
          placeholder="请输入新的手机号"
        />
      </el-form-item>
      <el-form-item class="flex-1" prop="msg_code">
        <div class="flex flex-1 flex-between">
          <el-input
            maxlength="6"
            @input="(value) => handleInput('msg_code', value, 'number')"
            class="flex-1"
            v-model="formInline.msg_code"
            placeholder="请输入手机验证码"
          />
          <el-button
            :disabled="time > 0"
            tertiary
            type="info"
            class="w-28 ml-2 bg-btn-bg-color border-none text-xx-blue"
            @click="handleGetMsgCode"
          >
            {{ time > 0 ? `${time}s后获取` : '获取验证码' }}
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </wrap-form>
</template>

<script lang="ts" setup>
  import { updatePhone } from '@/api/system/user';
  import WrapForm from '@/components/wrap-form';
  import { validatePhone, validateImgCode, validateSmsCode } from '@/utils/validator';
  import {
    useMsgCode,
    useCountTime,
    useFilterInputHander,
    useEncrypt,
  } from '@/views/hooks';
  import { ElMessage } from 'element-plus';

  import { onMounted, reactive, ref, toRaw } from 'vue';

  const { start, time } = useCountTime();
  const encrypt = useEncrypt();

  const formInline = reactive({
    password: '',
    phone: '',
    image_code: '',
    msg_code: '',
  });

  const handleInput = useFilterInputHander(formInline);
  const rules = {
    phone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    image_code: [
      { required: true, message: '请输入图形验证码', trigger: 'blur' },
      {
        validator: validateImgCode,
        trigger: 'blur',
      },
    ],
    msg_code: [
      { required: true, message: '请输入手机验证码', trigger: 'blur' },
      {
        validator: validateSmsCode,
        trigger: 'blur',
      },
    ],
  };
  const formRef = ref();
  const { captchaRef, fetchCaptch, getMsgCodeAction } = useMsgCode(formRef);
  const handleGetMsgCode = async () => {
    try {
      const { phone, image_code } = formInline;
      await getMsgCodeAction({ phone, image_code, type: 3 } as any);
      start(60);
    } catch (e) {console.log(e)}
  };

  const handleGetCaptch = () => {
    formInline.msg_code = '';
    fetchCaptch();
  };

  onMounted(() => {
    handleGetCaptch();
  });

  const handleSubmit = async () => {
    try {
      await formRef.value.validate();
      const { code, error } = await updatePhone(encrypt(toRaw(formInline)));
      if (code === 0 && !error) {
        ElMessage.success('手机号修改成功!');
        formInline.phone = '';
        formInline.image_code = '';
        formInline.msg_code = '';
        formInline.password = '';
        // router.push('/login');
      }
    } catch (e) {
      console.log(e);
      // message.error('验证不通过！')
    }
  };
</script>
