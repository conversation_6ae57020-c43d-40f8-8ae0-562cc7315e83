FROM node:18.17.1-alpine3.18 AS base
WORKDIR /app

# package
FROM base AS stage.package
COPY ./package.json /app/package.json
COPY ./pnpm-lock.yaml /app/pnpm-lock.yaml
RUN sed -i 3d /app/package.json

# build
FROM base AS stage.build
RUN npm config set registry https://registry.npmmirror.com
RUN npm install -g pnpm@8.9.2
COPY --from=stage.package /app/package.json /app/package.json
COPY --from=stage.package /app/pnpm-lock.yaml /app/pnpm-lock.yaml
RUN pnpm install
# RUN pnpm install --frozen-lockfile
COPY . /app
ENV NODE_ENV=production
RUN pnpm run build

# nginx or openresty
FROM docker.io/openresty/openresty:1.19.9.1-alpine-amd64
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories && \
    apk add tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone
WORKDIR /app
COPY ./openresty/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf
COPY --from=stage.build /app/dist /app/public
