<h1>Installation</h1>

<p>The recommended way to install the engine is to get it as an NPM package. In some cases it's not possible, so a browser-ready bundle is available for download and add directly to a html file.</p>

<h2>Install from NPM (recommended)</h2>

<p>The engine is available as an NPM package called <a href="https://www.npmjs.com/package/online-3d-viewer" target="_blank">online-3d-viewer</a>. The package itself is an ES6 module, so it can be used with several different bundlers.</p>

<p>You can install it by running the following command.</p>

<pre><code class="language-plaintext">npm install online-3d-viewer</code></pre>

<p>After installation you can import it like any other packages.</p>

<pre><code class="language-js">import * as OV from 'online-3d-viewer';</code></pre>

<h2>Download as browser-ready bundle</h2>

<p>A bundle is available for every release. You need to download the zip file attached to the <a href="https://github.com/kovacsv/Online3DViewer/releases" target="_blank">latest release</a>. After that, copy the content of the zip file on your server, and include the main file on your site.</p>

<pre><code class="language-html">&lt;script type="text/javascript" src="o3dv.min.js"&gt;&lt;/script&gt;</code></pre>
