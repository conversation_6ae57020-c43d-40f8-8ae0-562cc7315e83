<template>
  <el-card class="box-card">
    <el-row>
      <el-col :span="6">
        <el-form>
          <el-form-item label="">
            <el-input
              v-model="keyword"
              :suffix-icon="Search"
              placeholder="请输入风格名称或备注"
              @input="searchFn"
            />
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="12"></el-col>
      <el-col :span="6">
        <el-button
          type="primary"
          :icon="Plus"
          style="float: right"
          @click="addTable"
        >
          新增风格
        </el-button>
      </el-col>
    </el-row>
    <BasicTable
      :columns="styleColumns"
      :request="loadDataTable"
      :row-key="(row) => row.id"
      ref="actionRef"
      :actionColumn="actionColumn"
      :scroll-x="1090"
    />
  </el-card>
</template>

<script lang="ts" setup>
import { ref, h, watch } from 'vue'
import { ElImage, ElMessage, ElTooltip } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router';
import { getProviderStyleList, deleteProviderStyle } from '@/api/provider-style';
import { onMounted } from 'vue';
import debounce from '@/utils/debounce';
import { TableAction, BasicTable } from '@/components/Table';
import { reactive } from 'vue';
import { PERMISSION_KEYS } from '@/store/modules/user';

const keyword = ref('')
const router = useRouter();
const actionRef = ref();
const styleColumns = [
  {
    title: '风格名称',
    key: 'originalname',
    width: 260,
    render(row) {
      return h('div', [
        h(ElImage, {
          style: { 'width': '100px', height: '100px' },
          zoomRate: 1.2,
          maxScale: 6,
          minScale: 0.2,
          previewSrcList: [row.icon_url],
          fit: 'cover',
          src: row.icon_url,
          title: '点击放大',
          hideOnClickModal: true,
        }),
        h(
          ElTooltip,
          {
            trigger: 'hover',
            placement: 'top'
          },
          {
            content: () => [
              h('div', { style: 'text-align: center' }, row.title),
            ],
            default: () => [
              h('div', { style: 'text-align: center' }, row.title),
            ]
          },
        )
      ]);
    },
  },
  {
    title: '所属合作伙伴',
    key: 'address',
    render(row) {
      return row.provider.title;
    },
  },
  {
    title: '备注说明',
    key: 'description',
  },
];

const actionColumn = reactive({
  title: '操作',
  key: 'action',
  fixed: 'right',
  width: 160,
  render(record) {
    return h(TableAction as any, {
      style: 'button',
      actions: [
        {
          label: '删除',
          type: 'error',
          popConfirm: {
            title: '确定要删除吗？',
            confirm: handleDelete.bind(null, record)
          },
          auth: [
            PERMISSION_KEYS.operator.provider_style_remove
          ]
        },
        {
          label: '编辑',
          onClick: handleEdit.bind(null, record),
          auth: [
            PERMISSION_KEYS.operator.provider_style_edit
          ]
        }
      ],
    });
  },
});
function addTable() {
  router.push({ path: '/style/add' });
}
const handleEdit = (row: any) => {
  router.push({ path: `/style/edit`, query: {id: row.id} });
}
const handleDelete = async (row: any) => {
  await deleteProviderStyle(row.id)
  ElMessage({
    message: '删除成功',
    type: 'success',
  })
  reloadTable()
}

const tableData= ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchFn = debounce(() => {
  searchStyleList()
}, 300)
const searchStyleList = async () => {
  const res = await getProviderStyleList({
    page: currentPage.value,
    limit: pageSize.value,
    keyword: keyword.value,
  })
  tableData.value = res.data.rows
  total.value = res.data.total
  currentPage.value = res.data.page
}

const loadDataTable = async (res) => {
    const params = {
      ...res,
      keyword: keyword.value,
    }
    delete params.bundleType
    const data = await getProviderStyleList(params)
    if (data.code === 0) {
      return data.data
    }
    return ;
  };

  function reloadTable() {
    actionRef.value.updatePage(1)
  }
  watch(keyword, () => {
      reloadTable();
  })
  onMounted(() => {
    searchStyleList()
  })

</script>
<style>
  .n-data-table .n-data-table-th {
    font-weight: 400;
  }
  .n-popover {
    max-width: 400px;
  }
</style>
