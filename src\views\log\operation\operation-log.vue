<template>
  <el-card class="box-card">
    <div class="flex">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline flex-1">
        <el-form-item label="操作人:">
          <el-input v-model.trim="queryParams.name" placeholder="请输入操作人" clearable />
        </el-form-item>
        <el-form-item label="操作信息:">
          <el-input v-model.trim="queryParams.type" placeholder="请输入操作信息" clearable />
        </el-form-item>
        <el-form-item label="操作时间:">
          <InnerMonthPicker
            v-model="queryParams.dateRange"
          />
        </el-form-item>
      </el-form>
      <div>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </div>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <template v-for="(item, index) in tableColumns">
        <el-table-column
          v-if="item.show"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :formatter="item.formatter"
          show-overflow-tooltip
        />
      </template>
      <el-table-column prop="target" min-width="120" label="被操作对象信息">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-tooltip trigger="hover" placement="top">
              <template #content>
                <span v-text="row.target" class="cursor-pointer"></span>
              </template>
              <template #default>
                <span v-text="row.target" class="cursor-pointer block truncate"></span>
              </template>
            </el-tooltip>
            <el-icon class="ml-1" @click="handleCopy(row)">
              <DocumentCopy class="cursor-copy" />
            </el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="success" label="操作结果" width="100" :formatter="formatStatus" />
    </el-table>
    <el-pagination
      class="flex my-4 justify-end"
      background
      layout="total, prev, pager, next, sizes, jumper"
      v-model:page-size="queryParams.limit"
      v-model:current-page="queryParams.page"
      :total="totalRef"
    />
  </el-card>
</template>

<script lang="ts" setup>
  import { getOperationLog } from '@/api/system/log';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { DocumentCopy } from '@element-plus/icons-vue';
  import { useClipboard, useDebounceFn, useThrottleFn } from '@vueuse/core';
  import { ElMessage } from 'element-plus';
  import { onMounted, reactive, ref, watch } from 'vue';
  import { useExport } from '@/views/hooks';
  import { formatStatus } from '../format-column';
  import { format } from 'date-fns';
  import { InnerMonthPicker } from '@/components/InnerMonthPicker';

  const { exportFile } = useExport();

  const queryParams = reactive({
    name: '',
    type: '',
    limit: 10,
    page: 1,
    dateRange: [
      format(new Date(new Date().setDate(1)), 'yyyy-MM-dd'),
      format(new Date(), 'yyyy-MM-dd'),
    ] as any,
  });
  const totalRef = ref(0);
  const tableData = ref([]);

  const source = ref('');
  const { copy, isSupported } = useClipboard({ source });

  const handleCopy = useThrottleFn((row) => {
    source.value = row.target;
    if (!isSupported) {
      ElMessage.warning('您的浏览器不支持复制!');
      return;
    }
    copy(source.value);
    ElMessage.success('复制成功!');
    source.value = '';
  }, 3000);

  const generateParamas = () => {
    const { dateRange, ...other } = queryParams;
    const params: any = { ...other };
    if (dateRange && Array.isArray(dateRange)) {
      const [start, end] = dateRange;
      if (start) {
        params.create_start = start;
      }
      if (end) {
        params.create_end = end;
      }
    }

    return params;
  };

  const fetchLogList = useDebounceFn(async () => {
    const params = generateParamas();
    const {
      data: { rows: list, count },
    } = await getOperationLog(params);
    totalRef.value = count;
    tableData.value = list;
  }, 500);

  onMounted(async () => {
    await fetchLogList();
  });

  const dateTimeFormatter = (_row, _column, cellValue) => {
    return formatToDateTime(cellValue);
  };

  watch(
    [() => queryParams, () => queryParams.dateRange],
    async () => {
      await fetchLogList();
    },
    { deep: true }
  );

  const tableColumns = [
    { prop: 'updated_at', label: '操作时间', show: true, formatter: dateTimeFormatter },
    { prop: 'user.name', label: '操作人', show: true },
    { prop: 'module_name', label: '操作模块', show: true },
    { prop: 'type', label: '操作信息', show: true },
  ];

  //导出
  const handleExport = async () => {
    const params = generateParamas();
    exportFile('/business/export-query/operation-log/export', params);
  };
</script>
