import { http } from '@/utils/http/axios';

/**
 * @description: 能力伙伴风格列表
 */
export function getProviderStyleList(params) {
  return http.request({
    url: '/business/provider-style',
    method: 'GET',
    params,
  });
}

/**
 * @description: 新增伙伴风格
 */
export function addProviderStyle(params) {
  return http.request({
    url: '/business/provider-style',
    method: 'POST',
    params,
  });
}

/**
 * @description: 风格详情
 */
export function getProviderStyle(id) {
  return http.request({
    url: `/business/provider-style/${id}`,
    method: 'GET',
  });
}

/**
 * @description: 编辑风格
 */
export function editProviderStyle(id, params) {
  return http.request({
    url: `/business/provider-style/${id}`,
    method: 'POST',
    params,
  });
}

/**
 * @description: 删除风格
 */
export function deleteProviderStyle(id) {
  return http.request({
    url: `/business/provider-style/${id}/delete`,
    method: 'POST',
  });
}
