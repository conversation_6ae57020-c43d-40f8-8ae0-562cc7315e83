<template>
  <div style="padding: 16px">
    <p class="title">
      选择动作
    </p>
    <div class="animation-wrapper">
      <template v-for="animation in animationList.filter(i => i.gender === gender)">
        <div :class="'animation-item ' + (animation.file_path === currentPath && 'active')">
          <img class="animation-icon" :src="animation.url_icon || 'src/views/materialManage/preview/components/empty_icon.png'" alt="" @click="() => changeAnimation(animation)" />
          <p class="animation-title">{{ animation.name }}</p>
        </div>
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getSyncAssetList } from '@/api/material/material';
import { usePermission } from '@/hooks/web/usePermission';
import { PERMISSION_KEYS } from '@/store/modules/user';
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
const { hasPermission } = usePermission();
const props = defineProps(['resetAvatar', 'gender', 'modelValue', 'avatar']);
const emit = defineEmits(['update:modelValue']);

const currentPath = computed({
  get() {
    if (props.gender === 'male') {
      return props.modelValue[0];
    } else {
      return props.modelValue[1];
    }
  },
  set(value) {
    console.log('currentPath', value)
    const arr = [...props.modelValue];
    if (props.gender === 'male') {
      arr[0] = value;
    } else {
      arr[1] = value;
    }
    emit('update:modelValue', arr);
  },
});

const gender = computed({
  get() {
    return props.gender
  },
  set(value) {
    console.log('currentPath', value)
    // emit('update:modelValue', value);
  },
})

const animationList = ref<any[]>([])
const route = useRoute();

onMounted(async () => {
  if (hasPermission([PERMISSION_KEYS.cpuser.asset_read]) && route.query.styleId) {
    const res = await getSyncAssetList({
      style_id: route.query.styleId,
      type: 'animation',
      gender: '',
      page: 1,
      limit: 999,
    })
    animationList.value = res.data.rows
  }
})

const changeAnimation = async (animation) => {
  if (currentPath.value === animation.file_path) {
    currentPath.value = ''
  } else {
    currentPath.value = animation.file_path
  }
}

defineExpose({
  animationList
})

</script>
<style lang="less" scoped>
.animation-wrapper {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  overflow-y: auto;
  max-height: calc(100vh - 250px);
  width: calc(100% + 30px);
  overflow-x: hidden;
  &:empty {
    &::before {
      content: '暂无数据';
      text-align: center;
      font-size: 24px;
      width: 100%;
      line-height: 40px;
      opacity: .6;
    }
  }
}

.animation-item {
  width: 138px;

  &.active {
    .animation-icon {
      border: 2px solid rgba(64, 126, 255, 1);
    }
  }

  .animation-icon {
    width: 100%;
    height: 138px;
    border: 2px solid rgba(64, 126, 255, 0);
    border-radius: 6px;
    cursor: pointer;
  }

  .animation-title {
    width: 90%;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
