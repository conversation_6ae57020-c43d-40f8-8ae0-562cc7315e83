export interface ListData {
  id: string;
  name: string;
  icon: string;
  address: string;
  beginTime: string;
  endTime: string;
  date: string;
  type: string;
  gender: string;
  status: number;
  url: string;
  url_icon: string
  file_path: string
  sync_status: number
  size?: number;
  created_at?: string;
  style: {
    title: string
  }
  provider: {
    title: string
  }
}

export const statusOptions = [
  {
    label: '启用',
    value: 1,
  },
  {
    label: '禁用',
    value: 0,
  }
]

export const assetsSatusOptions = [
  {
    label: '草稿',
    value: 1,
  },
  {
    label: '待审核',
    value: 2,
  },
  {
    label: '已发布',
    value: 3,
  },
  {
    label: '驳回待修改',
    value: 4,
  },
  {
    label: '下架待修改',
    value: 5,
  },
]

export const syncStatusOptions = [{
  label: '已同步',
  value: 1,
},
{
  label: '未同步',
  value: 0,
}]

export enum AssetStatus {
  '草稿' = 1, //草稿 ；上传后的默认状态
  '待审核' = 2, //待审核
  '已发布' = 3, //已发布
  '驳回待修改' = 4, //驳回待修改
  '下架待修改' = 5, //下架待修改
}

export enum SyncStatus {
  '未同步' = 0, //待审核
  '已同步' = 1, //草稿 ；上传后的默认状态
}

export enum SyncStatusColor {
  'rgba(250, 173, 20, 1)' = 0, //待审核
  'rgba(6, 191, 127, 1)' = 1, //已发布
}

export enum AssetStatusColor {
  'rgba(196, 196, 196, 1)' = 1, //草稿 Israeli
  'rgba(250, 173, 20, 1)' = 2, //待审核
  'rgba(6, 191, 127, 1)' = 3, //已发布
  'rgba(255, 32, 32, 1)' = 4, //驳回待修改
  'rgba(255, 32, 32, .9)' = 5, //下架待修改
}
