# nginx or openresty
FROM docker.io/openresty/openresty:1.19.9.1-alpine-amd64
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories && \
    apk add tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone
WORKDIR /app
COPY ./openresty/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf
COPY ./dist /app/public
