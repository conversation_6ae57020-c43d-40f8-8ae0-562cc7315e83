<template>
  <el-form label-position="top" style="padding: 16px" :rules="rules" @submit.prevent>
    <el-form-item label="模板名称">
      <el-input :disabled="route.query.mode === 'template-edit'" v-model="name" maxlength="15" show-word-limit
        size="large" />
    </el-form-item>
    <el-form-item label="播报文本">
      <el-input v-model="text" placeholder="请输入播报的文本内容" type="textarea" rows="20" maxlength="200" show-word-limit />
    </el-form-item>
  </el-form>
</template>
<script lang="ts">
import { RESOURCE_MANAGER } from '@/components/AvatarRender';
import { ref, Ref, watch } from 'vue';
import { ModifiedScene } from '@/packages/sdk/fu_render_kit/lib/RenderKitSDK';
import { onBeforeRouteLeave } from 'vue-router';
import { getSyncAssetList } from '@/api/material/material';

export enum TaskState {
  TASK_IDLE,
  TASK_STARTING,
  TASK_RUNNING,
  TASK_CANCELLING,
}
</script>
<script lang="ts" setup>
import { FormRules } from 'element-plus';
import { onMounted, reactive, inject, defineProps, unref, toRaw, computed } from 'vue';
import { useRoute } from 'vue-router';
import { SOCKET_INS, TALK_INS } from '@/components/AvatarRender/index'
import { base64ToUint8Array } from '@/utils'
import { v4 as uuidv4 } from "uuid";

const props = defineProps(['avatar', 'tempData', 'name', 'text'])
const emit = defineEmits(['updateName', 'updateText']);
const route = useRoute()
const sceneRef = inject('scene') as Ref<ModifiedScene>;

const inTalkSessionRef = inject('inTalkSessionRef') as Ref<Boolean>
const avatarConfigRef = inject('avatarConfig') as any
const voiceTypeRef = ref('')
// 创建音乐对象
let audioObj = new Audio();
audioObj.loop = true
const name = computed({
  get() {
    return props.name
  },
  set(value) {
    emit('updateName', value);
  },
})

const text = computed({
  get() {
    return props.text
  },
  set(value) {
    emit('updateText', value);
  },
})

const rules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: '请输入模板名称',
      trigger: 'blur',
    },
  ],
  text: [
    {
      required: true,
      message: '请输入播报内容',
      trigger: 'blur',
    },
  ]
})

onMounted(async () => {
  TALK_INS.registerOnEnd(() => {
    console.log('播放结束')
    changeState(TaskState.TASK_IDLE)
    checkEnd()
  })
  changeState(TaskState.TASK_IDLE)
  SOCKET_INS.on("tts", subscribe)
})

onBeforeRouteLeave(() => {
  SOCKET_INS.off("tts", subscribe)
  if (props.avatar) {
    interrupt()
    props.avatar.clearAnimInterval()
    // props.avatar.setAnimation(avatarConfigRef.value?.gender + '_huxi', 'DefaultAnimNode', true)
    props.avatar.setAnimationGraphParamBool("MaskActive", 2);
  }
})

watch(
  avatarConfigRef,
  (val) => {
    if (val) {
      // 订阅socket tts事务
      if (val.gender === "male") {
        voiceTypeRef.value = "Aida"
      } else {
        voiceTypeRef.value = "Aiqi"
      }
    }
  },
  {
    immediate: true
  }
)

const talkStateRef = ref(TaskState.TASK_IDLE)
const uuidRef = ref('')
function changeState(state) {
  if (talkStateRef.value !== state) {
    if (
      talkStateRef.value === TaskState.TASK_RUNNING
    ) {
      // 从对话中切走, 重置表情
      handleTalkSessionEnd()
    }
    talkStateRef.value = state
  }
}
function handleTalkSessionEnd() {
  const exp = TALK_INS.getCurrentExpression(-1);
  inTalkSessionRef.value = false
  props.avatar.setAvatarMouthBlendShape(exp);
}

function subscribe(res) {
  if (talkStateRef.value === TaskState.TASK_CANCELLING) {
    return
  }
  let { audio, end, timestamp, uuid, timestampFormat } = res.data
  if (uuid === uuidRef.value) {
    const int16Arr = new Int16Array(
      base64ToUint8Array(audio)
    )
    if (end === true) {
      TALK_INS.setRequestStop()
      if (timestampFormat !== 'text') {
        // 音素类型的tts需要判断最后一条timestamp是否是silence，silence会有动画时间差
        const strArr = timestamp.split(/[(\r\n)\r\n]+/)
        let endSign = 'sil'
        let lastStr
        while (endSign === 'sil') {
          lastStr = strArr.pop()
          endSign = lastStr.split(' ').pop()
        }
        strArr.push(lastStr)
        timestamp = strArr.join('\n')
      }
    }
    console.log('feedPhenome', timestamp + '\n')
    TALK_INS.feedPhenome(timestamp + '\n', timestampFormat === 'text')
    TALK_INS.feedPCM(int16Arr)
  }
}

const isWatingTtsRef = ref(false)
let animationUuid = ''
let backgroundUuid = ''
let foregroundUuid = ''
let scene: ModifiedScene
watch(sceneRef, (v) => {
  scene = toRaw(unref(v))
}, { immediate: true })

const previewLoading = inject('previewLoading') as Ref<boolean>
let animationPlaying = false
async function emitByText() {
  previewLoading.value = true
  const {
    background,
    foreground,
    music,
    animation
  } = props.tempData
  props.avatar.setAnimationGraphParamBool("MaskActive", 1);
  const request: any[] = []
  const gender = avatarConfigRef.value?.gender
  backgroundUuid = uuidv4()
  foregroundUuid = uuidv4()
  const res = await getSyncAssetList({
    style_id: route.query.styleId,
    type: 'background,foreground,animation',
    page: 1,
    limit: 999,
  })
  const getUrl = (path) => {
    return res.data.rows.find(i => i.file_path === path)?.url
  }
  if (animation[0] && gender === 'male') {
    animationUuid = uuidv4()
    request.push(
      RESOURCE_MANAGER.prepareCustomBundle(getUrl(animation[0]), animationUuid)
    )
  }

  if (animation[1] && gender === 'female') {
    animationUuid = uuidv4()
    request.push(
      RESOURCE_MANAGER.prepareCustomBundle(getUrl(animation[1]), animationUuid)
    )
  }
  if (background) {
    request.push(
      RESOURCE_MANAGER.prepareCustomBundle(getUrl(background), backgroundUuid)
    )
  }
  if (foreground) {
    request.push(
      RESOURCE_MANAGER.prepareCustomBundle(getUrl(foreground), foregroundUuid)
    )
  }
  await Promise.all(request)
  const start = () => {
    console.log('animation', animation)
    if (animationUuid) {
      animationPlaying = true
      props.avatar.setAnimationAsync(
        animationUuid,
        true,
        gender + '_huxi',
        () => {
          console.log('12345')
          animationPlaying = false
          checkEnd()
        }
      )
      animationUuid = ''
    }
    if (background) {
      scene.addBackground(backgroundUuid)
    }
    if (foreground) {
      scene.addForeground(foregroundUuid)
    }
    if (music) {
      // 设置音乐文件路径
      audioObj.src = music;
      // 开始播放
      audioObj.play();
    }
  }
  if (text.value) {
    if (isWatingTtsRef.value) {
      return
    }
    isWatingTtsRef.value = true
    console.log('talkStateRef', talkStateRef.value)
    changeState(TaskState.TASK_STARTING)
    SOCKET_INS.emit(
      "message",
      {
        tts: {
          text: text.value,
          ratioVolume: 1,
          ratioSpeed: 1,
          format: "pcm",
          voice: voiceTypeRef.value,
          sampleRate: 16000,
        }
      },
      (response) => {
        isWatingTtsRef.value = false
        console.log(response)
        if (response.code === 0) {
          uuidRef.value = response.data.tts.uuid
          changeState(TaskState.TASK_RUNNING)
          handleTalkSessionBegin()
        } else {
          // 服务端处理失败，回到初始状态
          changeState(TaskState.TASK_IDLE)
        }
        start()
      }
    )
  } else {
    start()
    previewLoading.value = false
  }
}

function checkEnd() {
  let flag = talkStateRef.value === TaskState.TASK_IDLE && !animationPlaying
  if (flag) {
    interrupt()
  }
}

function interrupt() {
  return new Promise((resolve) => {
    scene.clearBackground()
    scene.clearForeground()
    audioObj.pause()
    audioObj.src = ''
    const gender = avatarConfigRef.value?.gender
    props.avatar.setAnimation(gender + '_huxi', 'DefaultAnimNode', true)
    if (
      talkStateRef.value !== TaskState.TASK_RUNNING
      && talkStateRef.value !== TaskState.TASK_STARTING
    ) {
      previewLoading.value = false
      return resolve(true)
    }

    if (uuidRef.value) {
      changeState(TaskState.TASK_CANCELLING)
      SOCKET_INS.emit(
        "message",
        {
          tts_cancel: {
            uuid: uuidRef.value,
          }
        },
        (res) => {
          if (res.code === 0) {
            uuidRef.value = ''
            changeState(TaskState.TASK_IDLE)
            resolve(true)
          }
        }
      )
    }
    TALK_INS.stop()
    previewLoading.value = false
    if (!uuidRef.value) {
      resolve(true)
      changeState(TaskState.TASK_IDLE)
    }
  })
}
function handleTalkSessionBegin() {
  inTalkSessionRef.value = true
}

defineExpose({
  emitByText
})

</script>
<style lang="less" scoped>
.box-card {
  min-height: 740px;
}

.content {
  margin-bottom: 110px;
}

.content-bottom {
  height: 100px;
  width: 100%;
  background: #fff;
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: 0;
}
</style>
