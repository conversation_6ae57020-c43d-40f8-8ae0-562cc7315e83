<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no">

	<title>Online 3D Viewer</title>

	<script type="text/javascript" src="../build/engine_dev/o3dv.min.js"></script>

    <style>
        div
        {
            float: left;
            border: 1px solid #eeeeee;
            margin: 0px 4px 4px 0px;
        }
    </style>
</head>

<body>
    <script type='text/javascript'>
        {
            let container = document.createElement ('div');
            container.style.width = '400px';
            container.style.height = '300px';
            document.body.appendChild (container);
            let modelUrls = [
                '../../test/testfiles/3ds/cube_four_instances.3ds',
                '../../test/testfiles/3ds/texture.png'
            ];
            OV.Init3DViewerFromUrlList (container, modelUrls);
        }
        {
            let container = document.createElement ('div');
            container.style.width = '400px';
            container.style.height = '300px';
            document.body.appendChild (container);
            let modelUrls = [
                '../../test/testfiles/obj/hundred_cubes.obj',
                '../../test/testfiles/obj/hundred_cubes.mtl'
            ];
            OV.Init3DViewerFromUrlList (container, modelUrls);
        }
        {
            let container = document.createElement ('div');
            container.style.width = '400px';
            container.style.height = '300px';
            document.body.appendChild (container);
            let modelUrls = [
                '../../test/testfiles/obj/hundred_cubes.obj?param1=value1&param2=value2',
                '../../test/testfiles/obj/hundred_cubes.mtl?param1=value1&param2=value2'
            ];
            OV.Init3DViewerFromUrlList (container, modelUrls);
        }
    </script>
</body>

</html>
