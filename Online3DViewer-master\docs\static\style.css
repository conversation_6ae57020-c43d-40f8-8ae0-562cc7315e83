@font-face
{
	font-family: Quicksand;
	src: url('Quicksand/Quicksand-Regular.ttf');
}

html, body
{
	font-size: 16px;
	line-height: 23px;
	font-family: Quicksand, Helvetica, sans-serif;
	width: 100%;
	height: 100%;
	margin: 0px;
	padding: 0px;
}

a
{
	color: #3393bd;
	text-decoration: none;
}

a:hover
{
	text-decoration: underline;
}

h1
{
	font-size: 28px;
	line-height: 32px;
	margin-top: 0px;
	margin-bottom: 20px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

h2
{
	font-size: 24px;
	margin-top: 20px;
	margin-bottom: 20px;
}

h3
{
	font-size: 20px;
	margin-top: 20px;
	margin-bottom: 20px;
}

p
{
	margin: 10px 0px;
}

ul
{
	padding-left: 20px;
	margin-left: 10px;
	margin-top: 10px;
	margin-bottom: 10px;
}

li
{
	padding-left: 0px;
	margin-left: 0px;
	margin-bottom: 5px;
}

.page h1, .page h2
{
	border-bottom: 1px solid #dddddd;
	padding-bottom: 10px;
}

.page h2, .page h3
{
	margin-top: 40px;
}

.thin_scrollbar
{
	scrollbar-color: #cccccc transparent;
	scrollbar-width: thin;
}

.thin_scrollbar::-webkit-scrollbar
{
	width: 3px;
	height: 3px;
}

.thin_scrollbar::-webkit-scrollbar-thumb
{
	background: #cccccc;
}

.navigation
{
	background: #ffffff;
	height: 100%;
	padding: 20px;
	position: fixed;
	top: 0px;
	left: 0px;
	box-sizing: border-box;
	border-right: 1px solid #dddddd;
	overflow: auto;
	z-index: 900;
}

.navigation_toggle
{
	background: #dddddd;
	width: 30px;
	height: 30px;
	padding: 10px;
	position: fixed;
	bottom: 20px;
	right: 20px;
	display: none;
	border-radius: 50px;
	cursor: pointer;
	z-index: 1000;
}

.navigation_toggle img
{
	width: 30px;
	height: 30px;
}

.navigation_section
{
	margin-bottom: 20px;
}

.navigation_title
{
	margin-bottom: 8px;
}

.navigation_item
{
	padding: 5px 10px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.navigation_item.selected
{
	font-weight: bold;
}

.main
{
	padding: 20px;
}

.function_container
{
	margin-bottom: 50px;
}

.function_signature
{
	background: #eeeeee;
	font-size: 18px;
	margin: 10px 0px;
	padding: 10px 10px;
	border-radius: 0px 5px 5px 0px;
	border-left: 4px solid #bbbbbb;
}

.function_description
{
	margin: 10px 0px;
}

.function_title
{
	font-size: 16px;
	font-weight: bold;
	margin-top: 15px;
	margin-bottom: 10px;
}

.parameter_header
{
	margin-bottom: 10px;
}

.parameter_main
{
	margin-bottom: 10px;
	margin-left: 30px;
}

.parameter_name
{
	font-family: monospace;
	margin-right: 3px;
}

.type
{
	background: #f0f0f0;
	font-family: monospace;
	padding: 2px 8px;
	border-radius: 3px;
}

.parameter_attributes
{
	color: #888888;
	margin-left: 3px;
}

.return_description
{
	margin-left: 3px;
}

.parameter_description
{
	margin-bottom: 10px;
}

code.inline
{
	background: #f0f0f0;
	font-family: monospace;
	padding: 2px 4px;
	border-radius: 3px;
}

.hljs
{
	background: #f0f0f0;
	border-radius: 0px 5px 5px 0px;
	border-left: 4px solid #c0c0c0;
}


@media (max-width: 600px)
{

.navigation
{
	border-right: 0px;
}

.main
{
	padding: 20px;
}

}
