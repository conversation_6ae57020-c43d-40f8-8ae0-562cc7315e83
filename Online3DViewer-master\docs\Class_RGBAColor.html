<!DOCTYPE html>
<html>

<head>
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<meta name="viewport" content="width=device-width, user-scalable=no">
	<link rel="icon" type="image/png" href="static/3dviewer_net_favicon.ico">

	<title>Online 3D Viewer - RGBAColor</title>

	<link rel="stylesheet" href="static/highlightjs/styles/github.min.css"/>
	<script src="static/highlightjs/highlight.min.js"></script>

    <link rel="stylesheet" type="text/css" href="static/style.css"/>
	<script type="text/javascript" src="static/script.js"></script>
</head>

<body>
<div id="navigation_toggle" class="navigation_toggle"><img id="navigation_icon" src="static/menu.svg"/></div>
<div id="navigation" class="navigation thin_scrollbar">
<div class="navigation_section">
<div class="navigation_title">Pages</div>
<div id="nav-Home" class="navigation_item"><a href="index.html" target="_self">Home</a></div>
<div id="nav-GitHub" class="navigation_item"><a href="https://github.com/kovacsv/Online3DViewer" target="_blank">GitHub</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Engine Usage</div>
<div id="nav-Installation" class="navigation_item"><a href="Page_Installation.html" target="_self">Installation</a></div>
<div id="nav-Usage" class="navigation_item"><a href="Page_Usage.html" target="_self">Usage</a></div>
<div id="nav-Migration Guide" class="navigation_item"><a href="Page_MigrationGuide.html" target="_self">Migration Guide</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Contribution</div>
<div id="nav-Contribution Guidelines" class="navigation_item"><a href="Page_ContributionGuidelines.html" target="_self">Contribution Guidelines</a></div>
<div id="nav-Environment Setup" class="navigation_item"><a href="Page_EnvironmentSetup.html" target="_self">Environment Setup</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Classes</div>
<div id="nav-Camera" class="navigation_item"><a href="Class_Camera.html" target="_self">Camera</a></div>
<div id="nav-EdgeSettings" class="navigation_item"><a href="Class_EdgeSettings.html" target="_self">EdgeSettings</a></div>
<div id="nav-EmbeddedViewer" class="navigation_item"><a href="Class_EmbeddedViewer.html" target="_self">EmbeddedViewer</a></div>
<div id="nav-EnvironmentSettings" class="navigation_item"><a href="Class_EnvironmentSettings.html" target="_self">EnvironmentSettings</a></div>
<div id="nav-InputFile" class="navigation_item"><a href="Class_InputFile.html" target="_self">InputFile</a></div>
<div id="nav-RGBAColor" class="navigation_item"><a href="Class_RGBAColor.html" target="_self">RGBAColor</a></div>
<div id="nav-RGBColor" class="navigation_item"><a href="Class_RGBColor.html" target="_self">RGBColor</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Functions</div>
<div id="nav-Init3DViewerElements" class="navigation_item"><a href="Function_Init3DViewerElements.html" target="_self">Init3DViewerElements</a></div>
<div id="nav-Init3DViewerFromFileList" class="navigation_item"><a href="Function_Init3DViewerFromFileList.html" target="_self">Init3DViewerFromFileList</a></div>
<div id="nav-Init3DViewerFromUrlList" class="navigation_item"><a href="Function_Init3DViewerFromUrlList.html" target="_self">Init3DViewerFromUrlList</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Enums</div>
<div id="nav-FileSource" class="navigation_item"><a href="Enum_FileSource.html" target="_self">FileSource</a></div>
<div id="nav-NavigationMode" class="navigation_item"><a href="Enum_NavigationMode.html" target="_self">NavigationMode</a></div>
<div id="nav-ProjectionMode" class="navigation_item"><a href="Enum_ProjectionMode.html" target="_self">ProjectionMode</a></div>
</div>

</div>
<div id="main" class="main">
<h1>RGBAColor</h1>
<div class="description">RGBA color object. Components are numbers in the range of 0..255.</div>
<h2>Constructor</h2>
<div class="function_container">
<div id="RGBAColor" class="function_signature">new RGBAColor (r, g, b, a)</div>
<div class="function_title">Parameters</div>
<div class="parameter_header">
<span class="parameter_name">r</span>
<span class="type parameter_type">number</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Red component.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">g</span>
<span class="type parameter_type">number</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Green component.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">b</span>
<span class="type parameter_type">number</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Blue component.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">a</span>
<span class="type parameter_type">number</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Alpha component.</div>
</div>
</div>
<h2>Methods</h2>
<div class="function_container">
<div id="Set" class="function_signature">Set (r, g, b, a)</div>
<div class="function_title">Description</div>
<div class="function_description">Sets the value of all components.</div>
<div class="function_title">Parameters</div>
<div class="parameter_header">
<span class="parameter_name">r</span>
<span class="type parameter_type">number</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Red component.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">g</span>
<span class="type parameter_type">number</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Green component.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">b</span>
<span class="type parameter_type">number</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Blue component.</div>
</div>
<div class="parameter_header">
<span class="parameter_name">a</span>
<span class="type parameter_type">number</span>
</div>
<div class="parameter_main">
<div class="parameter_description">Alpha component.</div>
</div>
</div>
<div class="function_container">
<div id="Clone" class="function_signature">Clone ()</div>
<div class="function_title">Description</div>
<div class="function_description">Creates a clone of the object.</div>
<div class="function_title">Returns</div>
<div class="function_returns">
<span class="type parameter_type"><a href="Class_RGBAColor.html" target="_self">RGBAColor</a></span>
</div>
</div>

</div>
<script type="text/javascript">Init ('RGBAColor')</script>
</body>

</html>
