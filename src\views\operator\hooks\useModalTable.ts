import { getDynamicProps } from '@/utils';
import { isProdMode } from '@/utils/env';
import { tryOnUnmounted } from '@vueuse/core';
import { getCurrentInstance, ref, unref, watch } from 'vue';

export default function useModal(props) {
  const modalRef = ref<any>(null);
  const currentInstance = getCurrentInstance();

  const getInstance = () => {
    const instance = unref(modalRef.value);
    if (!instance) {
      console.error('useModal instance is undefined!');
    }
    return instance;
  };

  const register = (modalInstance) => {
    isProdMode() &&
      tryOnUnmounted(() => {
        modalRef.value = null;
      });
    modalRef.value = modalInstance;
    currentInstance?.emit('register', modalInstance);

    watch(
      () => props,
      () => {
        props && modalInstance.setProps(getDynamicProps(props));
      },
      {
        immediate: true,
        deep: true,
      }
    );
  };

  const methods = {
    setProps: (props): void => {
      getInstance()?.setProps(props);
    },

    openModal: (params) => {
      getInstance()?.openModal(params);
    },
    closeModal: () => {
      getInstance()?.closeModal();
    },
    /*  setSubLoading: (status) => {
      getInstance()?.setSubLoading(status);
    }, */
  };

  return [register, methods] as const;
}
