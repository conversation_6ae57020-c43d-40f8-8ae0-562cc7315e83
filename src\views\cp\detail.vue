<template>
  <el-card class="box-card content">
    <h1 class="text-center">基础信息</h1>
    <div class="flex justify-center mt-10">
      <el-form
        class="w-6/12"
        ref="ruleFormRef"
        :model="detailForm"
        :rules="rules"
        label-width="120px"
        status-icon
      >
        <el-form-item label="公司名称：" v-if="detailForm.cp_type == 2">
          {{ detailForm.company_name }}
        </el-form-item>
        <el-form-item label="联系人：">
          {{ detailForm.name }}
        </el-form-item>
        <el-form-item label="电话号码：">
          {{ detailForm.phone }}
        </el-form-item>
        <el-form-item label="注册时间：">
          {{ formatToDateTime(detailForm?.created_at) }}
        </el-form-item>
        <el-form-item label="身份证：">
          <div class="flex h-24">
            <el-image
              style="height: 100px"
              :src="detailForm.id_card_img"
              :preview-src-list="[detailForm.id_card_img]"
            />
            <el-image
              class="ml-4"
              style="height: 100px"
              :src="detailForm.id_card_imgback"
              :preview-src-list="[detailForm.id_card_imgback]"
            />
          </div>
        </el-form-item>
        <el-form-item label="营业执照：" v-if="detailForm.cp_type == 2">
          <div class="h-24 flex">
            <el-image
              style="height: 100px"
              :src="detailForm.company_img"
              :preview-src-list="[detailForm.company_img]"
            />
          </div>
        </el-form-item>
        <el-form-item label="审批意见：" prop="comment">
          <el-input
            v-model="detailForm.comment"
            type="textarea"
            placeholder="请填写审批意见"
            rows="6"
            style="width: 100%"
            maxlength="100"
            @input="(value) => handleInput('comment', value)"
          />
        </el-form-item>
      </el-form>
    </div>
  </el-card>
  <div class="content-bottom">
    <el-button @click="goBack">返回</el-button>
    <el-popconfirm width="200" title="确定审核通过吗?" @confirm="handleSubmit(ruleFormRef, 1)">
      <template #reference>
        <el-button>通过</el-button>
      </template>
    </el-popconfirm>
    <el-button type="danger" @click="handleSubmit(ruleFormRef, 0)">驳回</el-button>
  </div>
</template>
<script lang="ts" setup>
  import { auditCp, getCpDetail } from '@/api/system/user';
  import { FormInstance, FormRules } from 'element-plus';
  import { onMounted, reactive, ref, toRaw } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { ElMessage } from 'element-plus';
import { useFilterInputHander } from '../hooks';
  const route = useRoute();
  const router = useRouter();

  const ruleFormRef = ref<FormInstance>();
  const detailForm = reactive({
    username: '',
    phone: '',
    name: '',
    cp_type: 0,
    id_card_img: '',
    id_card_imgback: '',
    company_img: '',
    comment: '',
  });
  const handleInput = useFilterInputHander(detailForm);

  const rules = reactive<FormRules>({
    comment: [
      {
        required: true,
        message: '请输入审批意见',
        trigger: ['blur'],
      },
    ],
  });

  const fetchCpDetail = async (id) => {
    const { code, data: detail } = await getCpDetail(id);
    if (code === 0) {
      for (let k in detail) {
        detailForm[k] = detail[k];
      }
      detailForm.company_img = detail.path_company_img
      detailForm.id_card_img = detail.path_card_img
      detailForm.id_card_imgback = detail.path_card_imgback
    }
  };

  onMounted(async () => {
    await fetchCpDetail(route.query.id);
  });

  const goBack = () => {
    router.push({
      path: '/audit/cp/list',
    });
  };

  const auditCpAction = async (id, status) => {
    const { comment } = detailForm;
    const params = {
      audit_reason: comment,
      audit_result: status,
    };
    const { code } = await auditCp(id, params);
    if (code === 0) {
      router.replace('/audit/cp/list');
    }
  };

  const handleSubmit = async (formEl: FormInstance | undefined, status) => {
    if (!formEl) return;
    try {
      const valid = await formEl.validate();
      if (valid) {
        const id = route.query.id;
        await auditCpAction(id, status);
        if (status === 1) {
          ElMessage({
            message: '审核通过',
            type: 'success',
          });
        }
      }
    } catch (e) {console.log(e)}
  };
</script>
<style lang="less" scoped>
  .box-card {
    min-height: 740px;
  }

  .content {
    margin-bottom: 110px;
  }

  .content-bottom {
    height: 100px;
    width: 100%;
    background: #fff;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 0;
  }
</style>
