import Axios from 'axios'
import { http } from '@/utils/http/axios';
export const axios = function (config) {
    let configCom = {
        method: config.method || "get",
        headers: {},
        ...config,
    }
    if (!("authorization" in config) || config.authorization) {
        // 不带authorization或设为true则加入jwt
        const token = window.localStorage.getItem("token")
        configCom.headers["Authorization"] = `${token || ""}`
    }
    return new Promise<any>((resolve, reject) => {
        Axios(configCom)
            .then((res) => {
                resolve(res)
            })
            .catch((err) => {
                reject(err)
            })
    })
}

export async function getAuthData(params) {  // params = {data: xxx}
    let res = await http.request({
        url: `/websdk/auth`,
        method: 'post',
        params
    });
    if (res.code === 0 && res.data) {
        return res.data.cdata;
    } else if (res.status == 200 && res.code === 120005) {
        return;
    } else if (res.status == 200) {
        throw res.message;
    }
}

// 获取 token, 其他请求均依赖于 token
export async function login() {
    try {
        let res = await axios({
            url: `/api/token`,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            return res.data.data
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}
// (根据形象id)获取形象配置
export async function getAvatarInfo(params) {
    try {
        let res = await axios({
            url: `/api/items/avatar/query`,
            params,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            return res.data.data
        } else {
            throw res
        }
    } catch (err) {
    }
}
// (根据资源id)获取单个资源配置  =>  对应的批量接口 /items/resource/list
export async function getResourceById(params) {
    try {
        let res = await axios({
            url: `/api/items/resource/query`,
            params,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            return res.data.data
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}

// 获取资源快照
export async function getItemsSnapshot(params) {
    try {
        let res = await axios({
            url: `/api/items/snapshot/query`,
            params,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            return res.data.data
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}
// 公私域拆分
export async function getProjectV2(params) {
    try {
        let res = await axios({
            url: `/api/items/v2/project/query`,
            params,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            return res.data.data
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}
// 获取资源快照
export async function getItemsSnapshotV2(params) {
    try {
        let res = await axios({
            url: `/api/items/v2/snapshot/query`,
            params,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            let privateList = res.data.data.private;
            let publicList = res.data.data.public;
            const requests: any[] = [];
            if (publicList) {
                requests.push(axios({ url: publicList, method: 'get' }));
            }
            if (privateList) {
                requests.push(axios({ url: privateList, method: 'get' }))
            }
            const results = await Promise.all(requests);
            let list = [];
            for (let res of results) {
                list = list.concat(res.data.list);
            }
            return { list };
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}

// 获取自定义资源快照
export async function getCentreConfigV2(params) {
    try {
        let res = await axios({
            url: `/api/items/v2/centre-config`,
            params,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            let url = res.data.data.editItemListPath;
            const result = await axios({ url, method: 'get' })
            return result.data.map;
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}

// 动作列表接口
export async function getAnimationList(params) {
    let res = await axios({
        url: `/api/demo/config/animation`,
        params,
        authorization: true,
    })
    if (res.status == 200 && res.data.code === 0) {
        return res.data.data
    } else {
        throw res
    }
}
// 获取资源 arraybuffer
export const fetchArrayBufferByUrl = async function (url) {
    const res = await Axios.get(url, { responseType: "arraybuffer" })
    return res.data
}

// 获取默认形象配置
export async function getDefaultAvatarList() {
    return http.request({
        url: '/business/asset-avatar/preset/templates',
        method: 'get',
    });
}
