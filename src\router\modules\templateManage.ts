import { Layout } from '@/router/constant';
import { renderIcon } from '@/utils/index';
import { PERMISSION_KEYS } from './audit';
import TemplateIcon from '@/components/menu-icons/template/template.vue';

/**
 * @param name 路由名称, 必须设置,且不能重名
 * @param meta 路由元信息（路由附带扩展信息）
 * @param redirect 重定向地址, 访问这个路由时,自定进行重定向
 * @param meta.disabled 禁用整个菜单
 * @param meta.title 菜单名称
 * @param meta.icon 菜单图标
 * @param meta.keepAlive 缓存该路由
 * @param meta.sort 排序越小越排前
 *
 * */
const routes: Array<any> = [
  {
    path: '/template',
    name: 'template',
    redirect: '/template/template-list',
    component: Layout,
    meta: {
      title: '',
      icon: renderIcon(TemplateIcon),
      sort: 2,
    },
    auth: [
      PERMISSION_KEYS.cpuser.template_read,
      PERMISSION_KEYS.operator.template_sync
    ],
    children: [
      {
        path: 'template-list',
        name: 'template-list',
        meta: {
          title: '模板管理',
          sort: 1,
        },
        component: () => import('@/views/materialManage/template/index.vue'),
      },
      {
        path: 'previewAvatar',
        name: 'template-preview',
        meta: {
          hidden: true,
          title: '模板预览',
          activeMenu: 'template-list',
        },
        component: () => import('@/views/materialManage/preview/index.vue'),
      },
      {
        path: 'template-add',
        name: 'template-add',
        meta: {
          hidden: true,
          title: '新增模板',
          activeMenu: 'template-list',
        },
        component: () => import('@/views/materialManage/preview/index.vue'),
      },
      {
        path: 'template-edit',
        name: 'template-edit',
        meta: {
          hidden: true,
          title: '模板编辑',
          activeMenu: 'template-list',
        },
        component: () => import('@/views/materialManage/preview/index.vue'),
      },
    ],
  },
  {
    path: '/template-audit',
    name: 'template-audit',
    redirect: '/template-audit/template-audit-list',
    component: Layout,
    meta: {
      title: '',
      icon: renderIcon(TemplateIcon),
      sort: 2,
    },
    auth: [PERMISSION_KEYS.auditor.audit_template],
    children: [
      {
        path: 'template-list',
        name: 'template-audit-list',
        meta: {
          title: '模板审核',
        },
        component: () => import('@/views/materialManage/template/index.vue'),
      },
      {
        path: 'previewAvatar',
        name: 'template-preview',
        meta: {
          hidden: true,
          title: '模板预览',
          activeMenu: 'template-list',
        },
        component: () => import('@/views/materialManage/preview/index.vue'),
      },
      {
        path: 'previewAvatar',
        name: 'template-audit-preview',
        meta: {
          hidden: true,
          title: '模板审核',
          activeMenu: 'template-list',
        },
        component: () => import('@/views/materialManage/preview/index.vue'),
      },
    ],
  },
];

export default routes;
