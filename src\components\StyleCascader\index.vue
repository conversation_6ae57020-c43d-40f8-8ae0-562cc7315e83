<template>
  <div style="width: 300px">
    <n-cascader
      clearable
      ref="cascaderRef"
      v-model:value="checkedValue"
      placeholder="请选择合作伙伴/风格"
      :options="options"
      check-strategy="child"
      :show-path="true"
      remote
      :on-load="lazyGetOption"
    />
  </div>
</template>

<script lang="ts" setup>
  import { getProviderStyleList } from '@/api/ability-provider';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import { onMounted, ref, watch } from 'vue';
  const props = defineProps(['onlyProviders', 'disabledCheckedAll']);
  const emit = defineEmits(['update:modelValue']);
  const checkedValue = ref('');
  const options = ref<any[]>([]);
  const userStore = useUserStore();
  const { providers } = storeToRefs(userStore);
  const cascaderRef = ref<any>(null)
  watch(checkedValue, (value) => {
    emit('update:modelValue', value);
  });
  onMounted(() => {
    if (providers.value.length > 0) {
      options.value = [
        ...providers.value.map((i) => ({
          value: i.id,
          label: i.title,
          depth: 1,
          isLeaf: !!props.onlyProviders,
        })),
      ];
      console.log('disabledCheckedAll', props.disabledCheckedAll)
      if (!props.disabledCheckedAll) {
        options.value.unshift({
          value: '',
          label: '全部',
          isLeaf: true,
        })
      } else {
        lazyGetOption(options.value[0]).then(res => {
          checkedValue.value = res.children[0].value
        })
      }
    } else {
      options.value = [];
    }
  });

  const lazyGetOption = (option) => {
    console.log('lazyoption', option)
    return getProviderStyleList(option.value).then((res) => {
      if (res.data.rows.length > 0) {
        option.children = res.data.rows.map((i) => {
          return {
            value: `${i.id}`,
            label: i.title,
            depth: 2,
            isLeaf: true,
          };
        });
      } else {
        option.children = [
          {
            value: undefined,
            label: '无数据',
            depth: 2,
            isLeaf: true,
            disabled: true,
          },
        ];
      }
      return option
    });
  };
</script>
