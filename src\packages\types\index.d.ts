export interface IAvatarConfig {
    avatar_id: string
    data: {
        bundle_list: any[]
        color_list: any[]
        facepup_config: {
            blendshapes: any[]
            bone_controllers: any[]
            bones: any[]
        },
        bodyShape: number
        gender: string
        name: string
        style: string
        texture_list: any[]
    }
    icon_url: string
}

export type ESpriteFitType = 0
export type CFloat3 = [number, number, number?][]
export type FUAvatarComponent = any
export enum EFuAvatarOperateComponentState {
    FU_OPERATE_AVATAR_COMPOENT_VISIBLE = 0,
    FU_OPERATE_AVATAR_COMPOENT_CAST_SHADOW = 1,
    FU_OPERATE_AVATAR_COMPOENT_RECEIVE_SHADOW = 2,
}
export enum FU_RENDER_BOOL {
    TRUE = 1,
    FALSE = 2,
}
export enum EFuRenderLoggerLevel {
    LOG_LEVEL_TRACE = 0,
    LOG_LEVEL_DEBUG = 1,
    LOG_LEVEL_INFO = 2,
    LOG_LEVEL_WARN = 3,
    LOG_LEVEL_ERROR = 4,
    LOG_LEVEL_CRITICAL = 5,
    LOG_LEVEL_OFF = 6
}
export enum ERenderFormat {
    CELL_FORMAT_RGBA16_FLOAT,
    CELL_FORMAT_RGBA8_UNORM,
    CELL_FORMAT_R11G11B10_FLOAT
}
export enum EMSAAOption {
    CELL_MSAA_NONE,
    CELL_MSAA_2X,
    CELL_MSAA_4X,
    CELL_MSAA_8X,
    CELL_MSAA_16X,
    CELL_MSAA_32X,
    CELL_MSAA_64X,
}
export enum EShadowQuality {
    CELL_SHADOW_QUALITY_HARD,
    CELL_SHADOW_QUALITY_LOW,
    CELL_SHADOW_QUALITY_MEDIUM,
    CELL_SHADOW_QUALITY_HIGH,
}
export type FUResourceManager = any
export type FUItem = any
export type CInput = any
export enum EPostProcessType {
    CELL_POST_PROCESS_FULL,
    CELL_POST_PROCESS_FXAA,
    CELL_POST_PROCESS_BLOOM,
    CELL_POST_PROCESS_FAKE_SHADOW,
    CELL_POST_PROCESS_MIRROR
}

export enum TaskState {
    TASK_IDLE,
    TASK_STARTING,
    TASK_RUNNING,
    TASK_CANCELLING,
}
