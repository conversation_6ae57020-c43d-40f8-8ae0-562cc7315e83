<template>
  <div style="">
    <div class="dressup-part-selector">
      <div
        v-for="{ name, normalIcon, selectedIcon, selectedColor } in controlData"
        v-show="!facepupShow"
        :key="name"
        class="dressup-part"
        :style="{
          'background-color': selectedDressupPart === name ? selectedColor : '#fff',
        }"
        @click="() => handlePartSelect(name)"
      >
        <img alt="图片" :src="selectedDressupPart === name ? selectedIcon : normalIcon" />
      </div>
    </div>
    <div class="dressup-pannel">
      <div v-if="selectedDressupPart" class="component-container">
        <div v-if="selectedDressupType" class="area-wrap">
          <div v-if="selectedTypeInCurrentPart" v-show="!facepupShow" class="component-area">
            <div
              v-if="selectedTypeInCurrentPart.facepup"
              class="component"
              :style="{
                'background-image': `url(${imgUrl}/slices/<EMAIL>)`,
              }"
              @click="
                () =>
                  showFacepup(
                    selectedTypeInCurrentPart.facepup[0].key,
                    selectedTypeInCurrentPart.type
                  )
              "
            ></div>
            <template v-if="selectedTypeInCurrentPart.type === 'bundle'">
              <div
                v-for="item in currentItems"
                :key="item.path"
                :class="[
                  'component',
                  item.path && item.path.includes(selectedTypeInCurrentPart.selected) ? 'active' : '',
                ]"
                :style="{
                  'background-image': `url(${item.icon_url || `${imgUrl}/slices/default_icon_2x.png`})`,
                }"
                @click="() => handleBundleSelect(item.path)"
              >
                <span v-if="isDebug">
                  {{ item.name.split('/').pop().split('.').shift() }}
                </span>
              </div>
            </template>
            <template v-else>
              <div
                v-for="item in currentItems"
                :key="item.id"
                class="component color-component"
                :style="{
                  'background-color': `rgb(${item[0]},${item[1]},${item[2]})`,
                  'background-image':
                    selectedTypeInCurrentPart.selected === (item.join && item.join('#'))
                      ? `url(${imgUrl}/slices/<EMAIL>`
                      : '',
                }"
                @click="() => handleColorSelect(item)"
              ></div>
            </template>
          </div>
        </div>
        <div class="type-wrap">
          <div class="type-container">
            <div
              v-for="{ name, normalIcon, selectedIcon } in availableTypesForGender"
              :key="name"
              class="face-component"
              :style="{
                'background-color':
                  selectedDressupType === name ? selectedPartInControlData.selectedColor : '#fff',
              }"
              @click="() => handleTypeSelect(name)"
            >
              <img alt="图片" :src="selectedDressupType === name ? selectedIcon : normalIcon" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <n-drawer
      class="facepup-container"
      placement="bottom"
      :mask-closable="true"
      :show-mask="true"
      v-model:show="facepupShow"
      to="#fu-renderkit-container"
    >
      <n-drawer-content closable>
        <template #header>
          <n-tabs
            :value="activefacePup"
            class="facepup-tab"
            :line-height="'0px'"
            @update:value="
              (v) => {
                activefacePup = v;
              }
            "
          >
            <template v-for="facepup in currentFacepup" :key="facepup.key">
              <n-tab-pane :tab="facepup.displayName" :name="facepup.key" />
            </template>
          </n-tabs>
        </template>
        <template v-for="facepup in currentFacepup" :key="facepup.key">
          <div :class="facepup.key" v-if="facepup.key === activefacePup">
            <n-radio-group
              v-if="facepupMode === 'advanced'"
              v-model="facepupType"
              class="facepup-type"
            >
              <n-radio
                v-for="(item, index) in facepup[facepupMode]"
                :key="item.displayName"
                :name="index"
              >
                {{ item.displayName }}
              </n-radio>
            </n-radio-group>
            <n-row :style="{ display: 'flex' }">
              <n-col v-if="facepup.icon" span="4" :style="{ textAlign: 'center' }">
                <div
                  size="64px"
                  :name="facepup.icon(gender)"
                  :style="{
                    position: 'relative',
                    top: '50%',
                    transform: 'translate(0, -50%)',
                  }"
                >
                  <img alt="图片" :src="facepup.icon(gender)" :style="{ height: '64px' }" />
                </div>
              </n-col>
              <n-col v-else span="4" />
              <n-col :span="facepup.icon ? 18 : 20">
                <template
                  v-for="item in facepupMode === 'fundamental'
                    ? facepup[facepupMode]
                    : facepup[facepupMode][facepupType].ops"
                  :key="item.displayName"
                >
                  <div
                    v-if="selectedTypeInCurrentPart.type !== 'conf'"
                    class="slider-container"
                    :key="item.displayName"
                  >
                    <span
                      :style="{
                        color: item.disabled ? '#bbb' : '#000',
                        fontSize: '12px',
                      }"
                      >{{ item.displayName }}</span
                    >
                    <n-slider
                      v-if="item.disabled"
                      class="slider-adjuster"
                      :disabled="item.disabled"
                      :value="0"
                      :min="-10"
                      :max="10"
                    />
                    <n-slider
                      v-else
                      class="slider-adjuster"
                      v-model:value="meshPoints[item.pos[0].slice(0, -3)]"
                      @update:value="(value) => handleValueInput(item, value)"
                      :min="-50"
                      :max="50"
                      :format-tooltip="(v) => v / 50"
                    />
                    <p :style="{ margin: '10px 0' }">{{
                      ((item.disabled ? 0 : meshPoints[item.pos[0].slice(0, -3)]) / 50).toFixed(2)
                    }}</p>
                  </div>
                </template>
              </n-col>
            </n-row>
          </div>
        </template>
        <n-radio-group
          class="facepup-type facepup-mode"
          v-model="facepupMode"
          v-if="selectedTypeInCurrentPart"
        >
          <n-radio name="fundamental" :style="{ marginRight: '5px' }">简易</n-radio>
          <n-radio name="advanced">专家</n-radio>
        </n-radio-group>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>
<script setup>
  import { ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { useGlobSetting } from '@/hooks/setting';

  const route = useRoute();
  const { imgUrl } = useGlobSetting();
</script>
<script>
  import { RESOURCE_MANAGER } from '@/components/AvatarRender';
  import {
    COLORS,
    generateMeshPoints,
    generateConfigData,
    generateBodyPitch,
    headBody5,
    headBody6,
    headBody7,
    headBody8,
    headBody9,
  } from '@/packages/configs/mesh_map';
  import { Plus } from '@element-plus/icons-vue';
  import { EAssetStatus } from '../../avatar/columns';

  export default {
    name: 'AvatarDressup',
    props: ['avatarConfig', 'currentProviderId', 'style_id'],
    data() {
      return {
        headBody5: headBody5,
        headBody6: headBody6,
        headBody7: headBody7,
        headBody8: headBody8,
        headBody9: headBody9,
        body: [
          {
            index: 5,
            icon_url: 'slices/<EMAIL>',
          },
          {
            index: 6,
            icon_url: 'slices/<EMAIL>',
          },
          {
            index: 7,
            icon_url: 'slices/<EMAIL>',
          },
          {
            index: 8,
            icon_url: 'slices/<EMAIL>',
          },
          {
            index: 9,
            icon_url: 'slices/<EMAIL>',
          },
        ],
        selectedDressupPart: '', // face, clothes, trousers, shoes, accessories
        selectedDressupType: '', // 具体细分类别
        avatarData: {},
        facepupShow: false,
        activefacePup: '',
        facepupMode: 'fundamental', // fundamental, advanced
        facepupType: 0,
        meshPoints: {},
        bodyPitchs: {},
        controlData: {},
        loadingBundle: '',
        sliding: false,
        isDebug: window.location.search.indexOf('debug') !== -1,
        currentItems: [],
        dialogVisible: false,
      };
    },
    computed: {
      gender() {
        return this.avatarData.gender || 'male';
      },
      caseGender() {
        return this.gender.slice(0, 1).toUpperCase() + this.gender.slice(1).toLowerCase();
      },
      selectedPartInControlData() {
        return (
          this.controlData.find((item) => item.name === this.selectedDressupPart) || { types: [] }
        );
      },
      availableTypesForGender() {
        return this.selectedPartInControlData.types.filter((item) => {
          return item.gender.includes(this.gender);
        });
      },
      selectedTypeInCurrentPart() {
        return this.availableTypesForGender.find((item) => item.name === this.selectedDressupType);
      },
      currentFacepup() {
        return this.selectedDressupType && this.selectedTypeInCurrentPart.facepup
          ? this.selectedTypeInCurrentPart.facepup.filter((item) => item[this.facepupMode].length)
          : [];
      },
    },
    watch: {
      avatarConfig: {
        immediate: true,
        handler: function (newVal, oldVal) {
          if (newVal) {
            this.avatarData = newVal;
            this.meshPoints = generateMeshPoints(this.avatarConfig.facepup_config);
            this.bodyPitchs = generateBodyPitch(this.avatarConfig.facepup_config);
            this.controlData = generateConfigData(
              newVal.color_list,
              Object.entries(newVal.bundle_list).map((i) => i[1])
            );
          }
        },
      },
      selectedDressupType: {
        immediate: true,
        handler: async function (newVal, oldVal) {
          if (newVal) {
            const res = RESOURCE_MANAGER.dressupComponents[newVal]
            const currentItemsDraft = (res && res.length > 0) ? res.filter(i => i.filter.gender === this.gender) : COLORS[newVal];
            this.currentItems = currentItemsDraft;
          }
        },
      },
    },
    methods: {
      showFacepup(activeKey, type) {
        this.activefacePup = activeKey;
        this.facepupType = 0;
        this.facepupShow = true;
      },
      handlePartSelect(part) {
        this.selectedDressupPart = part;
        this.selectedDressupType = this.availableTypesForGender?.[0]?.name;
      },
      handleValueInput(item, value) {
        const target = value > 0 ? item.pos : item.neg;
        for (let opField of target) {
          this.$emit('handleDressupChange', 'boneTransform', opField, Math.abs(value) / 50);
        }
        this.sliding = false;
      },
      handleDragStart() {
        this.sliding = true;
      },
      handleTypeSelect(type) {
        this.selectedDressupType = type;
      },
      handleColorSelect(color) {
        this.$emit('handleDressupChange', 'color', this.selectedTypeInCurrentPart.name, color);
        this.selectedTypeInCurrentPart.selected = color.join('#');
      },
      async handleBundleSelect(bundlePath) {
        this.selectedTypeInCurrentPart.selected = bundlePath;

        this.loadingBundle = bundlePath;
        this.$emit('handleDressupChange', 'bundle', undefined, bundlePath, (removeList) => {
          this.loadingBundle = '';
          const size = removeList.length;
          if (size > 0) {
            const list = [];
            for (let i = 0; i < size; i++) {
              list.push(removeList[i]);
            }

            for (let i = 0; i < this.controlData.length; i++) {
              const data = this.controlData[i];
              for (let j = 0; j < data.types.length; j++) {
                const type = data.types[j];
                if (type.type === 'bundle' && list.includes(type.selected)) {
                  type.selected = 'none';
                }
              }
            }
          }
        });
      },
    },
  };
</script>

<style scoped>
  .dressup-part-selector {
    position: absolute;
    left: 30px;
    top: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .dressup-part {
    height: 50px;
    border-radius: 4vh;
    margin-bottom: 2vh;
    cursor: pointer;
    box-shadow: 0 0 10px #eee;
  }
  .dressup-part img {
    max-width: 50px;
  }
  .dressup-pannel {
    position: absolute;
    bottom: 45px;
    left: 0;
    right: 0;
  }
  .type-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    padding: 16px;
  }
  .face-component {
    margin: 0px 8px;
    height: 60px;
    border-radius: 24px;
    background-color: #fff;
    cursor: pointer;
    font-size: 48px;
  }
  .face-component img {
    max-width: 60px;
  }
  .area-wrap,
  .type-wrap {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .component-area {
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    padding: 0 24px;
  }
  .component {
    box-sizing: border-box;
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background-position: 50%;
    background-size: 56px;
    background-color: #fff;
    margin: 0 8px;
    cursor: pointer;
    background-repeat: no-repeat;
  }
  @media (max-width: 375px) {
    .component {
      width: 15vw;
      height: 15vw;
    }
  }
  @media (max-height: 550px) {
    .component {
      width: 6vw;
      height: 6vw;
    }
    .type-container {
      padding: 5px 12px;
    }
    .face-component {
      font-size: 40px;
    }
  }
  .component.active {
    border: 3px solid #4a5076;
  }
  .color-component {
    box-sizing: border-box;
    border-radius: 28px;
    border: 3px solid #fff;
    background-position: 50%;
    background-size: 56px;
    margin: 0 8px;
  }
  .facepup-container {
    overflow: visible;
    box-sizing: border-box;
  }
  .facepup-tab {
    display: flex;
    flex-wrap: wrap;
  }
  .facepup-tab :deep(.n-tabs__wrap) {
    width: 100%;
    order: 2;
    border-top: 1px solid #ddd;
  }
  .facepup-tab :deep(.n-tabs__content) {
    width: 100%;
    order: 1;
    padding: 12px;
    padding-top: 0;
  }
  .facepup-tab :deep(.n-tabs__nav) {
    padding-left: 10px;
    display: -webkit-box !important;
  }
  .facepup-tab :deep(.n-tab) {
    padding: 0 20px !important;
    -webkit-flex: none;
    -webkit-box-flex: initial;
  }
  .facepup-tab :deep(.n-tabs__wrap--scrollable .n-tab) {
    -webkit-box-flex: initial !important;
  }
  .facepup-mode {
    position: fixed;
    left: 12px;
    top: 10px;
    margin-left: 0 !important;
    transform: translate(0, 0) !important;
    margin-top: 0 !important;
    padding: 3px 3px !important;
    border: 1px solid #ddd;
  }
  .facepup-type {
    justify-content: center;
    font-size: 12px;
    background: #f4f7ff;
    display: inline-block;
    margin-left: 50%;
    transform: translate(-50%, 10px);
    padding: 3px 10px;
    border-radius: 15px;
    margin-top: 5px;
  }
  .facepup-type :deep(.n-radio) {
    float: left;
    padding: 4px 10px 3px;
    border-radius: 13px;
  }
  .facepup-type :deep(.n-radio .n-radio__icon) {
    display: none;
  }
  .facepup-type :deep(.n-radio .n-radio__label) {
    margin-left: 0;
    color: #646778;
  }
  .facepup-type :deep(.n-radio[aria-checked='true']) {
    background: #fff;
  }
  .slider-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }
  .slider-adjuster {
    flex: 1;
    margin-left: 10px;
    margin-right: 10px;
    pointer-events: none;
  }
  .slider-adjuster + span {
    font-size: 12pt;
    width: 24pt;
    text-align: right;
  }
  .slider-adjuster.sliding {
    pointer-events: auto;
  }
  .slider-adjuster :deep(.n-slider-handle-wrapper) {
    pointer-events: auto;
  }
  .component {
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
