import io from "socket.io-client"

export class Socket {
    url
    token
    handlers
    events
    socket
    constructor() {
        this.handlers = new Map()
        this.events = [
            "connect",
            "connect_error",
            "error",
            "message",
            "cancel",
            "tts",
            "asr_nlp_tts",
        ]
        for (let event of this.events) {
            this.handlers.set(event, [])
        }
    }

    on(type, handler) {
        if (this.events.indexOf(type) !== -1) {
            const handlers = this.handlers.get(type)
            handlers.push(handler)
        }
    }

    emit(...args) {
        console.log('args', args)
        this.socket.emit(...args)
    }

    off(type, handler) {
        if (this.events.indexOf(type) !== -1) {
            const handlers = this.handlers.get(type)
            const findIndex = handlers.indexOf(handler)
            console.log('findIndex', findIndex)
            if (findIndex !== -1) {
                handlers.splice(findIndex, 1)
            }
        }
    }

    trigger(type, ...args) {
        if (this.events.indexOf(type) !== -1) {
            const handlers = this.handlers.get(type)
            for (let handler of handlers) {
                handler(...args)
            }
        }
    }

    renewSocket(connectCallback?) {
        const socket = io(
            this.url,
            { auth: { token: this.token } } /* { withCredentials: true } */
        )

        socket.on("connect", () => {
            this.trigger("connect")
            connectCallback && connectCallback()
            console.log("connect success", socket.id)
        })
        socket.on("connect_error", (err) => {
            this.trigger("connect_error", err)
            console.log("connect_error", err)
        })
        socket.on("error", (err) => {
            this.trigger("error", err)
            console.log("socker Error:", err)
        })
        socket.on("message", (res) => {
            console.log("socker message:", res)
            this.trigger("message", res)
            if (res.code === 15401) {
                // token 失效
                this.close()
                // this.renewSocket()
            }
        })
        socket.on("asr_nlp_tts", (res) => {
            this.trigger("tts", res)
        })
        socket.on("tts_stream", (res) => {
            this.trigger("tts", res)
        })
        window.onbeforeunload = function () {
            this.close()
        }
        this.socket = socket
    }

    close() {
        if (this.socket) {
            this.socket.close()
        }
    }

    initialize(token, cb) {
        this.token = token
        this.close()
        this.renewSocket(cb)
    }
}

export default Socket
