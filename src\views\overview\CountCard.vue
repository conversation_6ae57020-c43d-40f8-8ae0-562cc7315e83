<template>
  <div class="flex flex-col justify-between h-40 item-content">
    <div class="text-current"
      >{{ title }}
      <el-tooltip :content="tip" v-if="tip">
        <el-icon
          class="cursor-pointer align-text-bottom"
          :size="16"
          color="#6b7280"
          placement="right"
          ><InfoFilled
        /></el-icon>
      </el-tooltip>
    </div>

    <div class="num flex-1 flex items-center">{{ total }}</div>
  </div>
</template>

<script>
  import { defineComponent } from 'vue';
  import { InfoFilled } from '@element-plus/icons-vue';
  import { ElTooltip, ElIcon } from 'element-plus';
  export default defineComponent({
    components: {
      InfoFilled,
      ElTooltip,
      ElIcon,
    },
    props: {
      title: String,
      tip: String,
      total: {
        type: Number,
        defaultValue: 0,
      },
    },
  });
</script>

<style lang="less" scoped>
  .item-content {
    box-sizing: border-box;
    background: #f7f8fa;
    border-radius: 4px;
    padding: 24px;
    margin: 5px 10px;
    width: calc(25% - 20px);

    .text-current {
      color: #222326;
      line-height: 22px;
      font-size: 14px;
      font-weight: 400;
    }

    .num {
      font-size: 32px;
      line-height: 40px;
      font-weight: 700;
      color: #407eff;
    }
  }
</style>
