<template>
  <div>
    <el-card class="box-card">
      <div class="flex justify-between items-center">
        <h2>资产概览</h2>
        <div class="w-80 flex justify-start">
          <span class="flex-1">今日更新数量: {{ tipsTotal.todayAssetTotal }}</span>
          <span class="flex-1">本月更新数量: {{ tipsTotal.monthAssetTotal }}</span>
        </div>
      </div>
      <div class="flex flex-row flex-nowrap">
        <CountCard
          v-for="item in totalList"
          :key="item.key"
          :tip="item.tip"
          :title="item.title"
          :total="item.total"
        />
      </div>
    </el-card>
    <el-card class="box-card mt-5">
      <div class="flex justify-between items-center">
        <h2 class="text-header-text-color text-xl"
          >资源被使用TOP 10
          <el-tooltip content="所选时间范围内，上架素材资源和背景音乐被使用次数TOP 10">
            <el-icon
              :size="16"
              color="#6b7280"
              class="cursor-pointer align-text-middle"
              placement="right"
            >
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </h2>
        <el-form
          :inline="true"
          :model="topQueryParams"
          class="justify-self-end self-center text-end"
        >
          <el-form-item class="mt-0" style="margin-bottom: 0" label="操作时间:">
            <InnerMonthPicker
              class="xx-date-picker -ml-48"
              v-model="topQueryParams.dateRange"
            />
          </el-form-item>
        </el-form>
      </div>
      <el-select v-model="chartType">
        <el-option value="bar" label="柱状图" />
        <el-option value="pie" label="圆饼图" />
      </el-select>
      <div id="chart" ref="barChart" class="flex justify-between"> </div>
    </el-card>
    <el-card class="box-card mt-5">
      <el-tabs v-model="queryParams.resource_type" class="demo-tabs" @tab-click="handleClickTab">
        <el-tab-pane label="素材" :name="0" />
        <el-tab-pane label="预置形象" :name="1" />
        <el-tab-pane label="背景音乐" :name="2" />
      </el-tabs>
      <div>
        <h2 class="text-header-text-color text-xl">资源数量变化</h2>
        <el-form :inline="true" :model="queryParams" class="flex justify-end items-center">
          <el-form-item
            :style="{ width: '240px' }"
            label="风格类型："
            v-if="[0, 1].includes(queryParams.resource_type)"
          >
            <StyleCascader :key="queryParams.resource_type" v-model="queryParams.style_id" />
          </el-form-item>
          <el-form-item label="所属CP:">
            <el-select clearable v-model="queryParams.user_id" placeholder="请选择CP">
              <el-option key="" label="全部" value="" />
              <el-option
                v-for="item in userOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                clearable
              />
            </el-select>
          </el-form-item>

          <el-form-item label="素材类型：" v-if="queryParams.resource_type == 0">
            <el-tree-select
              clearable
              v-model="queryParams.type"
              :data="typeTreeData"
              :check-strictly="false"
              :render-after-expand="false"
              :props="{ label: 'chinese', value: 'name' }"
            />
          </el-form-item>
          <el-form-item>
            <el-date-picker
              clearable
              popper-class="xx-date-picker -ml-48"
              v-model="queryParams.dateRange"
              @calendar-change="chooseDay2 = $event[0]"
              @focus="chooseDay2 = null"
              @blur="chooseDay2 = null"
              @visible-change="chooseDay2 = null"
              :disabled-date="disabled30Date2"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            />
          </el-form-item>
        </el-form>
      </div>
      <div id="chart" ref="chartRef"></div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
  import { getAssetTypeList } from '@/api/material/material';
  import {
    getAuditOverviewTop10,
    getAuditStatisticsTotal,
    getAuditOverviewTrendLine,
  } from '@/api/statistics';
  import { getAllUserNames } from '@/api/system/user';
  import { useECharts } from '@/hooks/web/useECharts';
  import dayjs from 'dayjs';
  import { format } from 'date-fns';
  import { Ref, onMounted, reactive, ref, watch } from 'vue';
  import useDisabledDate from '@/views/hooks/useDisabledDate';
  import { InfoFilled } from '@element-plus/icons-vue';
  import CountCard from './CountCard.vue';
  import { NAME_MAP } from './const';
  import { InnerMonthPicker } from '@/components/InnerMonthPicker';
import { barChartOption, pieChartOption } from './index.vue';
  
  const chartRef = ref<HTMLDivElement | null>(null);
  const barChart = ref<HTMLDivElement | null>(null);
  const userOptions = ref<{ name: string; id: number }[]>([]);
  const chooseDay2 = ref(null);
  const tipsTotal = ref({
    todayAssetTotal: 0,
    monthAssetTotal: 0,
  });
  const totalList = ref<Array<{ title: string; tip?: string; total: number; key: string }>>([]);

  const disabled30Date2 = useDisabledDate(chooseDay2);

  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const { setOptions: setBarChartOptions } = useECharts(barChart as Ref<HTMLDivElement>);

  const handleClickTab = (tab) => {
    queryParams.resource_type = tab.props.name;
    queryParams.dateRange = [];
    queryParams.provider_id = '';
    queryParams.style_id = '';
    queryParams.user_id = '';
    queryParams.type = '';
  };

  const topQueryParams = reactive({
    dateRange: [
      format(new Date().setDate(1), 'yyyy-MM-dd'),
      format(new Date(), 'yyyy-MM-dd'),
    ] as any,
  });


  const lineChartOption = ref<any>({
    xAxis: {
      type: 'category',
      data: [],
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#959596',
      },
      axisLine: {
        lineStyle: {
          color: '#E5E5E5',
        },
      },
    },
    yAxis: {
      type: 'value',
      name: '资源数量',
      minInterval: 1,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      formatter: '资源数量: {c0}<br />时间: {b0}',
    },
    series: [
      {
        data: [],
        type: 'line',
        lineStyle: {
          color: '#407EFF',
        },
        symbol: 'circle',
        itemStyle: {
          color: '#407EFF',
        },
      },
    ],
  });

  onMounted(() => {
    fetchAllUserNames();
    setOptions(lineChartOption.value);
    setBarChartOptions(barChartOption);
  });

  watch(
    () => topQueryParams,
    () => {
      fetchOverviewTop10();
    },
    { deep: true }
  );

  const fetchAllUserNames = async () => {
    const { code, data } = await getAllUserNames();
    if (code == 0) {
      userOptions.value = data;
    }
  };
  const chartType = ref('bar')
  watch(chartType, (v) => {
    if (v === 'bar') {
      setBarChartOptions(barChartOption);
    } else {
      setBarChartOptions(pieChartOption);
    }
  })
  const fetchOverviewTop10 = async () => {
    const { dateRange } = topQueryParams;
    const params: any = {};
    if (dateRange && Array.isArray(dateRange)) {
      const [start, end] = dateRange;
      params.create_start = start;
      params.create_end = end;
    }
    const { data } = await getAuditOverviewTop10(params);
    const dates: string[] = [];
    const totals: number[] = [];
    const pieData: any[] = []
    data.forEach(({ total, resource_name }) => {
      dates.push(resource_name);
      totals.push(total);
      pieData.push({
        name: resource_name,
        value: total
      })
    });
    console.log('totals', totals)
    barChartOption.xAxis.data = dates;
    barChartOption.series[0].data = totals;
    pieChartOption.series[0].data = pieData
    setBarChartOptions(chartType.value === 'bar' ? barChartOption : pieChartOption);
  };

  const generateParamas = () => {
    const { provider_id, style_id, type, resource_type, dateRange, ...other } = queryParams;
    const params: any = { ...other, resource_type };
    if ([0, 1].includes(resource_type)) {
      params.type = type;
      params.style_id = style_id;
    }
    if (dateRange && Array.isArray(dateRange)) {
      const [start, end] = dateRange;
      params.create_start = start;
      params.create_end = end;
    }
    return params;
  };

  const fetchAuditStatisticsTotal = async () => {
    const {
      data: {
        act_total,
        background_total,
        cloth_total,
        model_total,
        all_asset_total,
        today_asset_total,
        month_asset_total,
      },
    } = await getAuditStatisticsTotal();
    const list = [
      {
        title: NAME_MAP['model_total'],
        total: model_total,
        key: 'model_total',
        tip: '上线的头部和躯干素材',
      },
      {
        title: NAME_MAP['cloth_total'],
        total: cloth_total,
        key: 'cloth_total',
        tip: '上线的上装、下装、足部和配饰等素材',
      },
      {
        title: NAME_MAP['act_total'],
        total: act_total,
        key: 'act_total',
        tip: '上线的动作素材',
      },
      {
        title: NAME_MAP['background_total'],
        total: background_total,
        key: 'background_total',
        tip: '上线的背景和前景素材',
      },
      {
        title: NAME_MAP['all_asset_total'],
        total: all_asset_total,
        key: 'all_asset_total',
        tip: '所有CP用户制作的素材总和',
      },
    ];
    tipsTotal.value.monthAssetTotal = month_asset_total;
    tipsTotal.value.todayAssetTotal = today_asset_total;
    totalList.value = list;
  };

  const fetchTrendLine = async () => {
    const params = generateParamas();
    const { data } = await getAuditOverviewTrendLine(params);
    const dates: string[] = [];
    const totals: number[] = [];
    data.forEach(({ date, total }) => {
      dates.push(dayjs(date).format('MM-DD'));
      totals.push(total);
    });
    lineChartOption.value.xAxis.data = dates;
    lineChartOption.value.series[0].data = totals;
    setOptions(lineChartOption.value);
  };

  onMounted(() => {
    fetchTypeTree();
    fetchOverviewTop10();
    fetchTrendLine();
    fetchAuditStatisticsTotal();
  });

  const queryParams = reactive({
    resource_type: 0,
    dateRange: [],
    provider_id: '',
    style_id: '',
    user_id: '',
    type: '',
  });

  watch(
    () => queryParams,
    () => {
      fetchTrendLine();
    },
    { deep: true }
  );

  const typeTreeData = ref<any>([]);

  const fetchTypeTree = async () => {
    const { code, data } = await getAssetTypeList();
    if (code == 0) {
      typeTreeData.value = [
        {
          name: '',
          chinese: '全部',
          order: -1,
          level: 1,
          parent: null,
          gender: 0,
        },
        ...data,
      ];
    }
  };
</script>