# 只在开发模式中被载入
VITE_PORT = 8000

# 网站根目录
VITE_PUBLIC_PATH = 

# 是否开启mock
VITE_USE_MOCK = true

# 网站前缀
VITE_BASE_URL = /

# 是否删除console
VITE_DROP_CONSOLE = true

# 跨域代理，可以配置多个，请注意不要换行
#VITE_PROXY = [["/appApi","http://localhost:8001"],["/upload","http://localhost:8001/upload"]]
# VITE_PROXY=[["/gassets","http://jxyd-dev.faceunity.com:8086"],["/api","http://jxyd-dev.faceunity.com:8086"]]
# VITE_PROXY=[["/gassets","http://jxyd-dev.faceunity.com"],["/api","http://jxyd-dev.faceunity.com"]]
# VITE_PROXY=[["/gassets","https://jxyd-dev.faceunity.com"],["/api/websdk","https://websdkauth.faceunity.com/"],["/api/items","https://avatarxapi-test.faceunity.com"],["/api","https://jxyd-dev.faceunity.com"]]
VITE_PROXY=[["/api","http://*************:8090"],["/gassets","http://*************:8090"]]
# VITE_PROXY=[["/gassets","http://************:5100"],["/api","http://************:5100"]]

# 图片上传地址
VITE_GLOB_UPLOAD_URL= /business/file/upload

# 图片前缀地址
VITE_GLOB_IMG_URL=

# 接口前缀
VITE_GLOB_API_URL_PREFIX = /api
