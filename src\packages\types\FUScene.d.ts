import { FUImageBackground, FUImageForeground } from './FUImageBackground'
import { FUAvatar } from './FUAvatar'
import { EPostProcessType } from '@/packages/types'

type RigidBodyName = 'Head' | 'UpperBody' | 'LowerBody'

export interface FUScene {
    /**
     * @brief 增加一个背景图片
     */
    addImageBackground(): FUImageBackground
    /**
     * @brief 增加一个前景图片
     */
    addImageForeground(): FUImageForeground

    /**
     * @brief 删除已经创建的背景/前景图片
     */
    removeImageGround(ground: FUImageBackground | FUImageForeground): void

    /**
     * @brief 在场景中增加一个FUAvatar
     * @param controller_config_path_utf8 规则文件路径, 比如GAssets/config/controller_config.bundle
     * @param item_list 资源列表.
     * @param anim_graph_json 动画图.
     * @param anim_logic_json 动画逻辑.
     * @param avatar_json 描述avatar的数据
     */
    addAvatarAsync(controller_config_path_utf8: string, item_list_json: Object, anim_graph_json: Object, anim_logic_json: Object, avatar_json: Object): Promise<FUAvatar>
    addAvatar(name?: string): FUAvatar
    /**
     * @brief 准备Avatar创建相关的数据, 比如下载文件, 创建bundle.
     * 参数意义与 addAvatarAsync 一致.
     */
    prepareAvataAsync(controller_config_path_utf8: string, item_list_json: Object, avatar_json: Object): Promise<void>

    /**
     * @brief 删除已经创建的FUAvatar
     */
    removeAvatar(avtr: FUAvatar): void

    /**
     * @brief 设置当前背景为path_utf8指定的bundle文件.
     * @param path_utf8 bundle文件路径, 比如GAssets/2dsprite/background/bg_ptag.bundle.
     */
    addBackground(path_utf8: string): void

    /**
     * @brief 清除当前背景
     */
    clearBackground(): void

    /**
     * @brief 设置当前前景为path_utf8指定的bundle文件.
     * @param path_utf8 bundle文件路径, 比如gassets/2dsprite/foreground/fg_ptag_amale_yyzwlxj.bundle.
     */
    addForeground(path_utf8: string): void
    /**
     * @brief 清除当前前景
     */
    clearForeground(): void

    /**
     * @brief 设置当前光照为path_utf8指定的bundle文件.
     * @param path_utf8 bundle文件路径, 比如GAssets/light/common/light_man.bundle.
     */
    setLightAsync(path_utf8: string): Promise<void>
    addLight(path_utf8: string): void
    clearLight(): void
    /**
     * @brief 设置当前相机为path_utf8指定的bundle文件.
     * @param path_utf8 bundle文件路径, 比如GAssets/camera/scene/cam_ptag_quanshen.bundle.
     */
    setCameraAsync(path_utf8: string): Promise<void>
    setCamera(path_utf8: string): void
    /**
     * @brief 开启/关闭后处理效果
     * @param type 后处理效果
     * @param enable true/false
     */
    enableRenderPostProcess(type: EPostProcessType, enable: boolean): void
    setRenderClearColor(color: string | [number, number, number, number]): void;

    /**
     * 获取后处理效果是否开启.
     */
    isEnableRenderPostProcess(type: EPostProcessType): boolean;

    setRenderMsaaLevel(arg: any): void

    /**
     * addAvatar
     */
    addAvatar(): FUAvatar

    /**
     * 碰撞体
     * @param width 画布宽度
     * @param height 画布高度
     * @param x x轴坐标
     * @param y y轴坐标
     * @returns 
     */
    getRayCastBodyName(width: number, height: number, x: number, y: number): RigidBodyName

    addSceneItem(path_utf8: string): void
}


enum ERayCastResult {
    /** Avatar名字（系统内部名） */
    entity_name = 'entity_name',
    /** Avatar模型 */
    mesh_name = 'mesh_name',
    /** 部位名 */
    rigid_body_name = 'rigid_body_name',
    /** 骨骼名 */
    bone_name = 'bone_name',
    /** 世界坐标 */
    surface_position = 'surface_position',
    /** 法向量 */
    surface_normal = 'surface_normal',
    /** bundle名 */
    bundle = 'bundle',
}
