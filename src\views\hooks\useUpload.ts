import { useGlobSetting } from '@/hooks/setting';
import { http } from '@/utils/http/axios';

const globSetting = useGlobSetting();

export default function useUpload(scope, option = { withToken: true }) {
  const { uploadUrl: uurl = '/business/file/upload' } = globSetting;
  const uploadUrl = `${uurl}?scope=${scope}`;
  const httpRequest = async (options) => {
    const params = new FormData();
    params.append('file', options.file);
    const { code, data } = await http.request(
      {
        url: uploadUrl,
        method: 'POST',
        headers: { 'Content-Type': 'application/form-data' },
        params,
      },
      option
    );
    return data;
  };

  return httpRequest;
}
