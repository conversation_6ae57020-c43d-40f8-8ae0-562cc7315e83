import { useIntervalFn } from '@vueuse/core';
import { ref } from 'vue';

export default function useCountTime() {
  const time = ref(0);
  const { pause, resume } = useIntervalFn(
    () => {
      time.value--;
      if (time.value < 0) {
        pause();
      }
    },
    1000,
    { immediate: false }
  );
  const start = (count) => {
    time.value = count;
    resume();
  };
  return { time, start };
}
