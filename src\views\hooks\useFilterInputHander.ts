export default function useFilterInputHander(formValue) {
  const handleInput = (field, value = '', type?) => {
    let trimvalue = value.replace(/[\s]/g, '');
    if (type == 'number') {
      trimvalue = trimvalue.replace(/[^\d\s]/g, '');
    } else if (type == 'no-zh-char') {
      trimvalue = trimvalue.replace(/[\u4E00-\u9FA5\s]/g, '');
    }
    if (typeof field === 'string') {
      formValue[field] = trimvalue
    } else if (Array.isArray(field)) {
      const res = field.pop()
      const target = field.reduce((item, next) => {
        return item[next]
      }, formValue)
      target[res] = trimvalue
    }
  };
  return handleInput;
}
