{"asset": {"generator": "<PERSON>", "version": "2.0"}, "scene": 0, "scenes": [{"nodes": [0, 3]}], "nodes": [{"name": "Parent node 1", "children": [1, 2, 4], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0]}, {"name": "Child node 1", "children": [5], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 2.0, 0.0, 0.0, 1.0]}, {"name": "Child node 2", "children": [6], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 2.0, 0.0, 1.0]}, {"name": "Parent node 2", "children": [7], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 2.0, 0.0, 1.0]}, {"mesh": 0}, {"mesh": 1}, {"mesh": 2}, {"mesh": 0}], "meshes": [{"name": "Red cube", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 2}, "indices": 0, "mode": 4, "material": 0}]}, {"name": "Green cube", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 2}, "indices": 0, "mode": 4, "material": 1}]}, {"name": "Blue cube", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 2}, "indices": 0, "mode": 4, "material": 2}]}], "accessors": [{"bufferView": 0, "byteOffset": 0, "componentType": 5123, "count": 36, "max": [23], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 0, "componentType": 5126, "count": 24, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 288, "componentType": 5126, "count": 24, "max": [0.5, 0.5, 0.5], "min": [-0.5, -0.5, -0.5], "type": "VEC3"}], "materials": [{"name": "Red material", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.0, 0.0, 1.0]}}, {"name": "Green material", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.8, 0.0, 1.0]}}, {"name": "Blue material", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.0, 0.8, 1.0]}}], "bufferViews": [{"buffer": 0, "byteOffset": 576, "byteLength": 72, "target": 34963}, {"buffer": 0, "byteOffset": 0, "byteLength": 576, "byteStride": 12, "target": 34962}], "buffers": [{"byteLength": 648, "uri": "CubeHierarchy.bin"}]}