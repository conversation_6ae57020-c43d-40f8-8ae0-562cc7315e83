<!DOCTYPE html>
<html>

<head>
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<meta name="viewport" content="width=device-width, user-scalable=no">
	<link rel="icon" type="image/png" href="static/3dviewer_net_favicon.ico">

	<title>Online 3D Viewer - Migration Guide</title>

	<link rel="stylesheet" href="static/highlightjs/styles/github.min.css"/>
	<script src="static/highlightjs/highlight.min.js"></script>

    <link rel="stylesheet" type="text/css" href="static/style.css"/>
	<script type="text/javascript" src="static/script.js"></script>
</head>

<body>
<div id="navigation_toggle" class="navigation_toggle"><img id="navigation_icon" src="static/menu.svg"/></div>
<div id="navigation" class="navigation thin_scrollbar">
<div class="navigation_section">
<div class="navigation_title">Pages</div>
<div id="nav-Home" class="navigation_item"><a href="index.html" target="_self">Home</a></div>
<div id="nav-GitHub" class="navigation_item"><a href="https://github.com/kovacsv/Online3DViewer" target="_blank">GitHub</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Engine Usage</div>
<div id="nav-Installation" class="navigation_item"><a href="Page_Installation.html" target="_self">Installation</a></div>
<div id="nav-Usage" class="navigation_item"><a href="Page_Usage.html" target="_self">Usage</a></div>
<div id="nav-Migration Guide" class="navigation_item"><a href="Page_MigrationGuide.html" target="_self">Migration Guide</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Contribution</div>
<div id="nav-Contribution Guidelines" class="navigation_item"><a href="Page_ContributionGuidelines.html" target="_self">Contribution Guidelines</a></div>
<div id="nav-Environment Setup" class="navigation_item"><a href="Page_EnvironmentSetup.html" target="_self">Environment Setup</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Classes</div>
<div id="nav-Camera" class="navigation_item"><a href="Class_Camera.html" target="_self">Camera</a></div>
<div id="nav-EdgeSettings" class="navigation_item"><a href="Class_EdgeSettings.html" target="_self">EdgeSettings</a></div>
<div id="nav-EmbeddedViewer" class="navigation_item"><a href="Class_EmbeddedViewer.html" target="_self">EmbeddedViewer</a></div>
<div id="nav-EnvironmentSettings" class="navigation_item"><a href="Class_EnvironmentSettings.html" target="_self">EnvironmentSettings</a></div>
<div id="nav-InputFile" class="navigation_item"><a href="Class_InputFile.html" target="_self">InputFile</a></div>
<div id="nav-RGBAColor" class="navigation_item"><a href="Class_RGBAColor.html" target="_self">RGBAColor</a></div>
<div id="nav-RGBColor" class="navigation_item"><a href="Class_RGBColor.html" target="_self">RGBColor</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Functions</div>
<div id="nav-Init3DViewerElements" class="navigation_item"><a href="Function_Init3DViewerElements.html" target="_self">Init3DViewerElements</a></div>
<div id="nav-Init3DViewerFromFileList" class="navigation_item"><a href="Function_Init3DViewerFromFileList.html" target="_self">Init3DViewerFromFileList</a></div>
<div id="nav-Init3DViewerFromUrlList" class="navigation_item"><a href="Function_Init3DViewerFromUrlList.html" target="_self">Init3DViewerFromUrlList</a></div>
</div>
<div class="navigation_section">
<div class="navigation_title">Enums</div>
<div id="nav-FileSource" class="navigation_item"><a href="Enum_FileSource.html" target="_self">FileSource</a></div>
<div id="nav-NavigationMode" class="navigation_item"><a href="Enum_NavigationMode.html" target="_self">NavigationMode</a></div>
<div id="nav-ProjectionMode" class="navigation_item"><a href="Enum_ProjectionMode.html" target="_self">ProjectionMode</a></div>
</div>

</div>
<div id="main" class="main">
<div class="page">
<h1>Migration Guide</h1>

<p>This document lists changes in the engine to help you to migrate your code base.</p>

<h2>0.11.0</h2>

<ul>
    <li>CameraMode is renamed to <a href="Enum_ProjectionMode.html" target="_self">ProjectionMode</a>. The corresponding parameter also changed in <a href="Class_EmbeddedViewer.html" target="_self">EmbeddedViewer</a> from <code class="inline">cameraMode</code> to <code class="inline">projectionMode</code>.</li>
</ul>

<h2>0.8.21</h2>

<ul>
    <li><a href="Function_Init3DViewerElements.html" target="_self">Init3DViewerElements</a> must be called when the document is loaded and returns an array of the created <a href="Class_EmbeddedViewer.html" target="_self">EmbeddedViewer</a> objects. Earlier it registered a load event handler inside, now it's the responsibility of the caller.</li>
</ul>

<h2>0.8.20</h2>

<ul>
    <li>Init3DViewerElement has been renamed to <a href="Function_Init3DViewerFromUrlList.html" target="_self">Init3DViewerFromUrlList</a>.</li>
    <li><a href="Function_Init3DViewerFromFileList.html" target="_self">Init3DViewerFromFileList</a> is now available to initialize the viewer based on File objects.</li>
</ul>

</div>
</div>
<script type="text/javascript">Init ('Migration Guide')</script>
</body>

</html>
