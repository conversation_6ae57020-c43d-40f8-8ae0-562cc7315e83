<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no">

	<title>Online 3D Viewer</title>

	<script type="text/javascript" src="../build/engine_dev/o3dv.min.js"></script>

    <script type='text/javascript'>
        window.addEventListener ('load', () => {
            OV.Init3DViewerElements ();
        });
    </script>

    <style>
        iframe, div.online_3d_viewer
        {
            float: left;
            border: 1px solid #eeeeee;
            margin: 0px 4px 4px 0px;
        }
    </style>
</head>

<body>
    <iframe
        src="../../website/embed.html#model=../../website/assets/models/solids.obj,../../website/assets/models/solids.mtl$edgesettings=on,0,0,0,1"
        width="360" height="240"
        style="border:1px solid #eeeeee;">
    </iframe>
    <iframe
        src="../../website/embed.html#model=../../website/assets/models/solids.obj,../../website/assets/models/solids.mtl$edgesettings=on,255,0,0,1"
        width="360" height="240"
        style="border:1px solid #eeeeee;">
    </iframe>
    <iframe
        src="../../website/embed.html#model=../../website/assets/models/solids.obj,../../website/assets/models/solids.mtl$edgesettings=on,0,0,0,80"
        width="360" height="240"
        style="border:1px solid #eeeeee;">
    </iframe>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../website/assets/models/solids.obj,../../website/assets/models/solids.mtl"
        edgesettings="on,0,0,0,1">
    </div>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../website/assets/models/solids.obj,../../website/assets/models/solids.mtl"
        edgesettings="on,255,0,0,1">
    </div>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../website/assets/models/solids.obj,../../website/assets/models/solids.mtl"
        edgesettings="on,0,0,0,80">
    </div>
</body>

</html>
