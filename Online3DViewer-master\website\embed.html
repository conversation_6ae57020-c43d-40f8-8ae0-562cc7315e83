<!DOCTYPE html>
<html>

<head>
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<meta name="viewport" content="width=device-width, user-scalable=no">
	<link rel="icon" type="image/png" href="assets/images/3dviewer_net_favicon.ico">

	<title>Online 3D Viewer Embedded</title>

	<!-- meta start -->
	<!-- meta end -->

	<!-- website start -->
	<link rel="stylesheet" type="text/css" href="../build/website_dev/o3dv.website.min.css">
	<script type="text/javascript" src="../build/website_dev/o3dv.website.min.js"></script>
	<!-- website end -->

	<!-- embed analytics start -->
    <script type="text/javascript">
        OV.SetWebsiteEventHandler ((eventName, eventLabel, eventParams) => {
            console.log ({
                eventName : eventName,
                eventLabel : eventLabel,
                eventParams : eventParams
            });
        });
    </script>
	<!-- embed analytics end -->

    <script type="text/javascript">
        OV.StartEmbed ();
    </script>

	<style>
		html, body
		{
			background: transparent;
		}
	</style>
</head>

<body>
	<div class="embed_viewer" id="embed_viewer">
		<a id="website_link" href="https://3dviewer.net" target="_blank" title="Open in 3dviewer.net">
			<img class="embed_logo" src="assets/images/3dviewer_net_logo.png"/>
		</a>
	</div>
</body>

</html>
