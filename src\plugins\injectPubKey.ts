import { getPubPem } from '@/api/system/user';
import JSEncrypt from 'jsencrypt';

export function setInjectPubKey(app) {
  const encrypt = new JSEncrypt();
  app.use(async () => {
    const { data: publicKey } = await getPubPem();
    encrypt.setPublicKey(publicKey);

    app.config.globalProperties.encryptValue = (value) => {
      return encrypt.encrypt(value);
    };
    app.config.globalProperties.pubPem = publicKey;
  });
}
