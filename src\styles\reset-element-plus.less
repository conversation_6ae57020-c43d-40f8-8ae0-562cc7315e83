.el-table .el-table__cell {
  padding: 12px 0 !important;
}

.el-table th.el-table__cell {
  background-color: #F7F8FA !important;
  color: #222324;
  font-size: 14px;
  font-weight: 400;
}

.el-tabs__nav-wrap::after {
  height: 1px !important;
}

.el-dropdown .el-dropdown-link {
  &:focus-visible {
    outline: none;
  }
}

.el-popper {
  max-width: 400px;
}

.el-picker-panel.el-date-range-picker .el-picker-panel__body-wrapper {
  border: 1px solid var(--el-datepicker-border-color);
  box-shadow: var(--el-box-shadow-light);
}

.preview-image img.el-image__preview {
  max-height: 192px;
}

.el-pagination.is-background .el-pager li.is-active {
  border: none;
  font-weight: 400;
  background-color: #407EFF !important;
  color: var(--el-color-white);

}

/*  分页样式 */
.el-pagination.is-background .el-pager li {
  background-color: #fff !important;
  border: 1px solid #ccc;
  border-radius: 3px;

  &:hover {
    border: 1px solid #3c9bff !important;
  }
}

.el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next {
  background-color: #fff !important;
}
