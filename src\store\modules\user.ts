import { ResultEnum } from '@/enums/httpEnum';
import { store } from '@/store';
import { ACCESS_TOKEN, CURRENT_USER, PROVIDER_ID } from '@/store/mutation-types';
import { defineStore } from 'pinia';

import { getProviderList, getProviderStyleList } from '@/api/ability-provider';
import { getUserInfo, login, logout, resetPwd } from '@/api/system/user';
import { storage } from '@/utils/Storage';
import router from '@/router';
import { useTimeoutFn } from '@vueuse/core';

export const PERMISSION_KEYS = {
  /** cp用户 */
  cpuser: {
    /** 查看素材 */
    asset_read: 'asset_read',
    /** 创建素材 */
    asset_create: 'asset_create',
    /** 查看预置形象 */
    asset_avatar_read: 'asset_avatar_read',
    /** 创建预置形象 */
    asset_avatar_create: 'asset_avatar_create',
    /** 创建背景音乐 */
    asset_bgm_create: 'asset_bgm_create',
    /** 编辑素材 */
    asset_edit: 'asset_edit',
    /** 编辑预置形象 */
    asset_avatar_edit: 'asset_avatar_edit',
    /** 编辑背景音乐 */
    asset_bgm_edit: 'asset_bgm_edit',
    /** 发布素材 */
    asset_publish: 'asset_publish',
    /** 撤销发布素材 */
    asset_unpublish: 'asset_unpublish',
    /** 发布预置形象 */
    asset_avatar_publish: 'asset_avatar_publish',
    /** 撤销发布预置形象 */
    asset_avatar_unpublish: 'asset_avatar_unpublish',
    /** 发布背景音乐 */
    asset_bgm_publish: 'asset_bgm_publish',
    /** 撤销发布背景音乐 */
    asset_bgm_unpublish: 'asset_bgm_unpublish',
    /** 删除素材 */
    asset_remove: 'asset_remove',
    /** 删除预置形象 */
    asset_avatar_remove: 'asset_avatar_remove',
    /** 删除背景音 */
    asset_bgm_remove: 'asset_bgm_remove',
    template_create: 'template_create',
    template_unpublish: 'template_unpublish',
    template_publish: 'template_publish',
    template_read: 'template_read',
    template_remove: 'template_remove',
    template_edit: 'template_edit',
  },
  /** 审核 */
  auditor: {
    /** 审核CP用户 */
    audit_user: 'audit_user',
    /** 审核素材 */
    audit_asset: 'audit_asset',
    /** 审核预置形象 */
    audit_asset_avatar: 'audit_asset_avatar',
    /** 审核背景音乐 */
    audit_asset_bgm: 'audit_asset_bgm',
    /** 审核人员查看数据统计 */
    audit_asset_refs4audit: 'audit_asset_refs4audit',
    /** 审核人员查看资产概览 */
    audit_asset_overview4audit: 'audit_asset_overview4audit',
    audit_template: 'audit_template',
  },
  /** 运营人员 */
  operator: {
    /** 查看合作伙伴 */
    provider_read: 'provider_read',
    /** 添加合作伙伴 */
    provider_add: 'provider_add',
    /** 编辑合作伙伴 */
    provider_edit: 'provider_edit',
    /** 删除合作伙伴 */
    provider_remove: 'provider_remove',
    /** 上传基础模型 */
    provider_model_upload: 'provider_model_upload',
    /** 查看能力伙伴风格 */
    provider_style_read: 'provider_style_read',
    /** 新增能力伙伴风格 */
    provider_style_add: 'provider_style_add',
    /** 编辑能力伙伴风格 */
    provider_style_edit: 'provider_style_edit',
    /** 删除能力伙伴风格 */
    provider_style_remove: 'provider_style_remove',
    /** 同步素材 */
    asset_sync: 'asset_sync',
    template_sync: 'template_sync',
  },
  /** 超管 */
  super: {
    /** 查看账户列表 */
    user_read_list: 'user_read_list',
    /** 添加账户 */
    user_add: 'user_add',
    /** 编辑账户 */
    user_edit: 'user_edit',
    /** 禁用账户 */
    user_disable: 'user_disable',
    /** 启用账户 */
    user_enable: 'user_enable',
    /** 删除账户 */
    user_remove: 'user_remove',
    /** 查看角色 */
    role_read: 'role_read',
    /** 添加角色 */
    role_add: 'role_add',
    /** 编辑角色 */
    role_edit: 'role_edit',
    /** 删除角色 */
    role_remove: 'role_remove',
    /** 禁用角色 */
    role_disable: 'role_disable',
    /** 启用角色 */
    role_enable: 'role_enable',
    /** 查看安全日志 */
    system_log_read: 'system_log_read',
    /** 查看操作日志 */
    operate_log_read: 'operate_log_read',
  },
} as const;

export type UserInfoType = {
  name: string;
  username: string;
  id: number;
  phone: string;
  avatarUrl?: string;
  avatarPath?: string;
  status: number;
  roles: [];
  role: {
    id: string;
    name: string;
    key: string;
  };
};

export interface IUserState {
  token: string;
  isCpUser: boolean;
  username: string;
  welcome: string;
  avatar: string;
  isExpired: boolean;
  currentProviderId: number | undefined;
  providers: any[];
  styles: any[];
  permissions: any[];
  info: UserInfoType;
  defaultStyle: number | string;
}

export const useUserStore = defineStore({
  id: 'app-user',
  state: (): IUserState => ({
    token: storage.get(ACCESS_TOKEN, ''),
    username: '',
    welcome: '',
    avatar: '',
    isCpUser: false,
    currentProviderId: storage.get(PROVIDER_ID),
    providers: [],
    styles: [],
    permissions: [],
    isExpired: false, // 密码是否过期
    info: storage.get(CURRENT_USER, {}),
    defaultStyle: '',
  }),
  getters: {
    getToken(): string {
      return this.token;
    },
    getAvatar(): string {
      return this.avatar;
    },
    getNickname(): string {
      return this.username;
    },
    getPermissions(): string[] {
      return this.permissions;
    },
    getUserInfo(): UserInfoType {
      return this.info;
    },
    currentProvider(): any {
      return this.providers.find(({ id }) => id === this.currentProviderId);
    },
  },
  actions: {
    setToken(token: string) {
      this.token = token;
    },
    setAvatar(avatar: string) {
      this.avatar = avatar;
    },
    setPermissions(permissions) {
      this.permissions = permissions;
    },
    setUserInfo(info: UserInfoType) {
      this.info = info;
    },
    setProviders(providers) {
      this.providers = providers;
    },
    setIsCpUser(isCpUser) {
      this.isCpUser = isCpUser;
    },
    setStyles(styles) {
      this.styles = styles;
    },
    setDefaultStyle(styleId) {
      this.defaultStyle = styleId;
    },
    setIsExpired(value) {
      this.isExpired = value;
    },
    async selectProvider(providerId: number) {
      if (!providerId) return false;
      this.currentProviderId = providerId;
      storage.set(PROVIDER_ID, providerId);
      await this.getProviderStyles(providerId);
      return true;
    },
    // 登录
    async login(params: any) {
      const response = await login(params);
      const { data: result, code } = response;
      if (code === ResultEnum.SUCCESS) {
        // 状态判断放在什么地方合理
        const ex = 7 * 24 * 60 * 60;
        storage.set(ACCESS_TOKEN, result.token, ex);
        this.setToken(result.token);
        this.setIsExpired(result.password_expired);
      }
      return response;
    },
    // 重置密码
    async reset(params) {
      const { code } = await resetPwd(params);
      if (code === 0) return true;
      return false;
    },
    // 获取能力合作伙伴
    async getProviders() {
      const response = await getProviderList();
      const { data: list = [] } = response;
      this.setProviders(list);
      if (!this.currentProviderId) {
        await this.selectProvider(list?.[0]?.id);
      }
      await this.getProviderStyles(this.currentProviderId || list[0].id);
    },

    async getProviderStyles(id) {
      const { data } = await getProviderStyleList(id);
      this.setStyles(data.rows);
      if (data.rows.length > 0) {
        this.setDefaultStyle(data.rows[0].id);
      } else {
        this.setDefaultStyle('');
      }
    },
    // 获取用户信息
    async getInfo() {
      try {
        const { data: result } = await getUserInfo();
        const {
          name,
          id,
          avatar_url: avatarUrl,
          avatar_path: avatarPath,
          phone,
          username,
          status,
          roles = [],
          role_first: role,
          permission_keys: permissions = [],
        } = result;
        const info = { name, id, phone, role, username, status, roles, avatarUrl, avatarPath };
        const isCpUser = roles.findIndex(({ key }) => key == 'cpuser') > -1;
        this.setIsCpUser(isCpUser);

        if (permissions?.length) {
          const ex = 7 * 24 * 60 * 60;
          this.setPermissions(permissions);
          this.setUserInfo(info);
          storage.set(CURRENT_USER, info, ex);
        } else {
          throw new Error('尚未分配权限, 请联系管理人员!');
        }
        this.setAvatar(avatarUrl);
        await this.getProviders(); // 获取合作伙伴
        return result;
      } catch (e: any) {
        if (e?.statusCode == 401 && e?.message?.includes('重新登陆')) {
          useTimeoutFn(() => {
            router.replace({ name: 'Login' });
          }, 0);
        }
      }
    },

    // 登出
    async logout() {
      await logout()
      this.setPermissions([]);
      this.setUserInfo({ name: '' } as any);
      storage.remove(ACCESS_TOKEN);
      storage.remove(CURRENT_USER);
      storage.remove(PROVIDER_ID);
    },
  },
});

// Need to be used outside the setup
export function useUser() {
  return useUserStore(store);
}
