.slide-y-transition {
  .transition-default();

  &-enter-from,
  &-leave-to {
    opacity: 0;
    transform: translateY(-15px);
  }
}

.slide-y-reverse-transition {
  .transition-default();

  &-enter-from,
  &-leave-to {
    opacity: 0;
    transform: translateY(15px);
  }
}

.slide-x-transition {
  .transition-default();

  &-enter-from,
  &-leave-to {
    opacity: 0;
    transform: translateX(-15px);
  }
}

.slide-x-reverse-transition {
  .transition-default();

  &-enter-from,
  &-leave-to {
    opacity: 0;
    transform: translateX(15px);
  }
}
