<h1>Online 3D Viewer</h1>

<p>Online 3D Viewer is a solution to visualize and explore 3D models in your browser. The solution builds up from two parts.</p>

<ul>
    <li><b>Engine:</b> A library to import, visualize and export 3D models. It can be used on any website to embed 3D models easily.</li>
    <li><b>Website:</b> The source code of the <a href="https://3dviewer.net" target="_blank">https://3dviewer.net</a> site which uses the engine under the hood.</li>
</ul>

<h2>Engine Usage</h2>

<p>These documents help you understanding how to use the engine in your own solution.</p>

<ul>
    <li>{@link Installation} - How to get the package?</li>
    <li>{@link Usage} - How to use it to embed 3D models?</li>
    <li>{@link Migration Guide} - What needs to be done when you update the library?</li>
</ul>

<h2>Contribution</h2>

<p>If you would like to contribute to the codebase, these documents help you to start.</p>

<ul>
    <li>{@link Contribution Guidelines} - How to contribute to the repository?</li>
    <li>{@link Environment Setup} - How to set up your environment for development?</li>
</ul>
