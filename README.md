# AvatarX

## 简介

AvatarX采用技术栈

- `Vue3`
- `Typescript`
- `<PERSON><PERSON>`
- `Vite`
- `Element-Plus`
- `NaiveUi`
- `Tailwind`

## 准备

- [node](http://nodejs.org/) 和 [git](https://git-scm.com/) -项目开发环境
- [Vite](https://vitejs.dev/) - 熟悉 vite 特性
- [Vue3](https://v3.vuejs.org/) - 熟悉 Vue 基础语法
- [TypeScript](https://www.typescriptlang.org/) - 熟悉`TypeScript`基本语法
- [Es6+](http://es6.ruanyifeng.com/) - 熟悉 es6 基本语法
- [Vue-Router-Next](https://next.router.vuejs.org/) - 熟悉 vue-router 基本使用
- [Naive-ui-admin](https://www.naiveui.com/) - ui 基本使用

## 使用

- 获取项目代码

```bash
git clone http://*************/jxyd/avatarx.git
```

- 安装依赖

```bash
cd naive-ui-admin

yarn install

```

- 运行

```bash
yarn dev
```

- 打包

```bash
yarn build
```

## 文档

[NaiveAdmin 官网](https://www.naiveadmin.com)
[文档地址](https://jekip.github.io/docs)
