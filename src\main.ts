import './styles/tailwind.css';
import 'element-plus/dist/index.css';
import { createApp } from 'vue';
import { setupNaiveDiscreteApi, setupNaive, setupDirectives, setInjectPubKey } from '@/plugins';
import App from './App.vue';
import router, { setupRouter } from './router';
import { setupStore } from '@/store';
import pkg from '../package.json';

async function bootstrap() {
  const app = createApp(App);

  setInjectPubKey(app);
  // 挂载状态管理
  setupStore(app);

  // 注册全局常用的 naive-ui 组件
  setupNaive(app);

  // 挂载 naive-ui 脱离上下文的 Api
  setupNaiveDiscreteApi();

  // 注册全局自定义组件
  //setupCustomComponents();

  // 注册全局自定义指令，如：v-permission权限指令
  setupDirectives(app);

  // 挂载路由
  setupRouter(app);

  // 路由准备就绪后挂载 APP 实例
  // https://router.vuejs.org/api/interfaces/router.html#isready
  await router.isReady();

  // https://www.naiveui.com/en-US/os-theme/docs/style-conflict#About-Tailwind's-Preflight-Style-Override
  const meta = document.createElement('meta');
  meta.name = 'AvatarX';
  document.head.appendChild(meta);

  app.mount('#app', true);
  console.log(
    '%c current version: %s',
    'color: rgb(124 58 237); padding: 8px; font-size: 24px; background-color: rgb(74 222 128);',
    pkg.version
  );
}

void bootstrap();
