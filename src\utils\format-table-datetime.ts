import { formatToDateTime, formatToDate } from './dateUtil';

export const dateTimeFormatter = (_row, _column, cellValue) => {
  if (!cellValue) return '';
  return formatToDateTime(cellValue);
};

export const dateFormatter = (_row, _column, cellValue) => {
  if (!cellValue) return '';
  return formatToDate(cellValue);
};

export const getCount = (_row, _column, cellValue) => {
  if (!cellValue) return '';
  return cellValue.length;
};
