<template>
    <slot name="default"></slot>
    <canvas ref="canvas" id="canvas" oncontextmenu="event.preventDefault()" style="display: none"></canvas>
</template>

<script lang="ts">
import { defineComponent, provide, ref } from 'vue';
import { RENDER_SDK, RESOURCE_MANAGER, SOCKET_INS, TALK_INS } from './index'
import { ModifiedRenderer, ModifiedScene } from '@/packages/sdk/fu_render_kit/lib/RenderKitSDK';
import { onMounted } from 'vue';

// 获取拥有的avatars
function getEnableAvatars() {
    const requests: Promise<any>[] = []

    if (RESOURCE_MANAGER.defaultAvatarList.length) {
        RESOURCE_MANAGER.defaultAvatarList.forEach(avatar => {
            requests.push(Promise.resolve(avatar))
        })
    }
    return Promise.all(requests)
}

export default defineComponent({
    name: 'AvatarRender',
    components: {
    },
    setup() {
        const rendererRef = ref<ModifiedRenderer>()
        const sceneRef = ref<ModifiedScene>()
        provide('renderer', rendererRef)
        provide('scene', sceneRef)
        const defaultAvatarList = ref([])
        provide('defaultAvatarList', defaultAvatarList)
        const initSdk = async () => {
            if (rendererRef.value) return
            // 1. 初始化 RENDER_SDK 和 用来提供资源拉取和缓存管理的 RESOURCE_MANAGER; RESOURCE_MANAGER 可按实际情况自定义扩展
            // RENDER_SDK 初始化会加载 wasm 和 data，完成渲染引擎初始化
            // RESOURCE_MANAGER 初始化会完成资源信息的拉取
            await RESOURCE_MANAGER.initialize({ sdk: RENDER_SDK })
            await RENDER_SDK.initialize()
            await TALK_INS.initialize()
            // 初始化socket并连接
            SOCKET_INS.initialize(
                RESOURCE_MANAGER.token,
                () => {
                    console.log('socket connect success')
                    // setLoading(false)
                }
            )
            await Promise.all([
                RENDER_SDK.authWithAuthpack(
                    'authpack.bin',   // url of authpack.bin 
                    RESOURCE_MANAGER.api.getAuthData.bind(RESOURCE_MANAGER)  // 鉴权 api 接口实现
                ),
                getEnableAvatars()
            ])
            // 可选择关闭 log 输出
            RENDER_SDK.RenderKit?.setLogLevel(RENDER_SDK.EFuRenderLoggerLevel.LOG_LEVEL_INFO);
            // RENDER_SDK.RenderKit.setLogLevel(RENDER_SDK.EFuRenderLoggerLevel.FU_RENDER_LOG_LEVEL_OFF);
            // // 2. 创建全局 renderer, 创建场景，根据需要设置场景的光照、相机和背景
            const renderer = RENDER_SDK._modifiedRenderer
            if (!renderer) return
            await renderer.initialize({
                canvas: document.getElementById("canvas"),
            })
            await RESOURCE_MANAGER.prepareCustomBundle(`bundleAssets/camera/camera_default.bundle`, 'camera_default')
            await RESOURCE_MANAGER.prepareCustomBundle(`bundleAssets/light/light_default.bundle`, 'light_default')
            await RESOURCE_MANAGER.prepareCustomBundle(`bundleAssets/bundle/female_huxi.bundle`, 'female_huxi')
            await RESOURCE_MANAGER.prepareCustomBundle(`bundleAssets/bundle/male_huxi.bundle`, 'male_huxi')
            rendererRef.value = renderer
            const scene = renderer.customCreateScene()
            scene.enableRenderPostProcess(
                RENDER_SDK.EPostProcessType.POST_PROCESS_MIRROR,
                false
            )
            // 设置全透明清屏色
            scene.setRenderClearColor([0, 0, 0, 0])
            // 关闭 MSAA 获得更好兼容性
            scene.setRenderMsaaLevel(RENDER_SDK.EMSAAOption.MSAA_NONE)
            // 开启 FXAA 后处理
            scene.enableRenderPostProcess(
                RENDER_SDK.EPostProcessType.POST_PROCESS_FXAA,
                true
            )
            const camera: any = scene.setCamera('camera_default');
            console.log('camera', camera)
            camera.setLookAt({
                "eye": [
                    0,
                    147.6,
                    1160
                ],
                "target": [
                    0,
                    147,
                    1150
                ],
                "up": [
                    0,
                    1,
                    0
                ]
            })
            const perspective = camera.getPerspective()
            camera.setPerspective({
                ...perspective,
                focal_length: 97
            })
            scene.setLight('light_default');
            // 关闭法线重计算 提升性能
            // scene.setConfig({ enable_recomputer_normal: 0});
            sceneRef.value = scene
        }
        provide('initSdk', initSdk)
        onMounted(() => {
            if (localStorage.getItem('ACCESS-TOKEN')) {
                initSdk()
            }
        })
    },
});
</script>
