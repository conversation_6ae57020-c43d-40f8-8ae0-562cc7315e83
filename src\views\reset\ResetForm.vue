<template>
  <wrap-form
    v-if="!resetStatus"
    title="重置密码"
    @submit="handleSubmit"
    class="reset-form flex justify-center items-center m-auto"
  >
    <el-form
      ref="restFormRef"
      label-placement="left"
      size="large"
      :model="formInline"
      :rules="rules"
    >
      <el-form-item prop="image_code">
        <div class="flex flex-1 items-center">
          <el-input
            @input="(value) => handleInput('image_code', value, 'number')"
            maxlength="4"
            class="flex-1"
            v-model="formInline.image_code"
            placeholder="请输入图形验证码"
          />
          <div class="w-28 ml-2" @click="handleGetCaptch">
            <img :src="captchaRef" alt="图片验证码" />
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="phone">
        <el-input
          maxlength="11"
          @input="(value) => handleInput('phone', value, 'number')"
          v-model="formInline.phone"
          placeholder="请输入手机号"
        />
      </el-form-item>
      <el-form-item class="flex-1" prop="msg_code">
        <div class="flex flex-1 flex-between">
          <el-input
            maxlength="6"
            @input="(value) => handleInput('msg_code', value, 'number')"
            class="flex-1"
            v-model="formInline.msg_code"
            placeholder="请输入手机验证码"
          />
          <el-button
            :disabled="time > 0"
            tertiary
            type="info"
            class="w-28 ml-2 bg-btn-bg-color border-none text-xx-blue"
            @click="handleGetMsgCode"
          >
            {{ time > 0 ? `${time}s后获取` : '获取验证码' }}
          </el-button>
        </div>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          maxlength="16"
          @input="(value) => handleInput('password', value, 'no-zh-char')"
          v-model="formInline.password"
          type="password"
          :show-password="true"
          placeholder="新密码"
        />
      </el-form-item>
      <el-form-item prop="password_confirm">
        <el-input
          maxlength="16"
          @input="(value) => handleInput('password_confirm', value, 'no-zh-char')"
          v-model="formInline.password_confirm"
          type="password"
          :show-password="true"
          placeholder="确认密码"
        />
      </el-form-item>
    </el-form>
  </wrap-form>
  <el-card class="box-card flex justify-center items-center m-auto" v-if="resetStatus">
    <div class="text-lg flex justify-center"
      ><el-icon class="text-2xl mr-3 success"> <SuccessFilled /> </el-icon>密码重置成功</div
    >
    <div class="text-center tips">立即去登录页面登录吧~</div>
    <el-button type="primary" class="w-full" @click="gotoLogin">去登录</el-button>
  </el-card>
</template>

<script setup lang="ts">
  import WrapForm from '@/components/wrap-form';
  import { useUserStore } from '@/store/modules/user';
  import useCountTime from '@/views/hooks/useCountTime';
  import { useMsgCode, useFilterInputHander, useEncrypt } from '@/views/hooks';
  import { SuccessFilled } from '@element-plus/icons-vue';
  import { useMessage } from 'naive-ui';
  import { validatePhone, validateImgCode, validateSmsCode } from '@/utils/validator';
  import { onMounted, reactive, ref, toRaw } from 'vue';
  import { useRouter } from 'vue-router';

  const restFormRef = ref();
  const message = useMessage();
  const resetStatus = ref(false);
  const encrypt = useEncrypt();
  const { start, time } = useCountTime();

  const formInline = reactive({
    phone: '',
    password: '',
    password_confirm: '',
    msg_code: '',
    image_code: '',
  });

  const handleInput = useFilterInputHander(formInline);

  const validatePassSame = (_rule, value, callback) => {
    if (value == '') {
      return callback(new Error('请输入确认密码'));
    } else if (value !== formInline.password) {
      return callback(new Error('确认密码与密码不一致,请重新输入!'));
    } else {
      callback();
    }
  };

  const rules = {
    phone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
    password: [
      {
        required: true,
        message: '请输入密码',
        trigger: 'blur',
      },
      {
        min: 8,
        message: '密码长度至少为8个字符',
        trigger: 'blur',
      },
      {
        max: 16,
        message: '密码长度最长16个字符',
        trigger: 'blur',
      },
      {
        pattern: /(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9]).{6,30}/,
        message: '密码必须包含大写字母、小写字母、数字和特殊字符',
        trigger: 'blur',
      },
    ],
    password_confirm: [
      {
        required: true,
        message: '请输入确认密码',
        trigger: ['input', 'blur'],
      },
      {
        min: 8,
        message: '密码长度至少为8个字符',
        trigger: 'blur',
      },
      {
        max: 16,
        message: '密码长度最长16个字符',
        trigger: 'blur',
      },
      {
        pattern: /(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9]).{6,30}/,
        message: '密码必须包含大写字母、小写字母、数字和特殊字符',
        trigger: 'blur',
      },
      {
        validator: validatePassSame,
        trigger: 'blur',
      },
    ],
    msg_code: [
      { required: true, message: '请输入手机验证码', trigger: 'blur' },
      {
        validator: validateSmsCode,
        trigger: 'blur',
      },
    ],
    image_code: [
      { required: true, message: '请输入图形验证码', trigger: 'blur' },
      {
        validator: validateImgCode,
        trigger: 'blur',
      },
    ],
  };

  const userStore = useUserStore();

  const router = useRouter();

  const { captchaRef, fetchCaptch, getMsgCodeAction } = useMsgCode(restFormRef);

  const handleGetMsgCode = async () => {
    try {
      const { phone, image_code } = formInline;
      formInline.msg_code = '';
      await getMsgCodeAction({ phone, image_code, type: 2 });
      start(60);
    } catch (e) {console.log(e)}
  };

  const handleGetCaptch = () => {
    formInline.image_code = '';
    fetchCaptch();
  };

  onMounted(() => {
    handleGetCaptch();
  });

  const gotoLogin = () => {
    router.replace('/login');
  };

  const handleSubmit = async () => {
    try {
      await restFormRef.value.validate();
      const result = await userStore.reset(encrypt(toRaw(formInline)));
      if (result) {
        message.success('密码重置成功!');
        resetStatus.value = true;
      }
    } catch (e) {console.log(e)}
  };
</script>

<style lang="less" scoped>
  .reset-form {
    width: 500px;
  }

  .box-card {
    width: 500px;
    line-height: 80px;

    .success {
      color: #67c23a;
    }

    .tips {
      color: rgb(148 163 184);
    }
  }
</style>
