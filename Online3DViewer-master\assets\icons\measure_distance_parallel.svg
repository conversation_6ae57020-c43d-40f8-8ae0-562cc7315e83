<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xml:space="preserve"
   enable-background="new 0 0 18 18"
   viewBox="0 0 18 18"
   y="0px"
   x="0px"
   id="svg6"
   version="1.1"><metadata
   id="metadata6504"><rdf:RDF><cc:Work
       rdf:about=""><dc:format>image/svg+xml</dc:format><dc:type
         rdf:resource="http://purl.org/dc/dcmitype/StillImage" /><dc:title></dc:title></cc:Work></rdf:RDF></metadata><defs
   id="defs6502" />

<line
   id="line6483"
   y2="13.5"
   x2="4.5"
   y1="5.5"
   x1="12.5"
   stroke-miterlimit="10"
   stroke-linejoin="round"
   stroke-linecap="round"
   stroke="#263238"
   fill="none" />
<line
   id="line6485"
   y2="2"
   x2="12"
   y1="6"
   x1="16"
   stroke-miterlimit="10"
   stroke-linejoin="round"
   stroke-linecap="round"
   stroke="#263238"
   fill="none" />
<line
   id="line6487"
   y2="13"
   x2="1"
   y1="17"
   x1="5"
   stroke-miterlimit="10"
   stroke-linejoin="round"
   stroke-linecap="round"
   stroke="#263238"
   fill="none" />
<g
   id="g6493">
	
		<line
   id="line6489"
   y2="5.5"
   x2="12.5"
   y1="5.5"
   x1="9.7"
   stroke-miterlimit="10"
   stroke-linejoin="round"
   stroke-linecap="round"
   stroke="#263238"
   fill="none" />
	
		<line
   id="line6491"
   y2="5.5"
   x2="12.5"
   y1="8.3"
   x1="12.5"
   stroke-miterlimit="10"
   stroke-linejoin="round"
   stroke-linecap="round"
   stroke="#263238"
   fill="none" />
</g>
<g
   id="g6499">
	
		<line
   id="line6495"
   y2="13.5"
   x2="4.5"
   y1="10.7"
   x1="4.5"
   stroke-miterlimit="10"
   stroke-linejoin="round"
   stroke-linecap="round"
   stroke="#263238"
   fill="none" />
	
		<line
   id="line6497"
   y2="13.5"
   x2="4.5"
   y1="13.5"
   x1="7.3"
   stroke-miterlimit="10"
   stroke-linejoin="round"
   stroke-linecap="round"
   stroke="#263238"
   fill="none" />
</g>
</svg>