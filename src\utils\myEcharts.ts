import * as echarts from 'echarts'
export default function doughnutPie (id, chart, options, dataArr) {
  let colorArr = dataArr.map(item => {
    return item.color
  })
  let { total, title } = options
  let option = {
    tooltip: {
        show: true,
    },
    legend: {
        show: true,
        data: ['销量'],
        x: 'center',
        y: 'bottom',
        padding: [0, 0, 10, 0],
        textStyle: {
            fontStyle: 'normal',
            fontSize: 12,
        },
    },
    grid: {
        top: '8%',
        bottom: '10%',
        left: '4%',
        right: '8%',
        containLabel: true,
    },
    xAxis: {
        type: 'category',
        data: chartData.data.month,
    },

    yAxis: {
        type: 'value',
    },
    series: [
        {
            name: '销量',
            type: 'line',
            data: chartData.data.amount,
        },
    ],
  }
  let dom = document.getElementById(id)
  if (!chart) chart = echarts.init(dom)
  chart.setOption(option, true)
  return chart
}
