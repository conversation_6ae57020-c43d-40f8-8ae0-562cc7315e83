import { Layout } from '@/router/constant';
import { renderIcon } from '@/utils/index';
import { PERMISSION_KEYS } from './audit';
import AvatarIcon from '@/components/menu-icons/cp/cpavatar.vue';
import AvatarAIcon from '@/components/menu-icons/audit/avatar.vue';

/**
 * @param name 路由名称, 必须设置,且不能重名
 * @param meta 路由元信息（路由附带扩展信息）
 * @param redirect 重定向地址, 访问这个路由时,自定进行重定向
 * @param meta.disabled 禁用整个菜单
 * @param meta.title 菜单名称
 * @param meta.icon 菜单图标
 * @param meta.keepAlive 缓存该路由
 * @param meta.sort 排序越小越排前
 *
 * */
const routes: Array<any> = [
  {
    path: '/avatar',
    name: 'Avatar',
    redirect: '/avatar/avatar-list',
    component: Layout,
    meta: {
      title: (isCpUser) => (isCpUser ? '预置形象' : '预置形象审核'),
      icon: (isCpUser) => (isCpUser ? renderIcon(AvatarIcon) : renderIcon(AvatarAIcon)),
      sort: 2,
      //hidden: true, // 隐藏形象管理菜单
    },
    auth: [PERMISSION_KEYS.auditor.audit_user, PERMISSION_KEYS.cpuser.asset_avatar_read],
    children: [
      {
        path: 'avatar-list',
        name: 'avatar-list',
        meta: {
          title: '形象管理',
        },
        component: () => import('@/views/materialManage/avatar/index.vue'),
      },
      {
        path: 'previewAvatar',
        name: 'avatar-preview',
        meta: {
          hidden: true,
          title: '形象预览',
          activeMenu: 'avatar-list',
        },
        component: () => import('@/views/materialManage/preview/index.vue'),
      },
      {
        path: 'model3d-preview',
        name: 'model3d-preview',
        meta: {
          hidden: true,
          title: '3D模型预览',
          activeMenu: 'avatar-list',
        },
        component: () => import('@/components/Online3DViewer/Model3DPreview.vue'),
      },
      {
        path: 'addAvatar',
        name: 'avatar-add',
        meta: {
          hidden: true,
          title: '新增形象',
          activeMenu: 'avatar-list',
        },
        component: () => import('@/views/materialManage/preview/index.vue'),
      },
      {
        path: 'editAvatar',
        name: 'avatar-edit',
        meta: {
          hidden: true,
          title: '形象编辑',
          activeMenu: 'avatar-list',
        },
        component: () => import('@/views/materialManage/preview/index.vue'),
      }
    ],
  },
];

export default routes;
