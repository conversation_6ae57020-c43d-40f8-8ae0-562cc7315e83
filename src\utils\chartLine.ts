import * as echarts from 'echarts'
export default function chartLine (id, chart: any, options, dataArr) {
  // let colorArr = dataArr.map(item => {
  //   return item.color
  // })
  // let { total, title } = options
  let option = {
    tooltip: {
        show: true,
    },
    legend: {
        show: true,
        data: ['销量'],
        x: 'center',
        y: 'bottom',
        padding: [0, 0, 10, 0],
        textStyle: {
            fontStyle: 'normal',
            fontSize: 12,
        },
    },
    grid: {
        top: '8%',
        bottom: '10%',
        left: '4%',
        right: '8%',
        containLabel: true,
    },
    xAxis: {
        type: 'category',
        data: [1, 2, 3,4 ,5],
    },

    yAxis: {
        title: '资源数量',
        type: 'value',
    },
    series: [
        {
            name: '销量',
            type: 'line',
            data: [10,20, 15, 10, 5],
            symbol: 'none'
        },
    ],
  }
  let dom = document.getElementById(id)
  if (!chart) chart = echarts.init(dom)
  chart.setOption(option, true)
  return chart
}