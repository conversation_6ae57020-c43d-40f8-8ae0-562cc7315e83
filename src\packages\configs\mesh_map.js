import { getAppEnvConfig } from '@/utils/env';
import bodyPitch from './data/body_pitch';
const {
    VITE_GLOB_IMG_URL
} = getAppEnvConfig();

export const meshMap = {
    mouth_root: {
        // name: 'mouth_root_jnt',
        key: 'mouth_root',
        displayName: '下半脸',
        icon(gender) {
            return `${VITE_GLOB_IMG_URL}/slices/${gender}<EMAIL>`;
        },
        fundamental: [{
            displayName: '上下',
            pos: ['mouth_root_jnt_syPos'],
            neg: ['mouth_root_jnt_syNeg'],
        }, {
            displayName: '前后',
            pos: ['mouth_root_jnt_szPos'],
            neg: ['mouth_root_jnt_szNeg'],
        }, {
            displayName: '宽窄',
            pos: ['mouth_root_jnt_sxPos'],
            neg: ['mouth_root_jnt_sxNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                disabled: true,
            }, {
                displayName: '上下',
                pos: ['mouth_root_jnt_tyPos'],
                neg: ['mouth_root_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['mouth_root_jnt_tzPos'],
                neg: ['mouth_root_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['mouth_root_jnt_rxPos'],
                neg: ['mouth_root_jnt_rxNeg'],
            }, {
                displayName: '左右',  // 绕 y 轴
                disabled: true,
            }, {
                displayName: '倾斜',  // 绕 z 轴
                disabled: true,
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['mouth_root_jnt_sxPos'],
                neg: ['mouth_root_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['mouth_root_jnt_syPos'],
                neg: ['mouth_root_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['mouth_root_jnt_szPos'],
                neg: ['mouth_root_jnt_szNeg'],
            }],
        }],
    },
    forehead: {
        // name: 'forehead_jnt',
        key: 'forehead',
        displayName: '额头',
        icon(gender) {
            return `${VITE_GLOB_IMG_URL}/slices/${gender}<EMAIL>`;
        },
        fundamental: [{
            displayName: '上下',
            pos: ['forehead_jnt_tyPos'],
            neg: ['forehead_jnt_tyNeg'],
        }, {
            displayName: '前后',
            pos: ['forehead_jnt_tzPos'],
            neg: ['forehead_jnt_tzNeg'],
        }, {
            displayName: '宽窄',
            pos: ['forehead_jnt_sxPos'],
            neg: ['forehead_jnt_sxNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['forehead_jnt_txPos'],
                neg: ['forehead_jnt_txNeg'],
            }, {
                displayName: '上下',
                pos: ['forehead_jnt_tyPos'],
                neg: ['forehead_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['forehead_jnt_tzPos'],
                neg: ['forehead_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['forehead_jnt_rxPos'],
                neg: ['forehead_jnt_rxNeg'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['forehead_jnt_ryPos'],
                neg: ['forehead_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['forehead_jnt_rzPos'],
                neg: ['forehead_jnt_rzNeg'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['forehead_jnt_sxPos'],
                neg: ['forehead_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['forehead_jnt_syPos'],
                neg: ['forehead_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['forehead_jnt_szPos'],
                neg: ['forehead_jnt_szNeg'],
            }],
        }],
    },
    ophryon: {
        // name: 'ophryon_jnt',
        key: 'ophryon_jnt',
        displayName: '印堂',
        icon(gender) {
            return `${VITE_GLOB_IMG_URL}/slices/${gender}<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                disabled: true,
            }, {
                displayName: '上下',
                pos: ['ophryon_jnt_tyPos'],
                neg: ['ophryon_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['ophryon_jnt_tzPos'],
                neg: ['ophryon_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['ophryon_jnt_rxPos'],
                neg: ['ophryon_jnt_rxNeg'],
            }, {
                displayName: '左右',  // 绕 y 轴
                disabled: true
            }, {
                displayName: '倾斜',  // 绕 z 轴
                disabled: true
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['ophryon_jnt_sxPos'],
                neg: ['ophryon_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['ophryon_jnt_syPos'],
                neg: ['ophryon_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['ophryon_jnt_szPos'],
                neg: ['ophryon_jnt_szNeg'],
            }],
        }]
    },
    malar: {
        key: 'malar',
        displayName: '太阳穴',
        icon(gender) {
            return `${VITE_GLOB_IMG_URL}/slices/${gender}<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_malar_jnt_txPos', 'R_malar_jnt_txNeg'],
                neg: ['L_malar_jnt_txNeg', 'R_malar_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_malar_jnt_tyPos', 'R_malar_jnt_tyPos'],
                neg: ['L_malar_jnt_tyNeg', 'R_malar_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_malar_jnt_tzPos', 'R_malar_jnt_tzPos'],
                neg: ['L_malar_jnt_tzNeg', 'R_malar_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_malar_jnt_rxNeg', 'R_malar_jnt_rxNeg'],
                neg: ['L_malar_jnt_rxPos', 'R_malar_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_malar_jnt_ryNeg', 'R_malar_jnt_ryPos'],
                neg: ['L_malar_jnt_ryPos', 'R_malar_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_malar_jnt_rzPos', 'R_malar_jnt_rzNeg'],
                neg: ['L_malar_jnt_rzNeg', 'R_malar_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_malar_jnt_sxPos', 'R_malar_jnt_sxPos'],
                neg: ['L_malar_jnt_sxNeg', 'R_malar_jnt_sxPos'],
            }, {
                displayName: '高度',
                pos: ['L_malar_jnt_syPos', 'R_malar_jnt_syPos'],
                neg: ['L_malar_jnt_syNeg', 'R_malar_jnt_syPos'],
            }, {
                displayName: '饱满',
                pos: ['L_malar_jnt_szPos', 'R_malar_jnt_szPos'],
                neg: ['L_malar_jnt_szNeg', 'R_malar_jnt_szPos'],
            }],
        }],
    },
    cheek: {
        key: 'cheek',
        displayName: '颧骨',
        icon(gender) {
            return `${VITE_GLOB_IMG_URL}/slices/${gender}<EMAIL>`;
        },
        fundamental: [{
            displayName: '左右',
            pos: ['L_cheek_jnt_txPos', 'R_cheek_jnt_txNeg'],
            neg: ['L_cheek_jnt_txNeg', 'R_cheek_jnt_txPos'],
        }, {
            displayName: '上下',
            pos: ['L_cheek_jnt_tyPos', 'R_cheek_jnt_tyPos'],
            neg: ['L_cheek_jnt_tyNeg', 'R_cheek_jnt_tyNeg'],
        }, {
            displayName: '前后',
            pos: ['L_cheek_jnt_tzPos', 'R_cheek_jnt_tzPos'],
            neg: ['L_cheek_jnt_tzNeg', 'R_cheek_jnt_tzNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_cheek_jnt_txPos', 'R_cheek_jnt_txNeg'],
                neg: ['L_cheek_jnt_txNeg', 'R_cheek_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_cheek_jnt_tyPos', 'R_cheek_jnt_tyPos'],
                neg: ['L_cheek_jnt_tyNeg', 'R_cheek_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_cheek_jnt_tzPos', 'R_cheek_jnt_tzPos'],
                neg: ['L_cheek_jnt_tzNeg', 'R_cheek_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_cheek_jnt_rxNeg', 'R_cheek_jnt_rxNeg'],
                neg: ['L_cheek_jnt_rxPos', 'R_cheek_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_cheek_jnt_ryNeg', 'R_cheek_jnt_ryPos'],
                neg: ['L_cheek_jnt_ryPos', 'R_cheek_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_cheek_jnt_rzPos', 'R_cheek_jnt_rzNeg'],
                neg: ['L_cheek_jnt_rzNeg', 'R_cheek_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_cheek_jnt_sxPos', 'R_cheek_jnt_sxPos'],
                neg: ['L_cheek_jnt_sxNeg', 'R_cheek_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_cheek_jnt_syPos', 'R_cheek_jnt_syPos'],
                neg: ['L_cheek_jnt_syNeg', 'R_cheek_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_cheek_jnt_szPos', 'R_cheek_jnt_szPos'],
                neg: ['L_cheek_jnt_szNeg', 'R_cheek_jnt_szNeg'],
            }],
        }],
    },
    cheekUp: {
        key: 'cheekUp',
        displayName: '脸颊',
        icon(gender) {
            return `${VITE_GLOB_IMG_URL}/slices/${gender}<EMAIL>`;
        },
        fundamental: [{
            displayName: '左右',
            pos: ['L_cheekUp_jnt_txPos', 'R_cheekUp_jnt_txNeg'],
            neg: ['L_cheekUp_jnt_txNeg', 'R_cheekUp_jnt_txPos'],
        }, {
            displayName: '上下',
            pos: ['L_cheekUp_jnt_tyPos', 'R_cheekUp_jnt_tyPos'],
            neg: ['L_cheekUp_jnt_tyNeg', 'R_cheekUp_jnt_tyNeg'],
        }, {
            displayName: '前后',
            pos: ['L_cheekUp_jnt_tzPos', 'R_cheekUp_jnt_tzPos'],
            neg: ['L_cheekUp_jnt_tzNeg', 'R_cheekUp_jnt_tzNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_cheekUp_jnt_txPos', 'R_cheekUp_jnt_txNeg'],
                neg: ['L_cheekUp_jnt_txNeg', 'R_cheekUp_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_cheekUp_jnt_tyPos', 'R_cheekUp_jnt_tyPos'],
                neg: ['L_cheekUp_jnt_tyNeg', 'R_cheekUp_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_cheekUp_jnt_tzPos', 'R_cheekUp_jnt_tzPos'],
                neg: ['L_cheekUp_jnt_tzNeg', 'R_cheekUp_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_cheekUp_jnt_rxNeg', 'R_cheekUp_jnt_rxNeg'],
                neg: ['L_cheekUp_jnt_rxPos', 'R_cheekUp_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_cheekUp_jnt_ryNeg', 'R_cheekUp_jnt_ryPos'],
                neg: ['L_cheekUp_jnt_ryPos', 'R_cheekUp_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_cheekUp_jnt_rzPos', 'R_cheekUp_jnt_rzNeg'],
                neg: ['L_cheekUp_jnt_rzNeg', 'R_cheekUp_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_cheekUp_jnt_sxPos', 'R_cheekUp_jnt_sxPos'],
                neg: ['L_cheekUp_jnt_sxNeg', 'R_cheekUp_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_cheekUp_jnt_syPos', 'R_cheekUp_jnt_syPos'],
                neg: ['L_cheekUp_jnt_syNeg', 'R_cheekUp_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_cheekUp_jnt_szPos', 'R_cheekUp_jnt_szPos'],
                neg: ['L_cheekUp_jnt_szNeg', 'R_cheekUp_jnt_szNeg'],
            }],
        }],
    },
    levator: {
        key: 'levator',
        displayName: '咬肌',
        icon(gender) {
            return `${VITE_GLOB_IMG_URL}/slices/${gender}<EMAIL>`;
        },
        fundamental: [{
            displayName: '左右',
            pos: ['L_levator_jnt_txPos', 'R_levator_jnt_txNeg'],
            neg: ['L_levator_jnt_txNeg', 'R_levator_jnt_txPos'],
        }, {
            displayName: '上下',
            pos: ['L_levator_jnt_tyPos', 'R_levator_jnt_tyPos'],
            neg: ['L_levator_jnt_tyNeg', 'R_levator_jnt_tyNeg'],
        }, {
            displayName: '前后',
            pos: ['L_levator_jnt_tzPos', 'R_levator_jnt_tzPos'],
            neg: ['L_levator_jnt_tzNeg', 'R_levator_jnt_tzNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_levator_jnt_txPos', 'R_levator_jnt_txNeg'],
                neg: ['L_levator_jnt_txNeg', 'R_levator_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_levator_jnt_tyPos', 'R_levator_jnt_tyPos'],
                neg: ['L_levator_jnt_tyNeg', 'R_levator_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_levator_jnt_tzPos', 'R_levator_jnt_tzPos'],
                neg: ['L_levator_jnt_tzNeg', 'R_levator_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_levator_jnt_rxNeg', 'R_levator_jnt_rxNeg'],
                neg: ['L_levator_jnt_rxPos', 'R_levator_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_levator_jnt_ryNeg', 'R_levator_jnt_ryPos'],
                neg: ['L_levator_jnt_ryPos', 'R_levator_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_levator_jnt_rzPos', 'R_levator_jnt_rzNeg'],
                neg: ['L_levator_jnt_rzNeg', 'R_levator_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_levator_jnt_sxPos', 'R_levator_jnt_sxPos'],
                neg: ['L_levator_jnt_sxNeg', 'R_levator_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_levator_jnt_syPos', 'R_levator_jnt_syPos'],
                neg: ['L_levator_jnt_syNeg', 'R_levator_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_levator_jnt_szPos', 'R_levator_jnt_szPos'],
                neg: ['L_levator_jnt_szNeg', 'R_levator_jnt_szNeg'],
            }],
        }],
    },
    chin: {
        key: 'chin',
        displayName: '下巴尖',
        icon(gender) {
            return `${VITE_GLOB_IMG_URL}/slices/${gender}<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                disabled: true,
            }, {
                displayName: '上下',
                pos: ['chin_jnt_tyPos'],
                neg: ['chin_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['chin_jnt_tzPos'],
                neg: ['chin_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['chin_jnt_rxNeg'],
                neg: ['chin_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                disabled: true,
            }, {
                displayName: '倾斜',  // 绕 z 轴
                disabled: true,
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['chin_jnt_sxPos'],
                neg: ['chin_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['chin_jnt_syPos'],
                neg: ['chin_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['chin_jnt_szPos'],
                neg: ['chin_jnt_szNeg'],
            }],
        }],
    },
    chins: {
        key: 'chins',
        displayName: '下巴',
        icon(gender) {
            return `${VITE_GLOB_IMG_URL}/slices/${gender}<EMAIL>`;
        },
        fundamental: [{
            displayName: '前后',
            pos: ['L_chin_jnt_tzPos', 'R_chin_jnt_tzPos'],
            neg: ['L_chin_jnt_tzNeg', 'R_chin_jnt_tzPos'],
        }, {
            displayName: '宽窄',
            pos: ['L_chin_jnt_txPos', 'R_chin_jnt_txNeg'],
            neg: ['L_chin_jnt_txNeg', 'R_chin_jnt_txNeg'],
        }, {
            displayName: '饱满',
            pos: ['L_chin_jnt_szPos', 'R_chin_jnt_szPos'],
            neg: ['L_chin_jnt_szNeg', 'R_chin_jnt_szNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_chin_jnt_txPos', 'R_chin_jnt_txNeg'],
                neg: ['L_chin_jnt_txNeg', 'R_chin_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_chin_jnt_tyPos', 'R_chin_jnt_tyPos'],
                neg: ['L_chin_jnt_tyNeg', 'R_chin_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_chin_jnt_tzPos', 'R_chin_jnt_tzPos'],
                neg: ['L_chin_jnt_tzNeg', 'R_chin_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_chin_jnt_rxNeg', 'R_chin_jnt_rxNeg'],
                neg: ['L_chin_jnt_rxPos', 'R_chin_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_chin_jnt_ryNeg', 'R_chin_jnt_ryPos'],
                neg: ['L_chin_jnt_ryPos', 'R_chin_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_chin_jnt_rzPos', 'R_chin_jnt_rzNeg'],
                neg: ['L_chin_jnt_rzNeg', 'R_chin_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_chin_jnt_sxPos', 'R_chin_jnt_sxPos'],
                neg: ['L_chin_jnt_sxNeg', 'R_chin_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_chin_jnt_syPos', 'R_chin_jnt_syPos'],
                neg: ['L_chin_jnt_syNeg', 'R_chin_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_chin_jnt_szPos', 'R_chin_jnt_szPos'],
                neg: ['L_chin_jnt_szNeg', 'R_chin_jnt_szNeg'],
            }],
        }],
    },
    mandible: {
        key: 'mandible',
        displayName: '下颌',
        icon(gender) {
            return `${VITE_GLOB_IMG_URL}/slices/${gender}<EMAIL>`;
        },
        fundamental: [{
            displayName: '左右',
            pos: ['L_mandible_jnt_sxPos', 'R_mandible_jnt_sxPos'],
            neg: ['L_mandible_jnt_sxNeg', 'R_mandible_jnt_sxNeg'],
        }, {
            displayName: '上下',
            pos: ['L_mandible_jnt_syPos', 'R_mandible_jnt_syPos'],
            neg: ['L_mandible_jnt_syNeg', 'R_mandible_jnt_syNeg'],
        }, {
            displayName: '前后',
            pos: ['L_mandible_jnt_tzPos', 'R_mandible_jnt_tzPos'],
            neg: ['L_mandible_jnt_tzNeg', 'R_mandible_jnt_tzNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_mandible_jnt_txPos', 'R_mandible_jnt_txNeg'],
                neg: ['L_mandible_jnt_txNeg', 'R_mandible_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_mandible_jnt_tyPos', 'R_mandible_jnt_tyPos'],
                neg: ['L_mandible_jnt_tyNeg', 'R_mandible_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_mandible_jnt_tzPos', 'R_mandible_jnt_tzPos'],
                neg: ['L_mandible_jnt_tzNeg', 'R_mandible_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_mandible_jnt_rxNeg', 'R_mandible_jnt_rxNeg'],
                neg: ['L_mandible_jnt_rxPos', 'R_mandible_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_mandible_jnt_ryNeg', 'R_mandible_jnt_ryPos'],
                neg: ['L_mandible_jnt_ryPos', 'R_mandible_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_mandible_jnt_rzPos', 'R_mandible_jnt_rzNeg'],
                neg: ['L_mandible_jnt_rzNeg', 'R_mandible_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_mandible_jnt_sxPos', 'R_mandible_jnt_sxPos'],
                neg: ['L_mandible_jnt_sxNeg', 'R_mandible_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_mandible_jnt_syPos', 'R_mandible_jnt_syPos'],
                neg: ['L_mandible_jnt_syNeg', 'R_mandible_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_mandible_jnt_szPos', 'R_mandible_jnt_szPos'],
                neg: ['L_mandible_jnt_szNeg', 'R_mandible_jnt_szNeg'],
            }],
        }],
    },
    mandibleCape: {
        key: 'mandibleCape',
        displayName: '下颌角',
        icon(gender) {
            return `${VITE_GLOB_IMG_URL}/slices/${gender}<EMAIL>`;
        },
        fundamental: [{
            displayName: '左右',
            pos: ['L_mandibleCape_jnt_txPos', 'R_mandibleCape_jnt_txNeg'],
            neg: ['L_mandibleCape_jnt_txNeg', 'R_mandibleCape_jnt_txPos'],
        }, {
            displayName: '上下',
            pos: ['L_mandibleCape_jnt_tyPos', 'R_mandibleCape_jnt_tyPos'],
            neg: ['L_mandibleCape_jnt_tyNeg', 'R_mandibleCape_jnt_tyNeg'],
        }, {
            displayName: '前后',
            pos: ['L_mandibleCape_jnt_tzPos', 'R_mandibleCape_jnt_tzPos'],
            neg: ['L_mandibleCape_jnt_tzNeg', 'R_mandibleCape_jnt_tzNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_mandibleCape_jnt_txPos', 'R_mandibleCape_jnt_txNeg'],
                neg: ['L_mandibleCape_jnt_txNeg', 'R_mandibleCape_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_mandibleCape_jnt_tyPos', 'R_mandibleCape_jnt_tyPos'],
                neg: ['L_mandibleCape_jnt_tyNeg', 'R_mandibleCape_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_mandibleCape_jnt_tzPos', 'R_mandibleCape_jnt_tzPos'],
                neg: ['L_mandibleCape_jnt_tzNeg', 'R_mandibleCape_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_mandibleCape_jnt_rxNeg', 'R_mandibleCape_jnt_rxNeg'],
                neg: ['L_mandibleCape_jnt_rxPos', 'R_mandibleCape_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_mandibleCape_jnt_ryNeg', 'R_mandibleCape_jnt_ryPos'],
                neg: ['L_mandibleCape_jnt_ryPos', 'R_mandibleCape_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_mandibleCape_jnt_rzPos', 'R_mandibleCape_jnt_rzNeg'],
                neg: ['L_mandibleCape_jnt_rzNeg', 'R_mandibleCape_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_mandibleCape_jnt_sxPos', 'R_mandibleCape_jnt_sxPos'],
                neg: ['L_mandibleCape_jnt_sxNeg', 'R_mandibleCape_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_mandibleCape_jnt_syPos', 'R_mandibleCape_jnt_syPos'],
                neg: ['L_mandibleCape_jnt_syNeg', 'R_mandibleCape_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_mandibleCape_jnt_szPos', 'R_mandibleCape_jnt_szPos'],
                neg: ['L_mandibleCape_jnt_szNeg', 'R_mandibleCape_jnt_szNeg'],
            }],
        }],
    },
    brow: {
        key: 'brow',
        displayName: '眉毛',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [{
            displayName: '间距',
            pos: ['L_brow_jnt_txPos', 'R_brow_jnt_txNeg'],
            neg: ['L_brow_jnt_txNeg', 'R_brow_jnt_txPos'],
        }, {
            displayName: '上下',
            pos: ['L_brow_jnt_tyPos', 'R_brow_jnt_tyPos'],
            neg: ['L_brow_jnt_tyNeg', 'R_brow_jnt_tyNeg'],
        }, {
            displayName: '宽窄',
            pos: ['L_brow_jnt_syPos', 'R_brow_jnt_syPos'],
            neg: ['L_brow_jnt_syNeg', 'R_brow_jnt_syNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_brow_jnt_txPos', 'R_brow_jnt_txNeg'],
                neg: ['L_brow_jnt_txNeg', 'R_brow_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_brow_jnt_tyPos', 'R_brow_jnt_tyPos'],
                neg: ['L_brow_jnt_tyNeg', 'R_brow_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_brow_jnt_tzPos', 'R_brow_jnt_tzPos'],
                neg: ['L_brow_jnt_tzNeg', 'R_brow_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_brow_jnt_rxNeg', 'R_brow_jnt_rxNeg'],
                neg: ['L_brow_jnt_rxPos', 'R_brow_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_brow_jnt_ryNeg', 'R_brow_jnt_ryPos'],
                neg: ['L_brow_jnt_ryPos', 'R_brow_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_brow_jnt_rzPos', 'R_brow_jnt_rzNeg'],
                neg: ['L_brow_jnt_rzNeg', 'R_brow_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_brow_jnt_sxPos', 'R_brow_jnt_sxPos'],
                neg: ['L_brow_jnt_sxNeg', 'R_brow_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_brow_jnt_syPos', 'R_brow_jnt_syPos'],
                neg: ['L_brow_jnt_syNeg', 'R_brow_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_brow_jnt_szPos', 'R_brow_jnt_szPos'],
                neg: ['L_brow_jnt_szNeg', 'R_brow_jnt_szNeg'],
            }],
        }],
    },
    innBrow: {
        key: 'innBrow',
        displayName: '眉头',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [{
            displayName: '间距',
            pos: ['L_innBrow_jnt_txPos', 'R_innBrow_jnt_txNeg'],
            neg: ['L_innBrow_jnt_txNeg', 'R_innBrow_jnt_txPos'],
        }, {
            displayName: '上下',
            pos: ['L_innBrow_jnt_tyPos', 'R_innBrow_jnt_tyPos'],
            neg: ['L_innBrow_jnt_tyNeg', 'R_innBrow_jnt_tyNeg'],
        }, {
            displayName: '宽窄',
            pos: ['L_innBrow_jnt_syPos', 'R_innBrow_jnt_syPos'],
            neg: ['L_innBrow_jnt_syNeg', 'R_innBrow_jnt_syNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_innBrow_jnt_txPos', 'R_innBrow_jnt_txNeg'],
                neg: ['L_innBrow_jnt_txNeg', 'R_innBrow_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_innBrow_jnt_tyPos', 'R_innBrow_jnt_tyPos'],
                neg: ['L_innBrow_jnt_tyNeg', 'R_innBrow_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_innBrow_jnt_tzPos', 'R_innBrow_jnt_tzPos'],
                neg: ['L_innBrow_jnt_tzNeg', 'R_innBrow_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_innBrow_jnt_rxNeg', 'R_innBrow_jnt_rxNeg'],
                neg: ['L_innBrow_jnt_rxPos', 'R_innBrow_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_innBrow_jnt_ryNeg', 'R_innBrow_jnt_ryPos'],
                neg: ['L_innBrow_jnt_ryPos', 'R_innBrow_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_innBrow_jnt_rzPos', 'R_innBrow_jnt_rzNeg'],
                neg: ['L_innBrow_jnt_rzNeg', 'R_innBrow_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_innBrow_jnt_sxPos', 'R_innBrow_jnt_sxPos'],
                neg: ['L_innBrow_jnt_sxNeg', 'R_innBrow_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_innBrow_jnt_syPos', 'R_innBrow_jnt_syPos'],
                neg: ['L_innBrow_jnt_syNeg', 'R_innBrow_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_innBrow_jnt_szPos', 'R_innBrow_jnt_szPos'],
                neg: ['L_innBrow_jnt_szNeg', 'R_innBrow_jnt_szNeg'],
            }],
        }],
    },
    midBrow: {
        key: 'midBrow',
        displayName: '眉中',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_midBrow_jnt_txPos', 'R_midBrow_jnt_txNeg'],
                neg: ['L_midBrow_jnt_txNeg', 'R_midBrow_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_midBrow_jnt_tyPos', 'R_midBrow_jnt_tyPos'],
                neg: ['L_midBrow_jnt_tyNeg', 'R_midBrow_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_midBrow_jnt_tzPos', 'R_midBrow_jnt_tzPos'],
                neg: ['L_midBrow_jnt_tzNeg', 'R_midBrow_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_midBrow_jnt_rxNeg', 'R_midBrow_jnt_rxNeg'],
                neg: ['L_midBrow_jnt_rxPos', 'R_midBrow_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_midBrow_jnt_ryNeg', 'R_midBrow_jnt_ryPos'],
                neg: ['L_midBrow_jnt_ryPos', 'R_midBrow_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_midBrow_jnt_rzPos', 'R_midBrow_jnt_rzNeg'],
                neg: ['L_midBrow_jnt_rzNeg', 'R_midBrow_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_midBrow_jnt_sxPos', 'R_midBrow_jnt_sxPos'],
                neg: ['L_midBrow_jnt_sxNeg', 'R_midBrow_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_midBrow_jnt_syPos', 'R_midBrow_jnt_syPos'],
                neg: ['L_midBrow_jnt_syNeg', 'R_midBrow_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_midBrow_jnt_szPos', 'R_midBrow_jnt_szPos'],
                neg: ['L_midBrow_jnt_szNeg', 'R_midBrow_jnt_szNeg'],
            }],
        }],
    },
    outBrow: {
        key: 'outBrow',
        displayName: '眉尾',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_outBrow_jnt_txPos', 'R_outBrow_jnt_txNeg'],
                neg: ['L_outBrow_jnt_txNeg', 'R_outBrow_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_outBrow_jnt_tyPos', 'R_outBrow_jnt_tyPos'],
                neg: ['L_outBrow_jnt_tyNeg', 'R_outBrow_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_outBrow_jnt_tzPos', 'R_outBrow_jnt_tzPos'],
                neg: ['L_outBrow_jnt_tzNeg', 'R_outBrow_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_outBrow_jnt_rxNeg', 'R_outBrow_jnt_rxNeg'],
                neg: ['L_outBrow_jnt_rxPos', 'R_outBrow_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_outBrow_jnt_ryNeg', 'R_outBrow_jnt_ryPos'],
                neg: ['L_outBrow_jnt_ryPos', 'R_outBrow_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_outBrow_jnt_rzPos', 'R_outBrow_jnt_rzNeg'],
                neg: ['L_outBrow_jnt_rzNeg', 'R_outBrow_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_midBrow_jnt_sxPos', 'R_midBrow_jnt_sxPos'],
                neg: ['L_midBrow_jnt_sxNeg', 'R_midBrow_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_midBrow_jnt_syPos', 'R_midBrow_jnt_syPos'],
                neg: ['L_midBrow_jnt_syNeg', 'R_midBrow_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_midBrow_jnt_szPos', 'R_midBrow_jnt_szPos'],
                neg: ['L_midBrow_jnt_szNeg', 'R_midBrow_jnt_szNeg'],
            }],
        }],
    },
    eye: {
        key: 'eyeBrow',
        displayName: '眼睛',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [{
            displayName: '间距',
            pos: ['L_eye_jnt_txPos', 'R_eye_jnt_txNeg'],
            neg: ['L_eye_jnt_txNeg', 'R_eye_jnt_txPos'],
        }, {
            displayName: '上下',
            pos: ['L_eye_jnt_tyPos', 'R_eye_jnt_tyPos'],
            neg: ['L_eye_jnt_tyNeg', 'R_eye_jnt_tyNeg'],
        }, {
            displayName: '角度',
            pos: ['L_eye_jnt_rzPos', 'R_eye_jnt_rzNeg'],
            neg: ['L_eye_jnt_rzNeg', 'R_eye_jnt_rzPos'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_eye_jnt_txPos', 'R_eye_jnt_txNeg'],
                neg: ['L_eye_jnt_txNeg', 'R_eye_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_eye_jnt_tyPos', 'R_eye_jnt_tyPos'],
                neg: ['L_eye_jnt_tyNeg', 'R_eye_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_eye_jnt_tzPos', 'R_eye_jnt_tzPos'],
                neg: ['L_eye_jnt_tzNeg', 'R_eye_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_eye_jnt_rxNeg', 'R_eye_jnt_rxNeg'],
                neg: ['L_eye_jnt_rxPos', 'R_eye_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_eye_jnt_ryNeg', 'R_eye_jnt_ryPos'],
                neg: ['L_eye_jnt_ryPos', 'R_eye_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_eye_jnt_rzPos', 'R_eye_jnt_rzNeg'],
                neg: ['L_eye_jnt_rzNeg', 'R_eye_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_eye_jnt_sxPos', 'R_eye_jnt_sxPos'],
                neg: ['L_eye_jnt_sxNeg', 'R_eye_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_eye_jnt_syPos', 'R_eye_jnt_syPos'],
                neg: ['L_eye_jnt_syNeg', 'R_eye_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_eye_jnt_szPos', 'R_eye_jnt_szPos'],
                neg: ['L_eye_jnt_szNeg', 'R_eye_jnt_szNeg'],
            }],
        }],
    },
    interEyeLid: {
        key: 'interEyeLid',
        displayName: '内眼角',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_interEyeLid_jnt_txPos', 'R_interEyeLid_jnt_txNeg'],
                neg: ['L_interEyeLid_jnt_txNeg', 'R_interEyeLid_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_interEyeLid_jnt_tyPos', 'R_interEyeLid_jnt_tyPos'],
                neg: ['L_interEyeLid_jnt_tyNeg', 'R_interEyeLid_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_interEyeLid_jnt_tzPos', 'R_interEyeLid_jnt_tzPos'],
                neg: ['L_interEyeLid_jnt_tzNeg', 'R_interEyeLid_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_interEyeLid_jnt_rxNeg', 'R_interEyeLid_jnt_rxNeg'],
                neg: ['L_interEyeLid_jnt_rxPos', 'R_interEyeLid_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_interEyeLid_jnt_ryNeg', 'R_interEyeLid_jnt_ryPos'],
                neg: ['L_interEyeLid_jnt_ryPos', 'R_interEyeLid_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_interEyeLid_jnt_rzPos', 'R_interEyeLid_jnt_rzNeg'],
                neg: ['L_interEyeLid_jnt_rzNeg', 'R_interEyeLid_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_interEyeLid_jnt_sxPos', 'R_interEyeLid_jnt_sxPos'],
                neg: ['L_interEyeLid_jnt_sxNeg', 'R_interEyeLid_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_interEyeLid_jnt_syPos', 'R_interEyeLid_jnt_syPos'],
                neg: ['L_interEyeLid_jnt_syNeg', 'R_interEyeLid_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_interEyeLid_jnt_szPos', 'R_interEyeLid_jnt_szPos'],
                neg: ['L_interEyeLid_jnt_szNeg', 'R_interEyeLid_jnt_szNeg'],
            }],
        }],
    },
    outerEyeLid: {
        key: 'outerEyeLid',
        displayName: '外眼角',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_outerEyeLid_jnt_txPos', 'R_outerEyeLid_jnt_txNeg'],
                neg: ['L_outerEyeLid_jnt_txNeg', 'R_outerEyeLid_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_outerEyeLid_jnt_tyPos', 'R_outerEyeLid_jnt_tyPos'],
                neg: ['L_outerEyeLid_jnt_tyNeg', 'R_outerEyeLid_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_outerEyeLid_jnt_tzPos', 'R_outerEyeLid_jnt_tzPos'],
                neg: ['L_outerEyeLid_jnt_tzNeg', 'R_outerEyeLid_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_outerEyeLid_jnt_rxNeg', 'R_outerEyeLid_jnt_rxNeg'],
                neg: ['L_outerEyeLid_jnt_rxPos', 'R_outerEyeLid_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_outerEyeLid_jnt_ryNeg', 'R_outerEyeLid_jnt_ryPos'],
                neg: ['L_outerEyeLid_jnt_ryPos', 'R_outerEyeLid_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_outerEyeLid_jnt_rzPos', 'R_outerEyeLid_jnt_rzNeg'],
                neg: ['L_outerEyeLid_jnt_rzNeg', 'R_outerEyeLid_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_outerEyeLid_jnt_sxPos', 'R_outerEyeLid_jnt_sxPos'],
                neg: ['L_outerEyeLid_jnt_sxNeg', 'R_outerEyeLid_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_outerEyeLid_jnt_syPos', 'R_outerEyeLid_jnt_syPos'],
                neg: ['L_outerEyeLid_jnt_syNeg', 'R_outerEyeLid_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_outerEyeLid_jnt_szPos', 'R_outerEyeLid_jnt_szPos'],
                neg: ['L_outerEyeLid_jnt_szNeg', 'R_outerEyeLid_jnt_szNeg'],
            }],
        }],
    },
    upperEyeLidIn: {
        key: 'upperEyeLidIn',
        displayName: '上眼皮·内',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_upperEyeLidIn_jnt_txPos', 'R_upperEyeLidIn_jnt_txNeg'],
                neg: ['L_upperEyeLidIn_jnt_txNeg', 'R_upperEyeLidIn_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_upperEyeLidIn_jnt_tyPos', 'R_upperEyeLidIn_jnt_tyPos'],
                neg: ['L_upperEyeLidIn_jnt_tyNeg', 'R_upperEyeLidIn_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_upperEyeLidIn_jnt_tzPos', 'R_upperEyeLidIn_jnt_tzPos'],
                neg: ['L_upperEyeLidIn_jnt_tzNeg', 'R_upperEyeLidIn_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_upperEyeLidIn_jnt_rxNeg', 'R_upperEyeLidIn_jnt_rxNeg'],
                neg: ['L_upperEyeLidIn_jnt_rxPos', 'R_upperEyeLidIn_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_upperEyeLidIn_jnt_ryNeg', 'R_upperEyeLidIn_jnt_ryPos'],
                neg: ['L_upperEyeLidIn_jnt_ryPos', 'R_upperEyeLidIn_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_upperEyeLidIn_jnt_rzPos', 'R_upperEyeLidIn_jnt_rzNeg'],
                neg: ['L_upperEyeLidIn_jnt_rzNeg', 'R_upperEyeLidIn_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_upperEyeLidIn_jnt_sxPos', 'R_upperEyeLidIn_jnt_sxPos'],
                neg: ['L_upperEyeLidIn_jnt_sxNeg', 'R_upperEyeLidIn_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_upperEyeLidIn_jnt_syPos', 'R_upperEyeLidIn_jnt_syPos'],
                neg: ['L_upperEyeLidIn_jnt_syNeg', 'R_upperEyeLidIn_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_upperEyeLidIn_jnt_szPos', 'R_upperEyeLidIn_jnt_szPos'],
                neg: ['L_upperEyeLidIn_jnt_szNeg', 'R_upperEyeLidIn_jnt_szNeg'],
            }],
        }],
    },
    upperEyeLid: {
        key: 'upperEyeLid',
        displayName: '上眼皮·中',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_upperEyeLid_jnt_txPos', 'R_upperEyeLid_jnt_txNeg'],
                neg: ['L_upperEyeLid_jnt_txNeg', 'R_upperEyeLid_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_upperEyeLid_jnt_tyPos', 'R_upperEyeLid_jnt_tyPos'],
                neg: ['L_upperEyeLid_jnt_tyNeg', 'R_upperEyeLid_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_upperEyeLid_jnt_tzPos', 'R_upperEyeLid_jnt_tzPos'],
                neg: ['L_upperEyeLid_jnt_tzNeg', 'R_upperEyeLid_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_upperEyeLid_jnt_rxNeg', 'R_upperEyeLid_jnt_rxNeg'],
                neg: ['L_upperEyeLid_jnt_rxPos', 'R_upperEyeLid_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_upperEyeLid_jnt_ryNeg', 'R_upperEyeLid_jnt_ryPos'],
                neg: ['L_upperEyeLid_jnt_ryPos', 'R_upperEyeLid_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_upperEyeLid_jnt_rzPos', 'R_upperEyeLid_jnt_rzNeg'],
                neg: ['L_upperEyeLid_jnt_rzNeg', 'R_upperEyeLid_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_upperEyeLid_jnt_sxPos', 'R_upperEyeLid_jnt_sxPos'],
                neg: ['L_upperEyeLid_jnt_sxNeg', 'R_upperEyeLid_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_upperEyeLid_jnt_syPos', 'R_upperEyeLid_jnt_syPos'],
                neg: ['L_upperEyeLid_jnt_syNeg', 'R_upperEyeLid_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_upperEyeLid_jnt_szPos', 'R_upperEyeLid_jnt_szPos'],
                neg: ['L_upperEyeLid_jnt_szNeg', 'R_upperEyeLid_jnt_szNeg'],
            }],
        }],
    },
    upperEyeLidOut: {
        key: 'upperEyeLidOut',
        displayName: '上眼皮·外',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_upperEyeLidOut_jnt_txPos', 'R_upperEyeLidOut_jnt_txNeg'],
                neg: ['L_upperEyeLidOut_jnt_txNeg', 'R_upperEyeLidOut_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_upperEyeLidOut_jnt_tyPos', 'R_upperEyeLidOut_jnt_tyPos'],
                neg: ['L_upperEyeLidOut_jnt_tyNeg', 'R_upperEyeLidOut_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_upperEyeLidOut_jnt_tzPos', 'R_upperEyeLidOut_jnt_tzPos'],
                neg: ['L_upperEyeLidOut_jnt_tzNeg', 'R_upperEyeLidOut_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_upperEyeLidOut_jnt_rxNeg', 'R_upperEyeLidOut_jnt_rxNeg'],
                neg: ['L_upperEyeLidOut_jnt_rxPos', 'R_upperEyeLidOut_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_upperEyeLidOut_jnt_ryNeg', 'R_upperEyeLidOut_jnt_ryPos'],
                neg: ['L_upperEyeLidOut_jnt_ryPos', 'R_upperEyeLidOut_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_upperEyeLidOut_jnt_rzPos', 'R_upperEyeLidOut_jnt_rzNeg'],
                neg: ['L_upperEyeLidOut_jnt_rzNeg', 'R_upperEyeLidOut_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_upperEyeLidOut_jnt_sxPos', 'R_upperEyeLidOut_jnt_sxPos'],
                neg: ['L_upperEyeLidOut_jnt_sxNeg', 'R_upperEyeLidOut_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_upperEyeLidOut_jnt_syPos', 'R_upperEyeLidOut_jnt_syPos'],
                neg: ['L_upperEyeLidOut_jnt_syNeg', 'R_upperEyeLidOut_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_upperEyeLidOut_jnt_szPos', 'R_upperEyeLidOut_jnt_szPos'],
                neg: ['L_upperEyeLidOut_jnt_szNeg', 'R_upperEyeLidOut_jnt_szNeg'],
            }],
        }],
    },
    lowerEyeLidIn: {
        key: 'lowerEyeLidIn',
        displayName: '下眼皮·内',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_lowerEyeLidIn_jnt_txPos', 'R_lowerEyeLidIn_jnt_txNeg'],
                neg: ['L_lowerEyeLidIn_jnt_txNeg', 'R_lowerEyeLidIn_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_lowerEyeLidIn_jnt_tyPos', 'R_lowerEyeLidIn_jnt_tyPos'],
                neg: ['L_lowerEyeLidIn_jnt_tyNeg', 'R_lowerEyeLidIn_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_lowerEyeLidIn_jnt_tzPos', 'R_lowerEyeLidIn_jnt_tzPos'],
                neg: ['L_lowerEyeLidIn_jnt_tzNeg', 'R_lowerEyeLidIn_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_lowerEyeLidIn_jnt_rxNeg', 'R_lowerEyeLidIn_jnt_rxNeg'],
                neg: ['L_lowerEyeLidIn_jnt_rxPos', 'R_lowerEyeLidIn_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_lowerEyeLidIn_jnt_ryNeg', 'R_lowerEyeLidIn_jnt_ryPos'],
                neg: ['L_lowerEyeLidIn_jnt_ryPos', 'R_lowerEyeLidIn_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_lowerEyeLidIn_jnt_rzPos', 'R_lowerEyeLidIn_jnt_rzNeg'],
                neg: ['L_lowerEyeLidIn_jnt_rzNeg', 'R_lowerEyeLidIn_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_lowerEyeLidIn_jnt_sxPos', 'R_lowerEyeLidIn_jnt_sxPos'],
                neg: ['L_lowerEyeLidIn_jnt_sxNeg', 'R_lowerEyeLidIn_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_lowerEyeLidIn_jnt_syPos', 'R_lowerEyeLidIn_jnt_syPos'],
                neg: ['L_lowerEyeLidIn_jnt_syNeg', 'R_lowerEyeLidIn_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_lowerEyeLidIn_jnt_szPos', 'R_lowerEyeLidIn_jnt_szPos'],
                neg: ['L_lowerEyeLidIn_jnt_szNeg', 'R_lowerEyeLidIn_jnt_szNeg'],
            }],
        }],
    },
    lowerEyeLid: {
        key: 'lowerEyeLid',
        displayName: '下眼皮·中',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_lowerEyeLid_jnt_txPos', 'R_lowerEyeLid_jnt_txNeg'],
                neg: ['L_lowerEyeLid_jnt_txNeg', 'R_lowerEyeLid_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_lowerEyeLid_jnt_tyPos', 'R_lowerEyeLid_jnt_tyPos'],
                neg: ['L_lowerEyeLid_jnt_tyNeg', 'R_lowerEyeLid_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_lowerEyeLid_jnt_tzPos', 'R_lowerEyeLid_jnt_tzPos'],
                neg: ['L_lowerEyeLid_jnt_tzNeg', 'R_lowerEyeLid_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_lowerEyeLid_jnt_rxNeg', 'R_lowerEyeLid_jnt_rxNeg'],
                neg: ['L_lowerEyeLid_jnt_rxPos', 'R_lowerEyeLid_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_lowerEyeLid_jnt_ryNeg', 'R_lowerEyeLid_jnt_ryPos'],
                neg: ['L_lowerEyeLid_jnt_ryPos', 'R_lowerEyeLid_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_lowerEyeLid_jnt_rzPos', 'R_lowerEyeLid_jnt_rzNeg'],
                neg: ['L_lowerEyeLid_jnt_rzNeg', 'R_lowerEyeLid_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_lowerEyeLid_jnt_sxPos', 'R_lowerEyeLid_jnt_sxPos'],
                neg: ['L_lowerEyeLid_jnt_sxNeg', 'R_lowerEyeLid_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_lowerEyeLid_jnt_syPos', 'R_lowerEyeLid_jnt_syPos'],
                neg: ['L_lowerEyeLid_jnt_syNeg', 'R_lowerEyeLid_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_lowerEyeLid_jnt_szPos', 'R_lowerEyeLid_jnt_szPos'],
                neg: ['L_lowerEyeLid_jnt_szNeg', 'R_lowerEyeLid_jnt_szNeg'],
            }],
        }],
    },
    lowerEyeLidOut: {
        key: 'lowerEyeLidOut',
        displayName: '下眼皮·外',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_lowerEyeLidOut_jnt_txPos', 'R_lowerEyeLidOut_jnt_txNeg'],
                neg: ['L_lowerEyeLidOut_jnt_txNeg', 'R_lowerEyeLidOut_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_lowerEyeLidOut_jnt_tyPos', 'R_lowerEyeLidOut_jnt_tyPos'],
                neg: ['L_lowerEyeLidOut_jnt_tyNeg', 'R_lowerEyeLidOut_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_lowerEyeLidOut_jnt_tzPos', 'R_lowerEyeLidOut_jnt_tzPos'],
                neg: ['L_lowerEyeLidOut_jnt_tzNeg', 'R_lowerEyeLidOut_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_lowerEyeLidOut_jnt_rxNeg', 'R_lowerEyeLidOut_jnt_rxNeg'],
                neg: ['L_lowerEyeLidOut_jnt_rxPos', 'R_lowerEyeLidOut_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_lowerEyeLidOut_jnt_ryNeg', 'R_lowerEyeLidOut_jnt_ryPos'],
                neg: ['L_lowerEyeLidOut_jnt_ryPos', 'R_lowerEyeLidOut_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_lowerEyeLidOut_jnt_rzPos', 'R_lowerEyeLidOut_jnt_rzNeg'],
                neg: ['L_lowerEyeLidOut_jnt_rzNeg', 'R_lowerEyeLidOut_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_lowerEyeLidOut_jnt_sxPos', 'R_lowerEyeLidOut_jnt_sxPos'],
                neg: ['L_lowerEyeLidOut_jnt_sxNeg', 'R_lowerEyeLidOut_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_lowerEyeLidOut_jnt_syPos', 'R_lowerEyeLidOut_jnt_syPos'],
                neg: ['L_lowerEyeLidOut_jnt_syNeg', 'R_lowerEyeLidOut_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_lowerEyeLidOut_jnt_szPos', 'R_lowerEyeLidOut_jnt_szPos'],
                neg: ['L_lowerEyeLidOut_jnt_szNeg', 'R_lowerEyeLidOut_jnt_szNeg'],
            }],
        }],
    },
    pupil: {
        key: 'pupil',
        displayName: '瞳孔',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [{
            displayName: '左右',
            pos: ['L_pupil_jnt_tzPos', 'R_pupil_jnt_tzNeg'],
            neg: ['L_pupil_jnt_tzNeg', 'R_pupil_jnt_tzPos'],
        }, {
            displayName: '上下',
            pos: ['L_pupil_jnt_tyNeg', 'R_pupil_jnt_tyNeg'],
            neg: ['L_pupil_jnt_tyPos', 'R_pupil_jnt_tyPos'],
        }, {
            displayName: '大小',
            pos: ['L_pupil_jnt_syPos', 'L_pupil_jnt_szPos', 'R_pupil_jnt_syPos', 'R_pupil_jnt_szPos'],
            neg: ['L_pupil_jnt_syNeg', 'L_pupil_jnt_szNeg', 'R_pupil_jnt_syNeg', 'R_pupil_jnt_szNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_pupil_jnt_tzPos', 'R_pupil_jnt_tzNeg'],
                neg: ['L_pupil_jnt_tzNeg', 'R_pupil_jnt_tzPos'],
            }, {
                displayName: '上下',
                pos: ['L_pupil_jnt_tyNeg', 'R_pupil_jnt_tyNeg'],
                neg: ['L_pupil_jnt_tyPos', 'R_pupil_jnt_tyPos'],
            }, {
                displayName: '前后',
                pos: ['L_pupil_jnt_txPos', 'R_pupil_jnt_txPos'],
                neg: ['L_pupil_jnt_txNeg', 'R_pupil_jnt_txNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_pupil_jnt_rzNeg', 'R_pupil_jnt_rzNeg'],
                neg: ['L_pupil_jnt_rzPos', 'R_pupil_jnt_rzPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_pupil_jnt_ryNeg', 'R_pupil_jnt_ryPos'],
                neg: ['L_pupil_jnt_ryPos', 'R_pupil_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_pupil_jnt_rxPos', 'R_pupil_jnt_rxNeg'],
                neg: ['L_pupil_jnt_rxNeg', 'R_pupil_jnt_rxPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_pupil_jnt_szPos', 'R_pupil_jnt_szPos'],
                neg: ['L_pupil_jnt_szNeg', 'R_pupil_jnt_szNeg'],
            }, {
                displayName: '高度',
                pos: ['L_pupil_jnt_syPos', 'R_pupil_jnt_syPos'],
                neg: ['L_pupil_jnt_syNeg', 'R_pupil_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_pupil_jnt_sxPos', 'R_pupil_jnt_sxPos'],
                neg: ['L_pupil_jnt_sxNeg', 'R_pupil_jnt_sxNeg'],
            }],
        }],
    },
    mouth: {
        key: 'mouth',
        displayName: '嘴巴',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [{
            displayName: '上下',
            pos: ['mouth_jnt_tyPos'],
            neg: ['mouth_jnt_tyNeg'],
        }, {
            displayName: '大小',
            pos: ['mouth_jnt_sxPos'],
            neg: ['mouth_jnt_sxNeg'],
        }, {
            displayName: '薄厚',
            pos: ['mouth_jnt_syPos'],
            neg: ['mouth_jnt_syNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                disabled: true,
            }, {
                displayName: '上下',
                pos: ['mouth_jnt_tyPos'],
                neg: ['mouth_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['mouth_jnt_tzPos'],
                neg: ['mouth_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['mouth_jnt_rxNeg'],
                neg: ['mouth_jnt_ryPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                disabled: true,
            }, {
                displayName: '倾斜',  // 绕 z 轴
                disabled: true,
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['mouth_jnt_sxPos'],
                neg: ['mouth_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['mouth_jnt_syPos'],
                neg: ['mouth_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['mouth_jnt_szPos'],
                neg: ['mouth_jnt_szNeg'],
            }],
        }],
    },
    lip: {
        key: 'lip',
        displayName: '唇中',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['lowerLip_jnt_txPos', 'upperLip_jnt_txPos'],
                neg: ['lowerLip_jnt_txNeg', 'upperLip_jnt_txNeg'],
            }, {
                displayName: '上下',
                pos: ['lowerLip_jnt_tyPos', 'upperLip_jnt_tyPos'],
                neg: ['lowerLip_jnt_tyNeg', 'upperLip_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['lowerLip_jnt_tzPos', 'upperLip_jnt_tzPos'],
                neg: ['lowerLip_jnt_tzNeg', 'upperLip_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['lowerLip_jnt_rxNeg', 'upperLip_jnt_rxNeg'],
                neg: ['lowerLip_jnt_rxPos', 'upperLip_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['lowerLip_jnt_ryPos', 'upperLip_jnt_ryPos'],
                neg: ['lowerLip_jnt_ryNeg', 'upperLip_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['lowerLip_jnt_rzPos', 'upperLip_jnt_rzPos'],
                neg: ['lowerLip_jnt_rzNeg', 'upperLip_jnt_rzNeg'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['lowerLip_jnt_sxPos', 'upperLip_jnt_sxPos'],
                neg: ['lowerLip_jnt_sxNeg', 'upperLip_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['lowerLip_jnt_syPos', 'upperLip_jnt_syPos'],
                neg: ['lowerLip_jnt_syNeg', 'upperLip_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['lowerLip_jnt_szPos', 'upperLip_jnt_szPos'],
                neg: ['lowerLip_jnt_szNeg', 'upperLip_jnt_szNeg'],
            }],
        }],
    },
    upperLip: {
        key: 'upperLip',
        displayName: '上唇',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_upperLip_jnt_txPos', 'R_upperLip_jnt_txNeg'],
                neg: ['L_upperLip_jnt_txNeg', 'R_upperLip_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_upperLip_jnt_tyPos', 'R_upperLip_jnt_tyPos'],
                neg: ['L_upperLip_jnt_tyNeg', 'R_upperLip_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_upperLip_jnt_tzPos', 'R_upperLip_jnt_tzPos'],
                neg: ['L_upperLip_jnt_tzNeg', 'R_upperLip_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_upperLip_jnt_rxNeg', 'R_upperLip_jnt_rxNeg'],
                neg: ['L_upperLip_jnt_rxPos', 'R_upperLip_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_upperLip_jnt_ryNeg', 'R_upperLip_jnt_ryPos'],
                neg: ['L_upperLip_jnt_ryPos', 'R_upperLip_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_upperLip_jnt_rzPos', 'R_upperLip_jnt_rzNeg'],
                neg: ['L_upperLip_jnt_rzNeg', 'R_upperLip_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_upperLip_jnt_sxPos', 'R_upperLip_jnt_sxPos'],
                neg: ['L_upperLip_jnt_sxNeg', 'R_upperLip_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_upperLip_jnt_syPos', 'R_upperLip_jnt_syPos'],
                neg: ['L_upperLip_jnt_syNeg', 'R_upperLip_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_upperLip_jnt_szPos', 'R_upperLip_jnt_szPos'],
                neg: ['L_upperLip_jnt_szNeg', 'R_upperLip_jnt_szNeg'],
            }],
        }],
    },
    lowerLip: {
        key: 'lowerLip',
        displayName: '下唇',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_lowerLip_jnt_txPos', 'R_lowerLip_jnt_txNeg'],
                neg: ['L_lowerLip_jnt_txNeg', 'R_lowerLip_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_lowerLip_jnt_tyPos', 'R_lowerLip_jnt_tyPos'],
                neg: ['L_lowerLip_jnt_tyNeg', 'R_lowerLip_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_lowerLip_jnt_tzPos', 'R_lowerLip_jnt_tzPos'],
                neg: ['L_lowerLip_jnt_tzNeg', 'R_lowerLip_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_lowerLip_jnt_rxNeg', 'R_lowerLip_jnt_rxNeg'],
                neg: ['L_lowerLip_jnt_rxPos', 'R_lowerLip_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_lowerLip_jnt_ryNeg', 'R_lowerLip_jnt_ryPos'],
                neg: ['L_lowerLip_jnt_ryPos', 'R_lowerLip_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_lowerLip_jnt_rzPos', 'R_lowerLip_jnt_rzNeg'],
                neg: ['L_lowerLip_jnt_rzNeg', 'R_lowerLip_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_lowerLip_jnt_sxPos', 'R_lowerLip_jnt_sxPos'],
                neg: ['L_lowerLip_jnt_sxNeg', 'R_lowerLip_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_lowerLip_jnt_syPos', 'R_lowerLip_jnt_syPos'],
                neg: ['L_lowerLip_jnt_syNeg', 'R_lowerLip_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_lowerLip_jnt_szPos', 'R_lowerLip_jnt_szPos'],
                neg: ['L_lowerLip_jnt_szNeg', 'R_lowerLip_jnt_szNeg'],
            }],
        }],
    },
    lipInner: {
        key: 'lipInner',
        displayName: '嘴角',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [{
            displayName: '宽窄',
            pos: ['L_lipInner_jnt_sxPos', 'R_lipInner_jnt_sxPos'],
            neg: ['L_lipInner_jnt_sxNeg', 'R_lipInner_jnt_sxNeg'],
        }, {
            displayName: '高度',
            pos: ['L_lipInner_jnt_syPos', 'R_lipInner_jnt_syPos'],
            neg: ['L_lipInner_jnt_syNeg', 'R_lipInner_jnt_syNeg'],
        }, {
            displayName: '倾斜',
            pos: ['L_lipInner_jnt_rzPos', 'R_lipInner_jnt_rzNeg'],
            neg: ['L_lipInner_jnt_rzNeg', 'R_lipInner_jnt_rzPos'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_lipInner_jnt_txPos', 'R_lipInner_jnt_txNeg'],
                neg: ['L_lipInner_jnt_txNeg', 'R_lipInner_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_lipInner_jnt_tyPos', 'R_lipInner_jnt_tyPos'],
                neg: ['L_lipInner_jnt_tyNeg', 'R_lipInner_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_lipInner_jnt_tzPos', 'R_lipInner_jnt_tzPos'],
                neg: ['L_lipInner_jnt_tzNeg', 'R_lipInner_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_lipInner_jnt_rxNeg', 'R_lipInner_jnt_rxNeg'],
                neg: ['L_lipInner_jnt_rxPos', 'R_lipInner_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_lipInner_jnt_ryNeg', 'R_lipInner_jnt_ryPos'],
                neg: ['L_lipInner_jnt_ryPos', 'R_lipInner_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_lipInner_jnt_rzPos', 'R_lipInner_jnt_rzNeg'],
                neg: ['L_lipInner_jnt_rzNeg', 'R_lipInner_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_lipInner_jnt_sxPos', 'R_lipInner_jnt_sxPos'],
                neg: ['L_lipInner_jnt_sxNeg', 'R_lipInner_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_lipInner_jnt_syPos', 'R_lipInner_jnt_syPos'],
                neg: ['L_lipInner_jnt_syNeg', 'R_lipInner_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_lipInner_jnt_szPos', 'R_lipInner_jnt_szPos'],
                neg: ['L_lipInner_jnt_szNeg', 'R_lipInner_jnt_szNeg'],
            }],
        }],
    },
    lipDown: {
        key: 'lipDown',
        displayName: '嘴角·小',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_lipDown_jnt_txPos', 'R_lipDown_jnt_txNeg'],
                neg: ['L_lipDown_jnt_txNeg', 'R_lipDown_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_lipDown_jnt_tyPos', 'R_lipDown_jnt_tyPos'],
                neg: ['L_lipDown_jnt_tyNeg', 'R_lipDown_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_lipDown_jnt_tzPos', 'R_lipDown_jnt_tzPos'],
                neg: ['L_lipDown_jnt_tzNeg', 'R_lipDown_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_lipDown_jnt_rxNeg', 'R_lipDown_jnt_rxNeg'],
                neg: ['L_lipDown_jnt_rxPos', 'R_lipDown_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_lipDown_jnt_ryNeg', 'R_lipDown_jnt_ryPos'],
                neg: ['L_lipDown_jnt_ryPos', 'R_lipDown_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_lipDown_jnt_rzPos', 'R_lipDown_jnt_rzNeg'],
                neg: ['L_lipDown_jnt_rzNeg', 'R_lipDown_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_lipDown_jnt_sxPos', 'R_lipDown_jnt_sxPos'],
                neg: ['L_lipDown_jnt_sxNeg', 'R_lipDown_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_lipDown_jnt_syPos', 'R_lipDown_jnt_syPos'],
                neg: ['L_lipDown_jnt_syNeg', 'R_lipDown_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_lipDown_jnt_szPos', 'R_lipDown_jnt_szPos'],
                neg: ['L_lipDown_jnt_szNeg', 'R_lipDown_jnt_szNeg'],
            }],
        }],
    },
    mouthDn: {
        key: 'mouthDn',
        displayName: '颏唇沟',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                disabled: true,
            }, {
                displayName: '上下',
                pos: ['mouthDn_jnt_tyPos'],
                neg: ['mouthDn_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['mouthDn_jnt_tzPos'],
                neg: ['mouthDn_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['mouthDn_jnt_rxNeg'],
                neg: ['mouthDn_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                disabled: true,
            }, {
                displayName: '倾斜',  // 绕 z 轴
                disabled: true,
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['mouthDn_jnt_sxPos'],
                neg: ['mouthDn_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['mouthDn_jnt_syPos'],
                neg: ['mouthDn_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['mouthDn_jnt_szPos'],
                neg: ['mouthDn_jnt_szNeg'],
            }],
        }],
    },
    nose: {
        key: 'nose',
        displayName: '鼻子',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [{
            displayName: '上下',
            pos: ['nose_jnt_tyPos'],
            neg: ['nose_jnt_tyNeg'],
        }, {
            displayName: '前后',
            pos: ['nose_jnt_tzPos'],
            neg: ['nose_jnt_tzNeg'],
        }, {
            displayName: '宽窄',
            pos: ['nose_jnt_sxPos'],
            neg: ['nose_jnt_sxNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                disabled: true,
            }, {
                displayName: '上下',
                pos: ['nose_jnt_tyPos'],
                neg: ['nose_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['nose_jnt_tzPos'],
                neg: ['nose_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['nose_jnt_rxNeg'],
                neg: ['nose_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                disabled: true,
            }, {
                displayName: '倾斜',  // 绕 z 轴
                disabled: true,
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['nose_jnt_sxPos'],
                neg: ['nose_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['nose_jnt_syPos'],
                neg: ['nose_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['nose_jnt_szPos'],
                neg: ['nose_jnt_szNeg'],
            }],
        }],
    },
    noseBridge: {
        key: 'noseBridge',
        displayName: '鼻梁',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                disabled: true,
            }, {
                displayName: '上下',
                pos: ['noseBridge_jnt_tyPos'],
                neg: ['noseBridge_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['noseBridge_jnt_tzPos'],
                neg: ['noseBridge_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['noseBridge_jnt_rxNeg'],
                neg: ['noseBridge_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                disabled: true,
            }, {
                displayName: '倾斜',  // 绕 z 轴
                disabled: true,
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['noseBridge_jnt_sxPos'],
                neg: ['noseBridge_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['noseBridge_jnt_syPos'],
                neg: ['noseBridge_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['noseBridge_jnt_szPos'],
                neg: ['noseBridge_jnt_szNeg'],
            }],
        }],
    },
    noseBridgeUp: {
        key: 'noseBridgeUp',
        displayName: '山根',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [{
            displayName: '上下',
            pos: ['noseBridgeUp_jnt_syPos'],
            neg: ['noseBridgeUp_jnt_syNeg'],
        }, {
            displayName: '宽窄',
            pos: ['noseBridgeUp_jnt_sxPos'],
            neg: ['noseBridgeUp_jnt_sxNeg'],
        }, {
            displayName: '饱满',
            pos: ['noseBridgeUp_jnt_szPos'],
            neg: ['noseBridgeUp_jnt_szNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                disabled: true,
            }, {
                displayName: '上下',
                pos: ['noseBridgeUp_jnt_tyPos'],
                neg: ['noseBridgeUp_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['noseBridgeUp_jnt_tzPos'],
                neg: ['noseBridgeUp_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['noseBridgeUp_jnt_rxNeg'],
                neg: ['noseBridgeUp_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                disabled: true,
            }, {
                displayName: '倾斜',  // 绕 z 轴
                disabled: true,
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['noseBridgeUp_jnt_sxPos'],
                neg: ['noseBridgeUp_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['noseBridgeUp_jnt_syPos'],
                neg: ['noseBridgeUp_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['noseBridgeUp_jnt_szPos'],
                neg: ['noseBridgeUp_jnt_szNeg'],
            }],
        }],
    },
    philtrum: {
        key: 'philtrum',
        displayName: '鼻翼侧',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_philtrum_jnt_txPos', 'R_philtrum_jnt_txNeg'],
                neg: ['L_philtrum_jnt_txNeg', 'R_philtrum_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_philtrum_jnt_tyPos', 'R_philtrum_jnt_tyPos'],
                neg: ['L_philtrum_jnt_tyNeg', 'R_philtrum_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_philtrum_jnt_tzPos', 'R_philtrum_jnt_tzPos'],
                neg: ['L_philtrum_jnt_tzNeg', 'R_philtrum_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_philtrum_jnt_rxNeg', 'R_philtrum_jnt_rxNeg'],
                neg: ['L_philtrum_jnt_rxPos', 'R_philtrum_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_philtrum_jnt_ryNeg', 'R_philtrum_jnt_ryPos'],
                neg: ['L_philtrum_jnt_ryPos', 'R_philtrum_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_philtrum_jnt_rzPos', 'R_philtrum_jnt_rzNeg'],
                neg: ['L_philtrum_jnt_rzNeg', 'R_philtrum_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_philtrum_jnt_sxPos', 'R_philtrum_jnt_sxPos'],
                neg: ['L_philtrum_jnt_sxNeg', 'R_philtrum_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_philtrum_jnt_syPos', 'R_philtrum_jnt_syPos'],
                neg: ['L_philtrum_jnt_syNeg', 'R_philtrum_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_philtrum_jnt_szPos', 'R_philtrum_jnt_szPos'],
                neg: ['L_philtrum_jnt_szNeg', 'R_philtrum_jnt_szNeg'],
            }],
        }],
    },
    noseWing: {
        key: 'noseWing',
        displayName: '鼻翼',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                pos: ['L_noseWing_jnt_txPos', 'R_noseWing_jnt_txNeg'],
                neg: ['L_noseWing_jnt_txNeg', 'R_noseWing_jnt_txPos'],
            }, {
                displayName: '上下',
                pos: ['L_noseWing_jnt_tyPos', 'R_noseWing_jnt_tyPos'],
                neg: ['L_noseWing_jnt_tyNeg', 'R_noseWing_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['L_noseWing_jnt_tzPos', 'R_noseWing_jnt_tzPos'],
                neg: ['L_noseWing_jnt_tzNeg', 'R_noseWing_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['L_noseWing_jnt_rxNeg', 'R_noseWing_jnt_rxNeg'],
                neg: ['L_noseWing_jnt_rxPos', 'R_noseWing_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                pos: ['L_noseWing_jnt_ryNeg', 'R_noseWing_jnt_ryPos'],
                neg: ['L_noseWing_jnt_ryPos', 'R_noseWing_jnt_ryNeg'],
            }, {
                displayName: '倾斜',  // 绕 z 轴
                pos: ['L_noseWing_jnt_rzPos', 'R_noseWing_jnt_rzNeg'],
                neg: ['L_noseWing_jnt_rzNeg', 'R_noseWing_jnt_rzPos'],
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['L_noseWing_jnt_sxPos', 'R_noseWing_jnt_sxPos'],
                neg: ['L_noseWing_jnt_sxNeg', 'R_noseWing_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['L_noseWing_jnt_syPos', 'R_noseWing_jnt_syPos'],
                neg: ['L_noseWing_jnt_syNeg', 'R_noseWing_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['L_noseWing_jnt_szPos', 'R_noseWing_jnt_szPos'],
                neg: ['L_noseWing_jnt_szNeg', 'R_noseWing_jnt_szNeg'],
            }],
        }],
    },
    noseHead: {
        key: 'noseHead',
        displayName: '鼻头',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [{
            displayName: '上下',
            pos: ['noseHead_jnt_tyPos'],
            neg: ['noseHead_jnt_tyNeg'],
        }, {
            displayName: '前后',
            pos: ['noseHead_jnt_tzPos'],
            neg: ['noseHead_jnt_tzNeg'],
        }, {
            displayName: '饱满',
            pos: ['noseHead_jnt_szPos'],
            neg: ['noseHead_jnt_szNeg'],
        }],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                disabled: true,
            }, {
                displayName: '上下',
                pos: ['noseHead_jnt_tyPos'],
                neg: ['noseHead_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['noseHead_jnt_tzPos'],
                neg: ['noseHead_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['noseHead_jnt_rxNeg'],
                neg: ['noseHead_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                disabled: true,
            }, {
                displayName: '倾斜',  // 绕 z 轴
                disabled: true,
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['noseHead_jnt_sxPos'],
                neg: ['noseHead_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['noseHead_jnt_syPos'],
                neg: ['noseHead_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['noseHead_jnt_szPos'],
                neg: ['noseHead_jnt_szNeg'],
            }],
        }],
    },
    noseBottom: {
        key: 'noseBottom',
        displayName: '鼻底',
        icon() {
            return `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`;
        },
        fundamental: [],
        advanced: [{
            displayName: '偏移',
            ops: [{
                displayName: '左右',
                disabled: true,
            }, {
                displayName: '上下',
                pos: ['noseBottom_jnt_tyPos'],
                neg: ['noseBottom_jnt_tyNeg'],
            }, {
                displayName: '前后',
                pos: ['noseBottom_jnt_tzPos'],
                neg: ['noseBottom_jnt_tzNeg'],
            }],
        }, {
            displayName: '旋转',
            ops: [{
                displayName: '上下',
                pos: ['noseBottom_jnt_rxNeg'],
                neg: ['noseBottom_jnt_rxPos'],
            }, {
                displayName: '左右',  // 绕 y 轴
                disabled: true,
            }, {
                displayName: '倾斜',  // 绕 z 轴
                disabled: true,
            }],
        }, {
            displayName: '缩放',
            ops: [{
                displayName: '宽窄',
                pos: ['noseBottom_jnt_sxPos'],
                neg: ['noseBottom_jnt_sxNeg'],
            }, {
                displayName: '高度',
                pos: ['noseBottom_jnt_syPos'],
                neg: ['noseBottom_jnt_syNeg'],
            }, {
                displayName: '饱满',
                pos: ['noseBottom_jnt_szPos'],
                neg: ['noseBottom_jnt_szNeg'],
            }],
        }],
    },
}

export function generateMeshPoints(initialState) {
    const bone_controllers = initialState.bone_controllers;
    const results = {};
    const keys = Reflect.ownKeys(meshMap);
    for (let key of keys) {
        const item = meshMap[key];
        const advancedTypes = item.advanced;
        for (let type of advancedTypes) {
            for (let op of type.ops) {
                if (op.disabled) {
                    continue;
                }
                for (let posOp of op.pos) {
                    const sig = posOp.slice(0, -3);
                    const state = bone_controllers.find((item) =>
                        item.name.startsWith(key)
                    );
                    let value = 0;
                    if (state) {
                        if (state.name.endsWith("Pos")) {
                            value = state.value * 50;
                        } else {
                            value = -state.value * 50;
                        }
                    }

                    results[sig] = value;
                }
            }
        }
    }
    return results;
}

export function generateConfigData(color_list, bundle_list) {
    const skinColor = color_list.find(
        (item) => item.name === "skin_color"
    );
    const hairColor = color_list.find(
        (item) => item.name === "hair_color"
    );
    const eyebrowColor = color_list.find(
        (item) => item.name === "eyebrow_color"
    );
    const irisColor = color_list.find(
        (item) => item.name === "iris_color"
    );
    const beardColor = color_list.find(
        (item) => item.name === "beard_color"
    );

    return [
        {
            name: "face",
            normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
            selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
            selectedColor: "#7B58FF",
            types: [
                {
                    name: "skin_color",
                    type: "color",
                    gender: ["female", "male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected: skinColor
                        ? skinColor.color.join("#")
                        : "",
                },
                {
                    name: "face",
                    type: "bundle",
                    gender: ["female", "male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    facepup: [
                        meshMap.mouth_root,
                        meshMap.forehead,
                        meshMap.ophryon,
                        meshMap.malar,
                        meshMap.cheek,
                        meshMap.cheekUp,
                        meshMap.levator,
                        meshMap.chin,
                        meshMap.chins,
                        meshMap.mandible,
                        meshMap.mandibleCape,
                    ],
                    selected:
                        bundle_list.find((item) =>
                            item.includes("face_components/face/")
                        ) || "none",
                },
                {
                    name: "hair",
                    type: "bundle",
                    gender: ["female", "male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("hair/")
                        ) || "none",
                },
                {
                    name: "hair_color",
                    type: "color",
                    gender: ["female", "male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected: hairColor
                        ? hairColor.color.join("#")
                        : "",
                },
                {
                    name: "brow",
                    type: "bundle",
                    gender: ["female", "male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    facepup: [
                        meshMap.brow,
                        meshMap.innBrow,
                        meshMap.midBrow,
                        meshMap.outBrow,
                    ],
                    selected:
                        bundle_list.find((item) =>
                            item.includes("brow/")
                        ) || "none",
                },
                {
                    name: "eyebrow_color",
                    type: "color",
                    gender: ["female", "male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected: eyebrowColor
                        ? eyebrowColor.color.join("#")
                        : "",
                },
                {
                    name: "eye",
                    type: "bundle",
                    gender: ["female", "male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    facepup: [
                        meshMap.eye,
                        meshMap.interEyeLid,
                        meshMap.outerEyeLid,
                        meshMap.upperEyeLidIn,
                        meshMap.upperEyeLid,
                        meshMap.upperEyeLidOut,
                        meshMap.lowerEyeLidIn,
                        meshMap.lowerEyeLid,
                        meshMap.lowerEyeLidOut,
                        meshMap.pupil,
                    ],
                    selected:
                        bundle_list.find((item) =>
                            item.includes("face_components/eye/")
                        ) || "none",
                },
                {
                    name: "iris_color",
                    type: "color",
                    gender: ["female", "male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected: irisColor
                        ? irisColor.color.join("#")
                        : "",
                },
                {
                    name: "eyelash",
                    type: "bundle",
                    gender: ["female", "male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("face_beauty/eyelash/")
                        ) || "none",
                },
                {
                    name: "eyeshadow",
                    type: "bundle",
                    gender: ["female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("face_beauty/eyeshadow/")
                        ) || "none",
                },
                {
                    name: "mouth",
                    type: "bundle",
                    gender: ["female", "male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    facepup: [
                        meshMap.mouth,
                        meshMap.lip,
                        meshMap.upperLip,
                        meshMap.lowerLip,
                        meshMap.lipInner,
                        meshMap.lipDown,
                        meshMap.mouthDn,
                    ],
                    selected:
                        bundle_list.find((item) =>
                            item.includes("face_components/mouth/")
                        ) || "none",
                },
                {
                    name: "lip",
                    type: "bundle",
                    gender: ["female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("face_beauty/lip/")
                        ) || "none",
                },
                {
                    name: "nose",
                    type: "bundle",
                    gender: ["female", "male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    facepup: [
                        meshMap.nose,
                        meshMap.noseBridge,
                        meshMap.noseBridgeUp,
                        meshMap.philtrum,
                        meshMap.noseWing,
                        meshMap.noseHead,
                        meshMap.noseBottom,
                    ],
                    selected:
                        bundle_list.find((item) =>
                            item.includes("face_components/nose/")
                        ) || "none",
                },
                {
                    name: "beard",
                    type: "bundle",
                    gender: ["male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("beard/")
                        ) || "none",
                },
                {
                    name: "beard_color",
                    type: "color",
                    gender: ["male"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected: beardColor
                        ? beardColor.color.join("#")
                        : "",
                },
                {
                    name: "facepainting",
                    type: "bundle",
                    gender: ["female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("face_beauty/facepainting/")
                        ) || "none",
                },
            ],
        },
        {
            name: "clothes",
            normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
            selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
            selectedColor: "#3EBAE4",
            types: [
                {
                    name: "shirt",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("upper_inner/shirt/")
                        ) || "none",
                },
                {
                    name: "coat",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("upper_outer/coat/")
                        ) || "none",
                },
                {
                    name: "hoodie",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("upper_inner/hoodie/")
                        ) || "none",
                },
                {
                    name: "suit",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("cloth/suit/")
                        ) || "none",
                },
                {
                    name: "cloth",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("cloth/cloth/")
                        ) || "none",
                },
            ],
        },
        {
            name: "trousers",
            normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
            selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
            selectedColor: "#F68249",
            types: [
                {
                    name: "shorts",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("lower_inner/shorts/")
                        ) || "none",
                },
                {
                    name: "trousers",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("lower_inner/trousers/")
                        ) || "none",
                },
                {
                    name: "skirt",
                    type: "bundle",
                    gender: ["female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("lower_inner/skirt/")
                        ) || "none",
                },
            ],
        },
        {
            name: "shoes",
            normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
            selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
            selectedColor: "#F8AF49",
            types: [
                {
                    name: "shoes",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("cloth/shoes/")
                        ) || "none",
                },
            ],
        },
        {
            name: "accessories",
            normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
            selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
            selectedColor: "#69DBBA",
            types: [
                {
                    name: "glasses",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("glasses/")
                        ) || "none",
                },
                {
                    name: "earring",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("earring/")
                        ) || "none",
                },
                {
                    name: "necklace",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("necklace/")
                        ) || "none",
                },
                {
                    name: "hat",
                    type: "bundle",
                    gender: ["male", "female"],
                    normalIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selectedIcon: `${VITE_GLOB_IMG_URL}/slices/<EMAIL>`,
                    selected:
                        bundle_list.find((item) =>
                            item.includes("hat/")
                        ) || "none",
                },
            ],
        },
    ];
}

function isPartInArray(key, strArray) {
    for (let str of strArray) {
        if (key.endsWith(str)) {
            return true;
        }
    }
    return false;
}

export function generateBodyPitch(initialState) {
    const bone_controllers = initialState.bone_controllers;
    const results = {};
    const keys = Reflect.ownKeys(bodyPitch);
    const posKeywords = ['long', 'thick', 'large', 'wide', 'fat', 'high'];
    for (let key of keys) {
        const item = bodyPitch[key];
        const fundamentals = item.fundamental;
        for (let type of fundamentals) {
            for (let pos of type.pos) {
                const lastIndex = pos.lastIndexOf('_');
                const opKey = pos.slice(0, lastIndex);

                const state = bone_controllers.find((item => item.name.startsWith(opKey)));
                let value = 0;
                if (state) {
                    const isPos = isPartInArray(state.name, posKeywords);
                    if (isPos && state.value) {
                        value = state.value * 50;
                    }
                    if (!isPos && state.value) {
                        value = -state.value * 50;
                    }
                }

                results[opKey] = value;
            }
        }
    }
    return results;
}

export const COLORS = {
    skin_color: [
        [245, 202, 168, 255],
        [240, 185, 147, 255],
        [230, 180, 140, 255],
        [219, 166, 126, 255],
        [182, 128, 92, 255],
        [144, 100, 65, 255],
    ],
    hair_color: [
        [115, 70, 53, 255],
        [55, 50, 52, 255],
        [89, 60, 44, 255],
        [109, 81, 64, 255],
        [120, 69, 50, 255],
        [237, 200, 143, 255],
        [192, 165, 135, 255],
        [167, 167, 165, 255],
        [173, 84, 76, 255],
        [195, 132, 81, 255],
        [180, 106, 69, 255],
        [232, 199, 114, 255],
        [186, 112, 150, 255],
        [123, 105, 167, 255],
        [91, 137, 200, 255],
        [143, 184, 110, 255],
    ],
    eyebrow_color: [
        [35, 30, 32, 255],
        [115, 70, 53, 255],
        [55, 50, 52, 255],
        [89, 60, 44, 255],
        [109, 81, 64, 255],
        [120, 69, 50, 255],
        [237, 200, 143, 255],
        [192, 165, 135, 255],
        [167, 167, 165, 255],
        [173, 84, 76, 255],
        [195, 132, 81, 255],
        [180, 106, 69, 255],
        [232, 199, 114, 255],
        [186, 112, 150, 255],
        [123, 105, 167, 255],
        [91, 137, 200, 255],
        [143, 184, 110, 255],
    ],
    iris_color: [
        [121, 47, 16, 255],
        [82, 62, 44, 255],
        [105, 66, 45, 255],
        [161, 62, 62, 255],
        [122, 102, 26, 255],
        [72, 103, 123, 255],
        [79, 85, 88, 255],
        [102, 49, 33, 255],
        [121, 79, 110, 255],
        [92, 134, 71, 255],
    ],
    beard_color: [
        [118, 62, 38, 255],
        [115, 70, 53, 255],
        [55, 50, 52, 255],
        [89, 60, 44, 255],
        [109, 81, 64, 255],
        [120, 69, 50, 255],
        [237, 200, 143, 255],
        [192, 165, 135, 255],
        [167, 167, 165, 255],
        [173, 84, 76, 255],
        [195, 132, 81, 255],
        [180, 106, 69, 255],
        [232, 199, 114, 255],
        [186, 112, 150, 255],
        [123, 105, 167, 255],
        [91, 137, 200, 255],
        [143, 184, 110, 255],
    ],
    makeup_lip_color: [
        [219, 149, 135, 255],
        [218, 90, 85, 255],
        [203, 57, 56, 255],
        [70, 63, 68, 255],
        [163, 202, 124, 255],
        [115, 171, 226, 255],
        [156, 139, 202, 255],
        [213, 144, 183, 255],
        [244, 212, 127, 255],
        [206, 158, 116, 255],
        [215, 153, 97, 255],
        [192, 106, 88, 255],
        [197, 197, 195, 255],
        [220, 203, 179, 255],
        [248, 222, 174, 255],
        [137, 109, 90, 255],
        [108, 79, 59, 255],
        [168, 121, 95, 255],
    ]
}

export { headBody5 } from './data/facepup_config_baseHeadBody'
export { headBody6 } from './data/facepup_config_sixHeadBody'
export { headBody7 } from './data/facepup_config_sevenHeadBody'
export { headBody8 } from './data/facepup_config_eightHeadBody'
export { headBody9 } from './data/facepup_config_nineHeadBody'