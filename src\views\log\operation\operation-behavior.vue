<template>
  <el-card class="box-card">
    <div class="flex">
      <el-form :inline="true" :model="queryParams" class="flex flex-nowrap">
        <el-form-item label="用户:">
          <el-input v-model.trim="queryParams.name" placeholder="请输入操作人" clearable />
        </el-form-item>
        <el-form-item label="操作模块:">
          <el-select v-model="queryParams.module_name" placeholder="请选择操作模块" clearable>
            <el-option label="全部" value="" />
            <el-option
              :value="option.value"
              :label="option.value"
              v-for="option in moduleNames"
              :key="option.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="操作信息">
          <el-input v-model.trim="queryParams.type" placeholder="请输入操作信息" clearable />
        </el-form-item>
        <el-form-item label="操作时间">
          <InnerMonthPicker
            v-model="queryParams.dateRange"
          />
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <template v-for="(item, index) in tableColumns">
        <el-table-column
          v-if="item.show"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          show-overflow-tooltip
        />
      </template>
    </el-table>
    <el-pagination
      class="flex my-4 justify-end"
      background
      layout="total, prev, pager, next, sizes, jumper"
      v-model:page-size="queryParams.limit"
      v-model:current-page="queryParams.page"
      :total="totalRef"
    />
  </el-card>
</template>

<script lang="ts" setup>
  import { getOperationBehaviorList, getModelNames } from '@/api/system/log';
  import { useDebounceFn } from '@vueuse/core';
  import { onMounted, reactive, ref, watch } from 'vue';
  import { InnerMonthPicker } from '@/components/InnerMonthPicker';
  import { format } from 'date-fns';
  const tableColumns = [
    { prop: 'user_name', label: '用户', show: true },
    { prop: 'module_name', label: '操作模块', show: true },
    { prop: 'type', label: '操作信息', show: true },
    { prop: 'times', label: '次数', width: 120, show: true },
  ];

  const moduleNames = ref<any>([]);

  const queryParams = reactive({
    name: '',
    type: '',
    module_name: '',
    limit: 10,
    page: 1,
    dateRange: [
      format(new Date(new Date().setDate(1)), 'yyyy-MM-dd'),
      format(new Date(), 'yyyy-MM-dd'),
    ] as any,
  });
  const totalRef = ref(0);
  const tableData = ref([]);

  const generateParamas = () => {
    const { dateRange, ...other } = queryParams;
    const params: any = { ...other };
    if (dateRange && Array.isArray(dateRange)) {
      const [start, end] = dateRange;
      if (start) {
        params.create_start = start;
      }
      if (end) {
        params.create_end = end;
      }
    }

    return params;
  };

  const fetchModuleName = async () => {
    const {
      data: { rows = [] },
    } = await getModelNames({});
    moduleNames.value = rows;
  };

  const fetchLogList = useDebounceFn(async () => {
    const params = generateParamas();
    const {
      data: { rows: list, count },
    } = await getOperationBehaviorList(params);
    totalRef.value = count;
    tableData.value = list;
  }, 500);

  onMounted(async () => {
    await fetchModuleName();
    await fetchLogList();
  });

  watch(
    [() => queryParams, () => queryParams.dateRange],
    async () => {
      await fetchLogList();
    },
    { deep: true }
  );
</script>
