name: Build

on:
  push:
    branches: [ master, dev ]
  pull_request:
    branches: [ master, dev ]

jobs:
  build:

    runs-on: ${{ matrix.os-type }}

    strategy:
      matrix:
        node-version: [18.x, 20.x]
        os-type: [windows-latest, macos-latest, ubuntu-latest]

    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
    - run: npm install
    - run: npm run create_dist
