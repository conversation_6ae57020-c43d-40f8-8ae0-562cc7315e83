import { http } from '@/utils/http/axios';

//获取table
export function getAssetList(params) {
  return http.request({
    url: '/business/asset',
    method: 'get',
    params,
  });
}


export function getSyncAssetList(params) {
  return http.request({
    url: '/business/ability-provider/asset/list',
    method: 'get',
    params,
  });
}

export function getSyncAssetDetail(params: {
  provider_id: string,
  filesystemposition: string
}) {
  return http.request({
    url: '/business/ability-provider/asset/one',
    method: 'get',
    params,
  });
}

export function getAuditAssetList(params) {
  return http.request({
    url: '/business/asset/audit/list',
    method: 'get',
    params,
  });
}

export function getGuestAssetList(params) {
  return http.request({
    url: '/business/asset/audit/listGuest',
    method: 'get',
    params,
  });
}

//获取素材类型
export function getAssetTypeList() {
  return http.request({
    url: '/business/asset/type/list',
    method: 'get',
  });
}

// 新增素材
export function addAsset(params) {
  return http.request({
    url: '/business/asset',
    method: 'post',
    params,
  });
}

// 删除素材
export function deleteAsset(id) {
  return http.request({
    url: `/business/asset/${id}/delete`,
    method: 'post',
  });
}

// 批量删除素材
export function batchDeleteAsset(params) {
  return http.request({
    url: '/business/asset/batch/deleteBatch',
    method: 'post',
    params,
  });
}
// 获取素材详情
export function getAssetDetail(id) {
  return http.request({
    url: `/business/asset/${id}`,
    method: 'get',
  });
}

// 审核员获取素材详情
export function getAuditAssetDetail(id) {
  return http.request({
    url: `/business/asset/${id}/audit`,
    method: 'get',
  });
}

// 编辑素材
export function editAsset(id, params) {
  return http.request({
    url: `/business/asset/${id}`,
    method: 'POST',
    params,
  });
}

// 素材申请发布
export function applyAssetPublish(id) {
  return http.request({
    url: `/business/asset/${id}/publish`,
    method: 'post',
  });
}

export function auditAssetPublish(id, params) {
  return http.request({
    url: `/business/asset/${id}/audit`,
    method: 'post',
    params,
  });
}

// 撤回素材申请
export function cancelAssetPublish(id) {
  return http.request({
    url: `/business/asset/${id}/revoke`,
    method: 'post',
  });
}

// 审核员下架素材
export function revisingAssetPublish(id) {
  return http.request({
    url: `/business/asset/${id}/revising`,
    method: 'post',
  });
}
