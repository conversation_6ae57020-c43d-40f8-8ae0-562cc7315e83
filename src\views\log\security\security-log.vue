<template>
  <el-card class="box-card">
    <div class="flex">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline flex-1">
        <el-form-item label="操作人:">
          <el-input v-model.trim="queryParams.name" placeholder="请输入操作人" clearable />
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model.trim="queryParams.success" placeholder="请选择状态" clearable>
            <el-option value="" label="全部" />
            <el-option value="1" label="成功" />
            <el-option value="0" label="失败" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作时间:">
          <el-date-picker
            value-format="YYYY-MM-DD"
            clearable
            v-model="queryParams.dateRange"
            type="daterange"
            :disabled-date="disabledDate"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
      </el-form>
      <div>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </div>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <template v-for="(item, index) in tableColumns">
        <el-table-column
          v-if="item.show"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :formatter="item.formatter"
          show-overflow-tooltip
        />
      </template>
      <el-table-column prop="ua" min-width="100" label="登录方式">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-tooltip trigger="hover" placement="top">
              <template #content>
                <span v-text="row.ua" class="cursor-pointer"></span>
              </template>
              <template #default>
                <span v-text="row.ua" class="cursor-pointer block truncate"></span>
              </template>
            </el-tooltip>
            <el-icon class="ml-1" @click="handleCopy(row)">
              <DocumentCopy class="cursor-copy" />
            </el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="page" label="操作页面" />
      <el-table-column prop="user.name" label="操作人" />
      <el-table-column prop="info" label="操作信息" />
      <el-table-column prop="success" label="操作结果" width="100" :formatter="formatStatus" />
    </el-table>
    <el-pagination
      class="flex my-4 justify-end"
      background
      layout="total, prev, pager, next, sizes, jumper"
      v-model:page-size="queryParams.limit"
      v-model:current-page="queryParams.page"
      :total="totalRef"
    />
  </el-card>
</template>

<script lang="ts" setup>
  import { getSecurityLog } from '@/api/system/log';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { DocumentCopy } from '@element-plus/icons-vue';
  import { useClipboard, useDebounceFn, useThrottleFn } from '@vueuse/core';
  import { ElMessage } from 'element-plus';
  import { onMounted, reactive, ref, watch } from 'vue';
  import { useExport } from '@/views/hooks';
  import { disabledDate } from '@/utils/dateUtil';
  import { formatStatus } from '../format-column';

  const { exportFile } = useExport();

  const queryParams = reactive({
    name: '',
    success: '',
    pagex: '',
    info: '',
    limit: 10,
    page: 1,
    dateRange: [] as any,
  });

  const source = ref('');
  const { copy, isSupported } = useClipboard({ source });

  const handleCopy = useThrottleFn((row) => {
    source.value = row.ua;
    if (!isSupported) {
      ElMessage.warning('您的浏览器不支持复制!');
      return;
    }
    copy(source.value);
    ElMessage.success('复制成功!');
    source.value = '';
  }, 3000);

  const totalRef = ref(0);
  const tableData = ref([]);

  const generateParamas = () => {
    const { dateRange, success, ...other } = queryParams;
    const params: any = { ...other };

    if (dateRange && Array.isArray(dateRange)) {
      const [start, end] = dateRange;
      if (start) {
        params.create_start = start;
      }
      if (end) {
        params.create_end = end;
      }
    }
    if (success !== '') {
      params.success = success;
    }

    return params;
  };

  const fetchLogList = useDebounceFn(async () => {
    const params = generateParamas();
    const {
      data: { rows: list, count },
    } = await getSecurityLog(params);
    totalRef.value = count;
    tableData.value = list;
  }, 500);

  onMounted(async () => {
    await fetchLogList();
  });

  const dateTimeFormatter = (_row, _column, cellValue) => {
    return formatToDateTime(cellValue);
  };

  const tableColumns = [
    { prop: 'updated_at', label: '时间', show: true, formatter: dateTimeFormatter },
    { prop: 'ip', label: 'IP地址', show: true },
  ];

  watch(
    () => queryParams,
    async () => {
      await fetchLogList();
    },
    { deep: true }
  );

  //导出
  const handleExport = () => {
    const params = generateParamas();
    exportFile('/business/export-query/system-log/export', params);
  };
</script>
