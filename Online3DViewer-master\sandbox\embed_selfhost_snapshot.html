<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no">

	<title>Online 3D Viewer</title>

	<script type="text/javascript" src="../build/engine_dev/o3dv.min.js"></script>

    <script type='text/javascript'>
        let embeddedViewers = null;

        window.addEventListener ('load', () => {
            embeddedViewers = OV.Init3DViewerElements ();
		});

        function CreateSnapshot ()
        {
            for (let embeddedViewer of embeddedViewers) {
                let viewer = embeddedViewer.GetViewer ();
                let img = document.createElement ('img');
                img.src = viewer.GetImageAsDataUrl (400, 300, false);
                document.body.appendChild (img);
            }
        }
    </script>

    <style>
        div.online_3d_viewer
        {
            width: 600px;
            height: 300px;
            border: 1px solid #cccccc;
        }

        img
        {
            border: 1px solid #cccccc;
            margin: 3px;
        }
    </style>
</head>

<body>
    <div class="online_3d_viewer"
        model="../../test/testfiles/obj/hundred_cubes.obj,../../test/testfiles/obj/hundred_cubes.mtl">
    </div>
    <div>
        <input type="button" onclick="CreateSnapshot ();" value="snapshot"></input>
    </div>
</body>

</html>
