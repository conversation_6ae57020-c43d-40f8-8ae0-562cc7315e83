<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no">

	<title>Online 3D Viewer</title>

	<script type="text/javascript" src="../build/engine_dev/o3dv.min.js"></script>

    <script type='text/javascript'>
        window.addEventListener ('load', () => {
            OV.Init3DViewerElements ();
        });
    </script>

    <style>
        div.online_3d_viewer
        {
            float: left;
            border: 1px solid #eeeeee;
            margin: 0px 4px 4px 0px;
        }
    </style>
</head>

<body>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../test/testfiles/gltf/BoxVertexColors/glTF-Binary/BoxVertexColors.glb"
        environmentmap="../website/assets/envmaps/fishermans_bastion/posx.jpg,../website/assets/envmaps/fishermans_bastion/negx.jpg,../website/assets/envmaps/fishermans_bastion/posy.jpg,../website/assets/envmaps/fishermans_bastion/negy.jpg,../website/assets/envmaps/fishermans_bastion/posz.jpg,../website/assets/envmaps/fishermans_bastion/negz.jpg">
    </div>
    <div class="online_3d_viewer"
		style="width: 360px; height: 240px;"
        model="../../test/testfiles/gltf/BoxVertexColors/glTF-Binary/BoxVertexColors.glb"
        environmentmap="../website/assets/envmaps/fishermans_bastion/posx.jpg,../website/assets/envmaps/fishermans_bastion/negx.jpg,../website/assets/envmaps/fishermans_bastion/posy.jpg,../website/assets/envmaps/fishermans_bastion/negy.jpg,../website/assets/envmaps/fishermans_bastion/posz.jpg,../website/assets/envmaps/fishermans_bastion/negz.jpg"
        environmentmapbg="true">
    </div>
</body>

</html>
