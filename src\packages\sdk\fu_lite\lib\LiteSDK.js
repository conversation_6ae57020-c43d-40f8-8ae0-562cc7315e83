import { Wasm<PERSON>el<PERSON> } from "./wasmHelper";

export class LiteSDK extends WasmHelper {
    _langType = 0 // 0 - CN,  1 - EN
    _fps = 25;
    get fps() { return this._fps; }
    set fps(val) {
        this._fps = val;
    }

    get _module() {
        return this.wasmCtx.module;
    }

    async initialize(options) {
        await this.wasmCtx.LoadWasm();
        const [decoder, bs_config] = await Promise.all([this.LoadAssets(options.decoderUrl), this.LoadAssets(options.BsConfig)]);
        this._liteHandle = this._module.Setup(
            decoder.ptr,
            decoder.size,
            bs_config.ptr,
            bs_config.size,
            this._langType
        );

        this._module._free(decoder.ptr);
        this._module._free(bs_config.ptr);
        if (!this._liteHandle) {
            throw new Error("Failed to setup FU Lite SDK")
        }
        this.length = this.getCoeffSize();
    }

    parseExpressionData(ttsTimestamp, isTextStamp = true) {
        this._module.ParseAudioPhonemes(
            this._liteHandle,
            ttsTimestamp,
            isTextStamp ? 1 : 0,
            this._langType,
            1 / this.fps,
            0
        );
    }

    getCoeffSize() {
        return this._module.GetCoeffSize(this._liteHandle);
    }

    getCurExpression(time) {
        const exp = this._module.GetCurExpression(this._liteHandle, time);
        const ret = new Float32Array(this.length)
        for (let i = 0; i < this.length; i++) {
            ret[i] = exp.get(i);
        }
        return ret;
    }

    clearCache() {
        this._module.ClearCacheBS(this._liteHandle);
    }
}
