import { CFloat3, EFuAvatarOperateComponentState, FUAvatarComponent, FUItem, FU_RENDER_BOOL, IAvatarConfig } from '@/packages/types'

export interface FUAvatar {
  avatarConfig: IAvatarConfig
  /**
   * @brief 设置配置规则
   */
  setConfig(ptr: any, size: any): void

  /**
   * @brief 增加资源列表
   * @param itmLst json字符串, 比如从item_list.json读取到的文本内容.
   */
  addItemList(itmLst: string): void

  /**
   * @brief 清除资源列表
   */
  clearItemList(): void

  /**
   * @brief 添加由相对路径指定的FUAvatarComponent, 比如帽子, 鞋子等. 返回此次更新后被移除的组件列表
   */
  addComponentsAsync(paths_utf8: string[], paths_count: number): Promise<string[]>
  addComponentWithMofifyTest(paths_utf8: string[]): Map<any>

  getComponent(paths_utf8: string): paths_utf8
  /**
   * @brief 删除由相对路径指定的FUAvatarComponent, 比如帽子, 鞋子等.
   */
  removeComponent(paths_utf8: string): void

  /**
   * @brief 准备相关路径数据, 比如下载文件, 创建bundle.
   */
  prepareComponentsAsync(paths_utf8: string[], paths_count: number): Promise<void>

  /**
   * @brief 指定某个具体的FUAvatarComponent是否开启/关闭指定的状态
   * @param opCmd 可见性/cast shadow/receive shadow
   * @param  enable 开启/关闭
   */
  enableComponent(comp: FUAvatarComponent, opCmd: EFuAvatarOperateComponentState, enable: FU_RENDER_BOOL): void

  /**
   * @brief 指定所有的FUAvatarComponent是否开启/关闭指定的状态
   */
  enableAllComponents(opCmd: EFuAvatarOperateComponentState, enable: FU_RENDER_BOOL): void

  /**
   * @brief 设置/获取 颜色值
   */
  setColor(part_name: string, color: string): void;
  getColor(part_name: string): [number, number, number, number];

  /**
   * @brief 设置/获取 捏脸值
   */
  setBoneTransform(name: string, value: number): void;
  getBoneTransform(name: string): number;

  /**
   * @brief 是否禁用高跟鞋
   */
  enableHighHeelBoneTransform(enable: FU_RENDER_BOOL): void

  /**
   * @brief 获取/设置 位置
   */
  getPosition(): CFloat3
  setPosition(pos: any): void


  /**
   * @brief 设置带有渐变时间的位置
   * @param pos 最终位置
   * @param duration_time 渐变时间
   */
  setInterpolatePosition(pos: CFloat3, duration_time: number): void

  /**
   * @brief 设置位置变化
   * @param pos 最终位置与当前位置的位置变化
   */
  setDeltaPosition(pos: CFloat3): void

  /**
   * @brief 设置带有渐变时间的位置变化
   * @param pos 最终位置与当前位置的位置变化
   * @param duration_time 渐变时间
   */
  setInterpolateDeltaPosition(pos: CFloat3, duration_time: number): void

  /**
   * @brief 获取/设置 旋转
   */
  getRotate(): number[];
  setRotate(euler_angle_degree: CFloat3[number]): void;

  /**
   * @brief 设置带有渐变时间的旋转
   * @param pos 最终旋转
   * @param duration_time 渐变时间
   */
  setInterpolateRotation(euler_angle_degree: CFloat3, duration_time: number): void

  /**
   * @brief 设置旋转变化
   * @param pos 最终旋转与当前旋转的旋转变化
   */
  setDeltaRotation(axis: CFloat3, degree: number): void
  /**
   * @brief 设置带有渐变时间的旋转变化
   * @param pos 最终旋转与当前旋转的旋转变化
   * @param duration_time 渐变时间
   */
  setInterpolateDeltaRotation(axis: CFloat3, degree: number, duration_time: number): void

  /**
   * @brief 获取/设置 缩放
   */
  getScale(): CFloat3
  setScale(scl: CFloat3): void

  /**
   * @brief 设置带有渐变时间的缩放
   * @param pos 最终缩放
   * @param duration_time 渐变时间
   */
  setInterpolateScale(scl: CFloat3, duration_time: number): void

  /**
  * @brief 设置缩放变化
  * @param pos 最终缩放与当前缩放的缩放变化
  */
  setDeltaScale(scl: CFloat3): void
  /**
   * @brief 设置带有渐变时间的缩放变化
   * @param pos 最终缩放与当前缩放的缩放变化
   * @param duration_time 渐变时间
   */
  setInterpolateDeltaScale(scl: CFloat3, duration_time: number): void

  /**
   * @brief 设置默认状态下的逻辑动画
   * @param path_utf8  bundle文件路径, 比如GAssets/animation/AsiaMale/common/ani_ptag_amale_huxi.bundle.
   */
  setDefaultStateAnimationAsync(anim_path_utf8: string, isLoop: boolean): Promise<void>
  setDefaultStateAnimationSync(path: string): any
  /**
   * @brief 开始/暂停/停止/重置动画
   */
  startAnimation(): void
  pauseAnimation(): void
  stopAnimation(): void
  resetAnimation(): void

  /**
   * @brief 设置动画系统蓝图
   * @param data webassembly 中的数据内容地址
   * @param dataSize 数据长度
   */
  setAnimGraph(data: number, dataSize: number): void

  /**
   * @brief 设置/获取 是否启用默认动画系统蓝图
   */
  useDefaultAnimationGraph(use: FU_RENDER_BOOL): void
  isUseDefaultAnimationGraph(): FU_RENDER_BOOL

  /**
   * @brief 设置动画逻辑系统数据
   * @param data webassembly 中的数据内容地址
   * @param dataSize 数据长度
   */
  setAnimLogic(data: number, dataSize: number): void

  /**
   * @brief 设置/获取 是否启用默认动画逻辑
   */
  useDefaultAnimationLogic(use: FU_RENDER_BOOL): void
  isUseDefaultAnimationLogic(): FU_RENDER_BOOL

  /**
   * @brief 设置动画blendshape数组
  */
  setBlendShape(anim_graph_node_name: string, mesh_name: string, bs_data_ptr: number, bs_size: number): void
  blendMouthShape(arg1: any, arg2: any): void
  /**
   * @brief 高级动画参数设置
  */
  setAnimationGraphParamBool(anim_graph_param_name: string, value: FU_RENDER_BOOL): void
  setAnimationGraphParamInt(anim_graph_param_name: string, value: number): void
  setAnimationGraphParamFloat(anim_graph_param_name: string, value: number): void
  setAnimationGraphParamString(anim_graph_param_name: string, value: string): void

  /**
   * @brief 高级动画参数获取
  */
  getAnimationGraphParamBool(anim_graph_param_name: string): FU_RENDER_BOOL
  getAnimationGraphParamInt(anim_graph_param_name: string): number
  getAnimationGraphParamFloat(anim_graph_param_name: string): number
  getAnimationGraphParamString(anim_graph_param_name: string): string;

  /**
   * setDynamicBoneAllEnable
   */
  setDynamicBoneAllEnable(bool: FU_RENDER_BOOL): void
  setConfigWithData(arg1: any, arg2: any): void
  setAnimation(path: string, node: string, flag: boolean): void

  /**
   * talk something
   */
  setState(state: TaskState): void
  getJson(): string

  /**
   * addItem
   */
  addItem(avatarItem: any): any

  setAllCastShadow(bool: boolean): void
}