<template>
  <div>
    <n-card :bordered="false" class="mt-4 proCard">
      <BasicTable
        :columns="columns"
        :request="loadDataTable"
        :row-key="(row) => row.id"
        ref="actionRef"
        :actionColumn="actionColumn"
      >
        <template #tableTitle>
          <el-form :inline="true">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="params.createAt"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
                type="daterange"
                placeholder="1"
                :disabled-date="
                  (date) => {
                    return date.getTime() > new Date().getTime();
                  }
                "
              />
            </el-form-item>
            <el-form-item label="">
              <el-input
                clearable
                v-model="params.keyword"
                type="daterange"
                placeholder="请输入角色名或描述"
                :prefix-icon="Search"
              />
            </el-form-item>
          </el-form>
        </template>
        <template #toolbar>
          <n-button type="primary" @click="addTable()">
            <template #icon>
              <n-icon>
                <PlusOutlined />
              </n-icon>
            </template>
            添加角色
          </n-button>
        </template>
        <template #action>
          <TableAction />
        </template>
      </BasicTable>
    </n-card>

    <n-modal v-model:show="showModal" :show-icon="false" preset="dialog" :title="editRoleTitle">
      <div class="py-3 menu-list">
        <n-tree
          block-line
          cascade
          checkable
          :virtual-scroll="true"
          :data="treeData"
          :expandedKeys="expandedKeys"
          :checked-keys="checkedKeys"
          style="max-height: 950px; overflow: hidden"
          @update:checked-keys="checkedTree"
          @update:expanded-keys="onExpandedKeys"
        />
      </div>
      <template #action>
        <n-space>
          <n-button type="info" ghost icon-placement="left" @click="packHandle">
            全部{{ expandedKeys.length ? '收起' : '展开' }}
          </n-button>
          <n-button type="primary" :loading="formBtnLoading" @click="confirmForm">提交</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, unref, h, onMounted } from 'vue';
  import { BasicTable, TableAction } from '../../../components/Table';
  import { getRoleList, changeRoleStatus, deleteRole } from '../../../api/system/role';
  import { PlusOutlined } from '@vicons/antd';
  import { Search } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';
  import moment from 'moment';
  import { PERMISSION_KEYS } from '@/store/modules/user';
  import { watch } from 'vue';
  import { ElMessageBox, ElSwitch, ElMessage } from 'element-plus';

  const router = useRouter();
  const actionRef = ref();

  const showModal = ref(false);
  const formBtnLoading = ref(false);
  const editRoleTitle = ref('');
  const treeData = ref([]);
  const expandedKeys = ref([]);
  const checkedKeys = ref(['console', 'step-form']);

  const params = reactive({
    keyword: '',
    createAt: [null, null],
  });

  const columns = [
    {
      title: '角色名称',
      key: 'name',
    },
    {
      title: '角色描述',
      key: 'description',
    },
    {
      title: '角色人数',
      key: 'user_count',
    },
    {
      title: '状态',
      key: 'status',
      render(row) {
        return h(ElSwitch, {
          disabled: row.key === 'super',
          modelValue: row.status === 1,
          beforeChange() {
            if (row.status === 2) {
              changeStatus(row);
              return true;
            } else {
              return new Promise(async (_) => {
                const action = await ElMessageBox.confirm(
                  '禁用生效后，该角色将不能登录系统，是否要禁用此角色？',
                  '操作确认'
                );
                if (action == 'confirm') {
                  changeStatus(row);
                } else {
                  return false;
                }
              });
            }
          },
        });
      },
    },
    {
      title: '创建人',
      key: 'user_name',
    },
    {
      title: '创建时间',
      key: 'created_at',
      render(row) {
        return moment(row.created_at).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '更新时间',
      key: 'updated_at',
      render(row) {
        return moment(row.updated_at).format('YYYY-MM-DD HH:mm:ss');
      },
    },
  ];
  const actionColumn = reactive({
    width: 250,
    title: '操作',
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction, {
        style: 'button',
        actions: [
          {
            label: '编辑',
            disabled: record.key === 'super',
            onClick: handleEdit.bind(null, record),
            ifShow: () => {
              return true;
            },
            auth: [PERMISSION_KEYS.super.role_edit],
          },
          {
            label: '删除',
            disabled: record.key === 'super' || record.status === 1,
            auth: [PERMISSION_KEYS.super.role_remove],
            type: 'error',
            popConfirm: {
              title: '确定要删除吗？',
              confirm: handleDelete.bind(null, record),
            },
          },
        ],
      });
    },
  });

  function addTable() {
    router.push({ path: '/role/add' });
  }

  watch(
    params,
    () => {
      reloadTable();
    },
    { deep: true }
  );

  const loadDataTable = async (res: any) => {
    let _params = {
      ...unref(params),
      ...res,
      create_start: params.createAt?.[0],
      create_end: params.createAt?.[1],
    };
    const data = await getRoleList(_params);
    if (data.code === 0) {
      return data.data;
    }
    return;
  };

  function reloadTable() {
    actionRef.value.updatePage(1);
  }

  function confirmForm(e: any) {
    e.preventDefault();
    formBtnLoading.value = true;
    setTimeout(() => {
      showModal.value = false;
      ElMessage({
        message: '提交成功!',
        type: 'success',
      });
      reloadTable();
      formBtnLoading.value = false;
    }, 200);
  }

  function handleEdit(record) {
    router.push({ name: 'role-edit', query: { id: record.id } });
  }

  async function handleDelete(record) {
    await deleteRole(record.id);
    ElMessage({
      message: '删除成功!',
      type: 'success',
    });
    reloadTable();
  }

  function checkedTree(keys) {
    checkedKeys.value = [checkedKeys.value, ...keys];
  }

  function onExpandedKeys(keys) {
    expandedKeys.value = keys;
  }

  function packHandle() {
    if (expandedKeys.value.length) {
      expandedKeys.value = [];
    } else {
      expandedKeys.value = treeData.value.map((item: any) => item.key) as [];
    }
  }

  async function changeStatus(record) {
    try {
      await changeRoleStatus(record.id, {
        status: record.status === 1 ? 2 : 1,
      });
      ElMessage({
        message: record.status === 1 ? '禁用成功' : '启用成功',
        type: 'success',
      });
      reloadTable();
    } catch (e) {
      console.error(e);
    }
  }

  onMounted(async () => {});
</script>

<style lang="less" scoped></style>
