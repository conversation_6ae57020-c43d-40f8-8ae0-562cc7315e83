<template>
  <el-dialog
    width="60%"
    v-model="visible"
    destroy-on-close
    modal-class="modal-table"
    @close="onCloseModal"
    :title="getBindValue.title"
  >
    <template #default>
      <el-card class="mt-4">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column label="合作伙伴" prop="provider_title" />
          <el-table-column label="风格名称" prop="style_title" />
          <el-table-column label="上架数量" prop="added_asset_snapshot" :formatter="getCount" />
          <el-table-column label="下架数量" prop="deleted_asset_snapshot" :formatter="getCount" />
          <!-- <el-table-column label="同步状态" prop="async_status" /> -->
          <el-table-column label="同步结果" prop="message" />
          <el-table-column
            label="开始时间"
            prop="createdAt"
            width="160"
            :formatter="dateTimeFormatter"
          />
          <el-table-column
            label="完成时间"
            width="160"
            prop="updatedAt"
            :formatter="formateFinishedDateTime"
          />
        </el-table>
        <el-pagination
          class="flex my-4 justify-end"
          background
          v-model:page-size="queryParams.limit"
          v-model:current-page="queryParams.page"
          :pager-count="5"
          @size-change="handlePageSizeChange"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalRef"
        />
      </el-card>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { getSyncActionList, getTemplateSyncActionList } from '@/api/operator';
  import { watch } from 'vue';
  import { computed, getCurrentInstance, reactive, unref, useAttrs, ref } from 'vue';
  import { deepMerge } from '@/utils';
  import { dateTimeFormatter, getCount } from '@/utils/format-table-datetime';

  const propsRef = ref(null);

  const tableData = ref([]);
  const totalRef = ref(0);
  const visible = ref(false);

  const props = defineProps({
    title: {
      type: String,
    },
    mode: {
      type: String
    }
  });

  const attrs = useAttrs();
  const emit = defineEmits(['on-close', 'on-ok', 'register']);

  const queryParams = reactive({
    page: 1,
    limit: 10,
    provider_id: '',
    style_id: '',
  });

  const formateFinishedDateTime = (row, _column, cellValue) => {
    if (row?.async_status == 'syncing') return '';
    return dateTimeFormatter(row, _column, cellValue);
  };

  const handlePageSizeChange = () => {
    queryParams.page = 1;
  };

  const fetchAsyncActionList = async () => {
    const fn = props.mode === 'template' ? getTemplateSyncActionList : getSyncActionList;
    const {
      data: { rows, total },
    } = await fn(queryParams);
    tableData.value = rows;
    totalRef.value = total;
  };

  watch(
    [
      () => queryParams.provider_id,
      () => queryParams.style_id,
      () => queryParams.page,
      () => queryParams.limit,
    ],
    () => {
      if (visible.value) {
        fetchAsyncActionList();
      }
    }
  );

  const getProps = computed(() => {
    return { ...props, ...(unref(propsRef) as any) };
  });

  const getBindValue = computed(() => {
    return {
      ...attrs,
      ...unref(getProps),
    };
  });

  async function setProps(modalProps) {
    propsRef.value = deepMerge(unref(propsRef) || ({} as any), modalProps);
  }

  function openModal(params) {
    queryParams.provider_id = params.provider_id;
    queryParams.style_id = params.style_id;
    queryParams.page = 1;
    queryParams.limit = 10;
    visible.value = true;
  }

  function onCloseModal() {
    queryParams.page = 1;
    queryParams.limit = 10;
    queryParams.provider_id = '';
    queryParams.style_id = '';
    visible.value = false;
    emit('on-close');
  }

  const modalMethods = {
    setProps,
    openModal,
    closeModal: onCloseModal,
  };
  const instance = getCurrentInstance();
  if (instance) {
    emit('register', modalMethods);
  }
</script>
