export function dataURLtoBlob(url) {
  var arr = url.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    i = bstr.length,
    u8arr = new Uint8Array(i);
  while (i--) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
}

export function printshotAvatar(renderer, width = 500, height = 500) {
  // 临时画布 在截图时对画布区域进行遮盖
  const tmpCanvas = document.createElement('canvas');
  let parentNode = document.getElementById('fu-renderkit-container') as HTMLElement;
  tmpCanvas.style.width = '50%';
  tmpCanvas.style.height = '80%';
  tmpCanvas.style.position = 'absolute';
  tmpCanvas.style.left = '0';
  tmpCanvas.style.top = '0';
  tmpCanvas.style.visibility = 'hidden'
  return new Promise<Blob>((resolve) => {
    renderer.captureImage(
      {
        width,
        height,
        tmpCanvas,
      },
      (nextSibling, done) => {
        parentNode.insertBefore(tmpCanvas, nextSibling);
        done((dataurl) => {
          console.log('url', dataurl);
          resolve(dataURLtoBlob(dataurl));
          parentNode.removeChild(tmpCanvas);
        });
      }
    );
  });
}