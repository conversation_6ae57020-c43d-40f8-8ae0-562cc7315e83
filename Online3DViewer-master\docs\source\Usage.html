<h1>Usage</h1>

There are two ways to use the library:
<ul>
    <li>Automatic initialization: Create some div elements on your site, and call a function that automatically initalizes all the viewers. This is the easiest way to use the library, but provides less flexibility.</li>
    <li>Initialization from code: Needs a bit more typing, but it's more flexible than the previous one.</li>
</ul>

<h2>Automatic initialization</h2>

<p>Create some elements anywhere on your page with the class name <code class="inline">online-3d-viewer</code>. You can create multiple elements with different parameters.</p>

<pre><code class="language-html">&lt;div class="online_3d_viewer"
    style="width: 800px; height: 600px;"
    model="model.obj, model.mtl"&gt;
&lt;/div&gt;</code></pre>

<p>You can specify several attributes:</p>
<ul>
    <li><b>model:</b> Relative path to model files separated by comma.</li>
    <li><b>camera:</b> Camera parameters (eye, center, up). 9 comma separated number values.</li>
    <li><b>defaultcolor:</b> Default color for surfaces with no material. 3 comma separated number values for r, g, b.</li>
    <li><b>backgroundcolor:</b> Canvas background color. 3 comma separated number values for r, g, b or four comma separated number values for r, g, b, a.</li>
    <li><b>edgesettings:</b> Edge display settings. 5 comma separated values for 'on'/'off', r, g, b, threshold angle.</li>
    <li><b>environmentmap:</b> Comma separated list of six images forming a texture box.</li>
    <li><b>environmentmapbg:</b> Boolean ("true" or "false") to set the environment map as background.</li>
</ul>

<p>After placing the elements, call the {@link Init3DViewerElements} function to initalize all the viewers. It must be called after the document is loaded.</p>

<pre><code class="language-js">window.addEventListener ('load', () => {
    // init all viewers on the page
    OV.Init3DViewerElements ();
});</code></pre>

<h2>Initialization from code</h2>

<p>You can also initalize the engine from code. In this case you can provide all the parameters as a JavaScript object. See the {@link EmbeddedViewer} documentation for more details.</p>

<pre><code class="language-js">window.addEventListener ('load', () => {
    // get the parent element of the viewer
    let parentDiv = document.getElementById ('viewer');

    // initialize the viewer with the parent element and some parameters
    let viewer = new OV.EmbeddedViewer (parentDiv, {
        camera : new OV.Camera (
            new OV.Coord3D (-1.5, 2.0, 3.0),
            new OV.Coord3D (0.0, 0.0, 0.0),
            new OV.Coord3D (0.0, 1.0, 0.0),
            45.0
        ),
        backgroundColor : new OV.RGBAColor (255, 255, 255, 255),
        defaultColor : new OV.RGBColor (200, 200, 200),
        edgeSettings : new OV.EdgeSettings (false, new OV.RGBColor (0, 0, 0), 1),
        environmentSettings : new OV.EnvironmentSettings (
            [
                'assets/envmaps/fishermans_bastion/posx.jpg',
                'assets/envmaps/fishermans_bastion/negx.jpg',
                'assets/envmaps/fishermans_bastion/posy.jpg',
                'assets/envmaps/fishermans_bastion/negy.jpg',
                'assets/envmaps/fishermans_bastion/posz.jpg',
                'assets/envmaps/fishermans_bastion/negz.jpg'
            ],
            false
        )
    });

    // load a model providing model urls
    viewer.LoadModelFromUrlList ([
        'model.obj',
        'model.mtl'
    ]);
});</code></pre>
