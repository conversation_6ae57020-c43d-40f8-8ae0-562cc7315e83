import { FUScene } from "./FUScene"

export class FURenderKit {
    /**
     * @brief 获取FURenderKitSDK版本号
     */
    getVersion(): string

    /**
     * @brief 初始化日志以及设置日志级别
     */
    initLogger(): void
    setLogLevel(eLvl: EFuRenderLoggerLevel): void

    /**
     * @brief 获取/销毁 内部单例实例
     */
    getInstance(): FURenderKit
    destroyInstance(): void

    /**
     * @return 内部是否授权成功, (注意)web总是成功，暂无授权过程
     */
    isAuthorized(): boolean

    /**
     * 初始化
     */
    init(engineAssetsPtr, engineAssetsSize): void

    /**
     * @param input 设置 NukSDK 的输入, 其包括 cpu 或者 gpu 的输入, 陀螺方向, 输入的用法等.
     */
    setInput(input: CInput): void

    /**
     * @brief 重新设置渲染图像的尺寸
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void

    /**
     * @brief 重置/保存/恢复NukSDK引擎状态.
     */
    resetGraphicState(): void
    saveGraphicState(): void
    restoreGraphicState(): void

    /**
     * @param name 名字
     * @return 新创建的场景
     */
    createScene(name: string): FUScene

    /**
     * @param scn 待删除的场景
     */
    destroyScene(scn: FUScene): void

    /**
     * @param name 名字
     * @return 通过名字获取创建过的场景.
     */
    getScene(name: string): FUScene;

    /**
     * @brief 渲染场景, 更新内部时间
     * @param scn 待渲染场景
     * @param elapsed_time 当前帧需要更新的时间(s)
     */
    render(scn: FUScene, elapsed_time: number): void

    /**
     * @brief 只渲染场景
     * @param scn 待渲染场景
     */
    renderWithoutUpdate(scn: FUScene): void

    /**
     * @brief 只更新内部时间
     * @param scn 待更新场景
     * @param elapsed_time 当前帧需要更新的时间(s)
     */
    updateForRender(scn: FUScene, elapsed_time: number): void

    /**
     * @brief 截取渲染全图
     * @param canvas sdk 关联的画布
     * @param done 结果回调，入参为截图 DataUrl
     * @param format 截图格式如 'image/png'
     * @param quality 截图质量 取值0~1
     */
    captureImage(canvas: HTMLCanvasElement, done, format: string, quality): void

    /**
     * @brief 设置/获取 渲染清除颜色
     */
    setRenderClearColor(color: string | [number, number, number, number]): void;
    getRenderClearColor(): string;

    /**
     * @brief 设置/获取 渲染像素格式
     */
    setRenderFormat(fmt: ERenderFormat): void
    getRenderFormat(): ERenderFormat

    /**
     * @brief 设置/获取 渲染多重采样级别，当开启多重采样抗锯齿时将降低在安卓平台的兼容性
     */
    setRenderMsaaLevel(opt: EMSAAOption): void
    getRenderMsaaLevel(): EMSAAOption

    /**
     * @brief 设置/获取 渲染阴影质量
     */
    setRenderShadowQuality(qty: EShadowQuality): void;
    getRenderShadowQuality(): EShadowQuality;

    /**
     * @brief 设置文件资源管理器. 
     * @param fu_resource_manager FURenderKit 假设: fu_resource_manager在FURenderKit的生命周期内不会被销毁.
     */
    setFUResourceManager(fu_resource_manager: FUResourceManager): void;

    /**
     * @brief 通过路径创建 FUItem.
     * @param path_utf8 路径
     */
    createItemAsync(path_utf8: string): Promise<FUItem>
}
